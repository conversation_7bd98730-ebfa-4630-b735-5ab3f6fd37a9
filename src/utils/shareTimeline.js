/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getPage, reportAnalytics } from '@base/utils/utils';
import isFunction from 'lodash/isFunction';

const getShareConfigMap = {
  query: () => {
    return {
      title: '寄快递  就用“微快递”， 全网快递  一键比价。',
    };
  },
  'order/edit': () => {
    return {
      title: '寄快递  就用“微快递”， 全网快递  一键比价。',
    };
  },
};

export function getShareTimeline() {
  const pageIns = getPage(-1);
  const { $router: { path: pageRouter = '' } = {} } = pageIns;
  const page = pageRouter.replace('/pages/', '').replace('/index', '');
  const getShareConfig = getShareConfigMap[page];
  const { report, title, query, imageUrl } = isFunction(getShareConfig)
    ? getShareConfig()
    : {
        title: '寄快递  就用“微快递”， 全网快递  一键比价。',
      };

  // 统计
  if (report) {
    reportAnalytics(report);
  }

  return {
    title,
    query,
    imageUrl,
  };
}
