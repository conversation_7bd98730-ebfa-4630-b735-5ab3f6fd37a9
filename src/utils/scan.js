/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { addEcode, getEcodeInfo } from '@/components/_pages/ecode/_utils';
import { formatAddress, orderAction } from '@/components/_pages/order/_utils';
import { switchCourierCollect } from '@/components/_pages/order/_utils/courier.detail';
import { bindActivity } from '@/components/_pages/order/_utils/order.edit';
import {
  checkCabinetOrder,
  formatMiniPostData,
  formatShopInfo,
  getKdgInfo,
  getMiniPostRelation,
  getRelationInfoByQrCode,
  getStoreInfoFromQrCode,
  getWkdStoreInfo,
  isCurrentPage,
  isUniversalCode,
  oiSendOrderQrcodeScan,
} from '@/components/_pages/store-card/_utils';
import logger from '@base/utils/logger';
import { createCompletePath, fixSubPackagesPath, removeURLParam } from '@base/utils/navigator';
import {
  checkInRouters,
  getPage,
  getStorageSync,
  isAvailableValue,
  now,
  scanParse,
  setStorage,
  trimString,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import qs from 'qs';

// 跳转页面还是resolve
const triggerJumpOrResolve = ({ path, toPath, query, suffix, ...rest }, resolve) => {
  if (path === fixSubPackagesPath(createCompletePath(toPath, suffix))) {
    resolve(query);
  } else {
    Taro.navigator({
      ...rest,
      suffix,
      url: toPath,
      options: query,
    });
  }
};

const navToTargetPage = (path, options, cb, target = 'blank') => {
  const page = getPage(-1);
  const { path: currentPath } = page.$router || {};
  if (!currentPath.includes(path)) {
    // 小程序内部只有通过首页调用scanAction才可以跳转
    // if (!allowJumpMaps.includes(currentPath)) return;
    Taro.navigator({
      url: path,
      options,
      target,
      onArrived: (currentPage) => {
        cb && cb(options, currentPage);
      },
    });
  } else {
    cb && cb(options, page);
  }
};
// 二维码链接对应
export const qrCodePathMap = {
  wechat: 'weixin.qq.com',
  query: 'f',
  card: 'c', //包含路径 c/minipost_1030
  pickup: 'g', //包含路径 g/minipost_1030
  IdCode: 'h',
  generalSend: 'd',
  station: 'cpm', //完整路径：cpm/qrc/wt_mini_55/**
  edit: 'j', //寄件下单码
  coupon: 'i', //包含minipost_1030 扫码领取优惠券
  queryScan: 'fq',
  pay: 'k', // 快递版分享二维码订单支付 yz/k/wt_mini_55
};
export const qrCodeQueryMap = {
  cky: 'instrumentSendOrder', // 出库仪特殊下单
};

export function checkUrlPathIsMatch(url, by) {
  if (!url || !by) return false;
  // 检查地址是否匹配

  const { url: parse } = scanParse(url);

  return parse && parse.includes(by);
}
export function checkQueryIsMatch(url, by) {
  if (!url || !by) return false;
  // 检查参数是否存在
  return by in scanParse(url).query;
}

//快递员扫码信息key
export const lastScanInfoKey = 'lastScanInfoKey';

// 更新缓存快递员扫码信息
export const updateScanInfo = (data) => {
  const { courier_id, customer, cmId, dak_id, dakId = dak_id || cmId } = data || {};
  if (courier_id || customer || dakId) {
    setStorage({
      key: lastScanInfoKey,
      data: data,
    });
  }
};

// 获取上次缓存快递员扫码信息
export const getScanInfo = () => {
  return getStorageSync(lastScanInfoKey);
};

// 检查扫码信息是否正确且有效，主要用于快递员现场扫码打印场景
export const checkCodeInfo = (orderData, codeInfoData) => {
  return new Promise((resolve) => {
    let codeInfo = codeInfoData;
    // 验证时效性：不超过10分钟
    if (!codeInfo) {
      const { ts, data } = getScanInfo() || {};
      console.log('ts, data', ts, data);
      if (!ts || !data) {
        return resolve(false);
      }
      codeInfo = data;
      const oneMinute = 1000 * 60;
      const cur_ts = now();
      const lastTime = ts * 1;
      const n = (cur_ts - lastTime) / oneMinute;
      console.log('时效性==>', n);
      if (n > 10) {
        return resolve(false);
      }
    }
    // 验证有效性
    console.log('验证有效性=>orderData==>', orderData);
    console.log('验证有效性=>codeInfo==>', codeInfo);
    const { customer_id, relationData = {} } = orderData || {};
    const { id: courier_id, dakId } = relationData || {};
    const code_customer_id = (codeInfo.customer && codeInfo.customer.id) || codeInfo.customer_id;
    const code_dakId = codeInfo.dakId || codeInfo.dak_id || codeInfo.cmId;
    const code_courier_id = codeInfo.courier_id;
    const res =
      customer_id || code_customer_id
        ? customer_id == code_customer_id
        : courier_id
        ? code_courier_id && courier_id == code_courier_id
        : dakId
        ? code_dakId && code_dakId == dakId
        : false;
    resolve(res);
  });
};

/**
 *
 * @description 二维码解析，解决支付宝跳转页面依然重复触发扫描事件逻辑的问题，增加isAlipayScanParsed （params全局参数问题）
 * @returns
 */
let isAlipayScanParsed = false;
export function qrCodeParse(result, page, opt = {}) {
  const { onlyParse = false } = opt || {};
  return new Promise((resolve, reject) => {
    let {
      path = '',
      params: { q, scene, qrCode },
    } = page.$router || {};
    logger.info('扫码处理', page && page.$router);
    q = isAvailableValue(result)
      ? trimString(result)
      : q || qrCode || (scene ? { scene, path } : '');
    if (!q) {
      reject(new Error('未扫描到任何内容'));
      return;
    }
    if (process.env.PLATFORM_ENV === 'alipay') {
      if (!isAvailableValue(result)) {
        if (isAlipayScanParsed) {
          reject(new Error('非扫描进入落地页'));
          return;
        }
        isAlipayScanParsed = true;
      }
    }
    let { url, query } = scanParse(q);

    console.info('url=====>', url);
    // console.info('query=====>', query);
    // console.info('onlyParse=====>', onlyParse);

    let qrCodeContent = decodeURIComponent(q);
    if (process.env.MODE_ENV === 'wkd') {
      let triggerOpts = null;
      // @微快递 二维码解析处理
      console.log('扫描二维码：', url, query);
      //兼容老版大客户
      if (url.startsWith('cpm/qrc/minipost_1001')) {
        url = 'cpm/qrc/minipost_1001';
      }
      switch (url) {
        case 'wkd/share':
        case 'share':
          if (onlyParse) {
            return resolve();
          }
          Taro.navigator({
            url: 'order/edit',
            target: 'tab',
            options: query,
          });
          break;
        case 'order/channelAdd':
          if (onlyParse) {
            return resolve();
          }
          // 快递码扫描
          getEcodeInfo(query)
            .then(({ express_code, ...rest }) => {
              const { data: address } = formatAddress(rest, 'receive', {
                reverse: true,
              });
              orderAction({
                action: 'edit',
                data: {
                  address,
                },
              });
              addEcode({ express_code }, { toastLoading: false });
            })
            .catch(reject);
          break;
        // 快递员，驿站，团队
        case 'Ws/courier':
        case 'wduser/info':
        case 'wduser/sendexpress':
        case 'wduser/scan':
        case 'courier':
        case 'vs_exp':
        case 'to':
        case 'ii': // 自定义优惠券
        case 'share': // 邀请下单码-二维码
          const {
            mb,
            cp = mb,
            phone = cp,
            id: dak_id,
            // userId, // 微商的，暂未开发
            jc: join_code,
            index_shop_id,
            uid,
            sub_uid,
            brand,
          } = query;
          console.log('query', query);
          logger.info('准备获取信息', phone || join_code || dak_id);
          if (uid || sub_uid) {
            // 绑定邀请下单码
            console.log('绑定邀请下单码');
            bindActivity(uid || sub_uid);
          }
          getWkdStoreInfo({ phone, join_code, dak_id, index_shop_id, brand })
            .then((res) => {
              console.log('已获取信息', res);
              logger.info('已获取信息', !!res);
              if (!res) return;
              updateScanInfo(res);
              if (onlyParse) {
                return resolve(res);
              }
              Taro.expSource = 'scan';
              const { code, ...relation } = res;
              let extraData;
              if (url == 'ii') {
                // 扫码赠券
                extraData = {
                  ...query,
                  type: 'scanCoupon',
                };
              }
              const teamMiss = code == 2911;
              if (!teamMiss) {
                relation.storageWay = 'scan';
                orderAction({
                  action: 'edit',
                  data: {
                    relation,
                    extraData,
                  },
                });
              } else {
                Taro.kbModal({
                  content: '该团队已解散！可点击【切换】其他下单对象。',
                  confirmText: '切换',
                  onConfirm: () => {
                    Taro.navigator({
                      url: 'order/relation',
                      options: {
                        type: 'courier',
                      },
                    });
                  },
                });
              }
              // 收藏快递员、驿站、团队
              if (brand) return;
              switchCourierCollect({
                account_phone: phone,
                join_code,
                dak_id,
                is_focused: teamMiss, // 团队已解散取消收藏；
              }).then((data) => {
                // 收藏优惠券
                if (url !== 'ii' && data && data.cost) {
                  if (process.env.PLATFORM_ENV === 'swan') return;
                  orderAction({
                    action: 'edit',
                    data: {
                      extraData: {
                        ...data,
                        type: 'focusCoupon',
                      },
                    },
                  });
                }
              });
            })
            .catch(reject);
          break;
        // 查件
        case 'scanSign/wxapp':
          if (onlyParse) {
            return resolve();
          }
          const { channel, waybill_no: waybill } = query;
          if (waybill) {
            Taro.navigator({
              url: 'query/detail',
              options: {
                waybill,
              },
            });
          } else {
            switch (channel) {
              case 'wsCheck':
                // 微商查件码
                Taro.navigator({
                  url: 'ws/query',
                  options: query,
                });
                break;
              case 'minpost':
                // 小邮筒查件
                break;

              default:
                if (channel !== 'jump') {
                  Taro.navigator({
                    url: 'query',
                    target: 'tab',
                    onArrived: () => {},
                  });
                }
                break;
            }
          }

          break;
        case 'Dak/device':
          // 快递柜
          triggerOpts = {
            toPath: 'kdg',
            suffix: 'default',
          };
          break;
        case 'v9/vhome/commodities/ShareIndex/store':
          // 驿站详情页
          const { uid: shop_id, ...storeRest } = query;
          triggerOpts = {
            toPath: 'ws/detail',
            query: { shop_id, ...storeRest },
          };
          break;
        case 'v7/share/CreateOrder/Show':
          const { type } = query || {};
          // 微商寄件
          if (type == 'create_order') {
            triggerOpts = { toPath: 'ws/order/edit' };
          } else {
            // 微商查件
            triggerOpts = { toPath: 'ws/query' };
          }
          break;
        case 'f/query/point':
          // 快递员查件
          query.channel = 'kdy';
          triggerOpts = { toPath: 'ws/query' };
          break;
        case 'v9/vhome/commodities/ShareIndex/delivery':
          break;
        case 'WsOrder/excellent':
          triggerOpts = { toPath: 'ws/order/edit-yhj' };
          break;
        case 'WsOrder/payPoints':
          // 优惠寄绑定支付分;
          triggerOpts = { toPath: 'order/credit-pay' };
          break;
        case 'WsOrder/tickets':
          break;
        case 'scanSign/dak':
          // 预约取件
          triggerOpts = {
            toPath: 'query/appointment',
            query: {
              dakId: query.id,
            },
          };
          break;
        case 'wduser/equity':
          // 权益次卡
          triggerOpts = {
            toPath: 'order/card',
          };
          break;
        // 扫码寄
        case 's':
        case 'dd':
          // 扫码寄-寄件二维码
          page.dealRelationLoading && page.dealRelationLoading(true);
          getStoreInfoFromQrCode(qrCodeContent, { type: 'send' })
            .then((data) => {
              console.log('扫码寄-寄件数据', data);
              page.dealRelationLoading && page.dealRelationLoading(false);
              updateScanInfo(data);
              if (onlyParse) {
                return resolve(data);
              }
              if (data && (data.account_phone || data.dak_id)) {
                orderAction({
                  action: 'edit',
                  data: {
                    relation: data,
                  },
                });
                if (data.account_phone) {
                  // 收藏快递员、大客户
                  switchCourierCollect({
                    account_phone: data.account_phone,
                    customer_id: data.customer ? data.customer.id : '',
                    is_focused: false,
                  });
                }
              }
            })
            .catch(() => {
              page.dealRelationLoading && page.dealRelationLoading(false);
            });
          break;
        case 'q':
        case 'ff':
          // 扫码寄 - 查件
          getStoreInfoFromQrCode(qrCodeContent, { type: 'find' }).then((options) => {
            console.log('扫码寄-查件数据', options);
            resolve(options);
          });
          break;
        // 添加大客户下单关系
        case 'cpm/qrc/minipost_1001':
        case 'courier_customer':
          getMiniPostRelation({ text: qrCodeContent }).then((data) => {
            console.log('getMiniPostRelation.data', data);
            let miniPostRelation = formatMiniPostData(data);
            console.log('miniPostRelation', miniPostRelation);
            updateScanInfo(miniPostRelation);
            if (onlyParse) {
              return resolve(miniPostRelation);
            }
            const { type, originData } = miniPostRelation || {};
            if (type == 'minpost_customer' || type == 'cloud_print') {
              orderAction({
                action: 'edit',
                data: {
                  extraData: {
                    ...originData,
                    qrCodeContent,
                    printerType: type,
                    type: 'miniPostPrinter',
                  },
                },
              });
              return;
            } else if (type == 'courier_customer') {
              miniPostRelation.type = 'courier';
              miniPostRelation.storageWay = 'scan';
            } else if (type == 'dak_customer') {
              miniPostRelation.type = 'dak';
            }
            orderAction({
              action: 'edit',
              data: {
                relation: miniPostRelation,
              },
            });
          });
          break;
        case 'djj':
          console.info('扫码进入======>>>>> djj');
          if (onlyParse) {
            return resolve();
          }
          orderAction({
            action: 'edit',
            data: {
              toDjjPage: true,
              relation: {
                type: 'djj',
                brand: 'dp',
              },
            },
          });
          break;
        default:
          break;
      }
      if (onlyParse) {
        return resolve();
      }
      triggerOpts && triggerJumpOrResolve({ path, query, ...triggerOpts }, resolve);
    } else {
      let triggerOpts = null;
      let triggerCallback = resolve;
      if (url == 'order' && onlyParse) {
        resolve(query);
        return;
      }
      if (url.includes('f/query/point/yz')) {
        url = 'fq';
      }
      if (url.includes('yz/kdg')) {
        url = 'kdg';
      } else if (url == 'kdg') {
        url = 'kdg-storage';
      } else if (url.includes('QrcodePayNew')) {
        url = 'pay-cabinet';
      } else {
        let aUrl = url.split('/');
        let url1 = aUrl[0];
        let url2 = aUrl[1];
        url = url1;
        // 兼容新码规则-start，类似 https://kbydy.cn/yz/d?i=35379554&k=2ae100f9
        const isFitQrCodeMap = (fitUrl, map) => {
          let fitIndex = -1;
          if (fitUrl && map) {
            fitIndex = Object.keys(map).findIndex((item) => fitUrl.includes(map[item]));
          }
          return fitIndex > -1;
        };
        if (url1 === 'yz') {
          if (isFitQrCodeMap(url2, qrCodePathMap)) {
            url = url2;
            qrCodeContent = qrCodeContent.replace('/yz', '');
          } else {
            url = aUrl.join('/');
          }
        }
        // 兼容新码规则-end
        if (`/${url}`.includes(`/${qrCodePathMap.generalSend}`)) {
          url = 'sendCard';
        } else if (url.includes(qrCodePathMap.wechat)) {
          qrCodeContent = url;
          url = 'sendCard';
        }
      }
      const { scene: entryScene } = Taro.getLaunchOptionsSync() || {};
      if (qrCodeQueryMap.cky in query) {
        // 特殊下单对象：出库仪二维码
        oiSendOrderQrcodeScan(qrCodeContent, 'global');
        return resolve();
      }
      switch (url) {
        case 'index':
          resolve({
            url,
            ...query,
          });
          break;
        case qrCodePathMap.queryScan:
          if (onlyParse) {
            return resolve();
          }
          query.channel = 'yz';
          triggerOpts = {
            toPath: 'ws/query',
          };
          break;
        case qrCodePathMap.query: // 扫码寄 - 查件
          if (onlyParse) {
            return resolve();
          }
          getStoreInfoFromQrCode(qrCodeContent, {
            type: 'find',
          }).then((options) => {
            resolve(options);
          });
          break;
        case 'sendCard': // 扫描：驿站微信定制公众号 或 扫码寄-寄件二维码
          getStoreInfoFromQrCode(qrCodeContent, { type: 'send' }).then((data) => {
            updateScanInfo(data);
            if (onlyParse) {
              return resolve(data);
            }
            const navToEdit = (params) => {
              const routerUrl = 'order/edit';
              console.log('params', params);
              params && Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(params);
              if (isCurrentPage(routerUrl)) return;
              Taro.navigator({
                url: 'order/edit',
                target: 'tab',
              });
            };
            if (isUniversalCode(data)) {
              navToEdit({ qrCodeContent });
            } else {
              navToEdit({ ...data });
            }
          });
          resolve();
          break;
        case qrCodePathMap.edit:
          getRelationInfoByQrCode(qrCodeContent, true).then((dakInfo) => {
            const { cmId: dakId, courierId } = dakInfo;
            updateScanInfo(dakInfo);
            if (onlyParse) {
              return resolve(dakInfo);
            }
            let params = {};
            if (dakId) {
              params.dakId = dakId;
            } else if (courierId) {
              params.courierId = courierId;
            }
            navToTargetPage(
              '/pages/order/edit/index',
              params,
              () => {
                Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(params);
              },
              'tab',
            );
          });
          if (!onlyParse) {
            resolve(qrCodeContent);
          }
          break;
        case qrCodePathMap.card:
          if (onlyParse) {
            return resolve();
          }
          const { dakId, cardId, courierId, ...reset } = query;
          let data = {
            ...reset,
            action: 'buy',
            dak_id: dakId,
            card_id: cardId,
            courier_id: courierId,
          };
          resolve(data);
          navToTargetPage('/pages/order/card/index', data, () => {
            getRelationInfoByQrCode(qrCodeContent, true).then((dakInfo) => {
              const { cmId: dak_id = dakId, courierId: courier_id = courierId } = dakInfo;
              let params = {};
              if (dak_id) {
                params.dakId = dak_id;
              } else if (courier_id) {
                params.courier_id = courier_id;
              }
              Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(params);
            });
          });

          break;
        case qrCodePathMap.IdCode:
          if (onlyParse) {
            return resolve();
          }
          navToTargetPage('/pages/IDcode/index', { ...query, action: 'turnstiles' }, () => {
            resolve(query);
          });
          break;
        case qrCodePathMap.pickup:
          if (onlyParse) {
            return resolve(query);
          }
          if (query.qrcode_expired && entryScene != 1011) {
            setTimeout(() => {
              Taro.kbToast({
                text: '请扫描大屏预约取件码',
              });
            }, 500);
            delete query.qrcode_expired;
          }
          if (entryScene == 1011) {
            if (process.env.PLATFORM_ENV === 'weapp') {
              let time = Math.floor(new Date().getTime() / 1000);
              query.qrcode_expired = time;
            }
          }
          resolve(query);
          navToTargetPage(
            '/pages/query/appointment/index',
            {
              q,
            },
            () => {
              getStoreInfoFromQrCode(
                query.dakId ? query : removeURLParam(decodeURIComponent(q), 'qrcode_expired'),
              ).then((res) => {
                Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo({});
              });
            },
          );
          break;
        case qrCodePathMap.station:
          getRelationInfoByQrCode(qrCodeContent, true).then((dakInfo) => {
            const { cmId, courierId: courier_id } = dakInfo;
            updateScanInfo(dakInfo);
            if (onlyParse) {
              return resolve(dakInfo);
            }
            let params = {};
            if (cmId) {
              params.dakId = cmId;
            } else if (courier_id) {
              params.courierId = courier_id;
            }
            navToTargetPage('/pages/order/edit/index', params, () => {
              Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(params);
            });
            resolve(params);
          });
          break;
        case qrCodePathMap.coupon:
          if (onlyParse) {
            return resolve();
          }
          Taro.navigator({
            url: 'order/edit',
            target: 'tab',
            post: {
              type: 'scanCoupon',
              data: query,
            },
          });
          resolve(query);
          break;
        case qrCodePathMap.pay:
          resolve(query);
          break;
        case 'vs_exp':
          resolve({});
          break;
        case 'vs_exp_e':
          resolve({});
          break;
        case 'kdg':
          if (onlyParse) {
            return resolve();
          }
          getKdgInfo(query, true).then(async ({ relation_info }) => {
            const { cabinetToken } = query;
            relation_info.isKdg = true;
            relation_info.isKdg_tmp = cabinetToken.includes('cabinetTmp_');
            relation_info.dynamicForms = {
              sendWay: {
                isShow: false,
              },
            };
            let params = formatShopInfo(relation_info);
            // 20231122 全局保留快递柜状态
            resolve({ isKdg: true });
            navToTargetPage(
              '/pages/order/edit/index',
              params,
              async () => {
                Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(params);
                await checkCabinetOrder(relation_info);
              },
              'tab',
            );
          });
          break;
        case 'kdg-storage':
          resolve(query);
          break;
        case 'pay-cabinet':
          navToTargetPage('pickup/pay-cabinet', query);
          resolve(query);
          break;
        default:
          if (onlyParse) {
            return resolve();
          }
          // 非小程序内扫描，跳过
          if (result) {
            // 小程序内扫描器才弹出提示
            Taro.kbToast({
              text: '请扫描指定二维码',
            });
          }
          reject(new Error('未配置的处理项：' + decodeURIComponent(q)));
          break;
      }
      triggerOpts && triggerJumpOrResolve({ path, query, ...triggerOpts }, triggerCallback);
    }
  });
}

export const scanAction = (opts, page = getPage(-1)) => {
  const { result, scanType = 'QR_CODE' } = opts || {};
  console.log('opts=======>', opts);
  if (scanType === 'QR_CODE') {
    return qrCodeParse(result, page, opts);
  }
  return new Promise((resolve) => {
    if (scanType === 'WX_CODE') {
      // 小程序码
      Taro.navigator({
        url: `/${result}`,
      });
    } else {
      // 条码，跳转搜索页
      const { path } = page.$router || {};
      const vipsPath = ['query/index', 'query/match'];
      if (!checkInRouters(path, vipsPath)) {
        Taro.navigator({
          url: `query/match?${qs.stringify({
            word: result,
          })}`,
        });
      } else {
        resolve(result);
      }
    }
  });
};

// 扫码
export const scanCode = (options = {}) => {
  return new Promise((resolve, reject) => {
    const {
      onlyFromCamera = false,
      scanType = ['barCode', 'qrCode', 'datamatrix', 'pdf417'],
      mode,
    } = options;
    const data =
      process.env.PLATFORM_ENV === 'weapp'
        ? {
            onlyFromCamera,
            scanType,
          }
        : {
            hideAlbum: onlyFromCamera,
            type: scanType.map((item) => item.replace('Code', '')),
          };
    Taro.kbToast({
      status: 'loading',
      sleepOn: true,
      text: '解析中',
    });
    Taro.scanCode({
      ...data,
    })
      .then((res) => {
        let copyRes = res;
        if (process.env.PLATFORM_ENV === 'swan') {
          const { scanType } = res;
          res.scanType = scanType === 'qrCode' || scanType === 'QR_CODE' ? 'QR_CODE' : 'BAR_CODE';
          copyRes = res;
        }
        if (process.env.PLATFORM_ENV === 'alipay') {
          const { code, qrCode } = res;
          copyRes = {
            result: code,
            scanType: qrCode ? 'QR_CODE' : 'BAR_CODE',
          };
        }
        let { result, scanType: type, path } = copyRes;

        if (type === 'WX_CODE' && path) {
          result = path;
        }
        console.log('mode', mode);
        Taro.kbToast({
          isOpened: false,
        });
        if (!result) {
          reject(new Error('未扫描到内容'));
        } else if (mode === 'global') {
          console.log('result', result);
          scanAction({ result, scanType: type }).then(resolve).catch(reject);
        } else {
          resolve(result);
        }
      })
      .catch(() => {
        Taro.kbToast({
          isOpened: false,
        });
        reject();
      });
  });
};

/**
 *
 * @description 合并扫码携带的参数
 * @param {*} $router
 * @returns
 */
export function mergeParamsByScan($router) {
  const { params } = $router || {};
  const { query } = scanParse(params);
  $router.params = {
    ...params,
    ...query,
  };
  return $router;
}
