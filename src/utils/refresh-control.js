/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';

Taro.kbRefreshControlMap = {};

/**
 *
 * @description 获取或设置刷新控制
 * @param {*} key
 * @param {*} action  record-记录 | check-检查
 * @returns
 */
export function refreshControl(key = REFRESH_KEY, action = 'record') {
  if (action === 'record') {
    Taro.kbRefreshControlMap[key] = {};
    return true;
  }

  const isAllowRefresh = !!Taro.kbRefreshControlMap[key];
  if (isAllowRefresh) {
    Taro.kbRefreshControlMap[key] = null;
  }
  return isAllowRefresh;
}

export const REFRESH_KEY = 'REFRESH_KEY'; // 通用key
export const REFRESH_KEY_QUERY = 'REFRESH_KEY_QUERY'; // 查件列表
export const REFRESH_KEY_QUERY_DETAIL = 'REFRESH_KEY_QUERY_DETAIL'; // 查件详情
export const REFRESH_KEY_ORDER = 'REFRESH_KEY_ORDER'; // 订单列表
export const REFRESH_KEY_ADDRESS = 'REFRESH_KEY_ADDRESS'; // 地址列表
export const REFRESH_KEY_COURIER = 'REFRESH_KEY_COURIER'; // 快递员列表
export const REFRESH_KEY_CREDIT = 'REFRESH_KEY_CREDIT'; // 检查支付分是否开通
export const REFRESH_KEY_CREDIT_CLICK = 'REFRESH_KEY_CREDIT_CLICK'; // 点击开通支付分

export const REFRESH_KEY_CABINET = 'REFRESH_KEY_CABINET'; // 柜子列表

export const REFRESH_KEY_ORDER_DELIVERY = 'REFRESH_KEY_ORDER_DELIVERY'; // 跑腿订单列表

export const REFRESH_KEY_APPOINTMENT_LIST = 'REFRESH_KEY_APPOINTMENT_LIST'; // 预约列表
