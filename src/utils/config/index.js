/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import diffConfig from './diff';

const { app_uid, auth } = diffConfig;

/**
 * 胶囊配置
 */
export const capsuleBounding = {
  android: {
    top: 8,
    right: 10,
    width: 95,
    height: 32,
  },
  ios: {
    top: 4,
    right: 7,
    width: 87,
    height: 32,
  },
};

// 特殊页面
export const loginPage = 'user/login';
export const mobilePage = 'user/relation/edit';
export const homePage = 'query';

export const apiPrefixPath =
  process.env.MODE_ENV === 'third.post' ||
  process.env.MODE_ENV === 'third' ||
  process.env.MODE_ENV === 'yz' ||
  process.env.MODE_ENV === 'third.pro'
    ? '/g_mp'
    : '';

// 接口请求配置，权限，域名
export const apiOptions = {
  auth,
  info:
    process.env.MODE_ENV === 'wkd'
      ? {
          version: process.env.APP_VERSION,
        }
      : {
          app_uid,
          app_version: process.env.APP_VERSION,
        },
  domain: `https://api.kuaidihelp.com${apiPrefixPath}`,
};

// 登录信息本地缓存数据的key
export const loginStorageKey = 'loginData';

// 环境对应中文
export const envNames = {
  weapp: '微信',
  alipay: '支付宝',
  swan: '百度',
};

export const PLATFORM_NAME = envNames[process.env.PLATFORM_ENV] || '微信';

// 下单关系本地缓存key
export const relationStorageKey = 'vshopSendCourier';

export { app_uid };
