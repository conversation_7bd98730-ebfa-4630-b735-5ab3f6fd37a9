/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { apiOptions } from '@/utils/config';

export function getIdsMap() {
  const {
    info: { app_uid },
  } = apiOptions;
  const doc = {
    0: {
      name: '服务协议',
      url: `/UserAgreement/view?agreementType=serviceAgreement&platform=${
        process.env.MODE_ENV === 'wkd' ? 'mina' : `minipost_${app_uid}`
      }`,
    },
    1: {
      name: '绑定手机号使用协议',
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/help/wkds_phone_pro'
          : `/UserAgreement/view?agreementType=bindMobileAgreement&platform=minipost_${app_uid}`,
    },
    2: {
      name: '实名常见问题',
      url: '/help/real_name_exp',
    },
    4: {
      name: '关联手机号',
      url: '/help/wkds_relation',
    },
    5: {
      name: '包裹通知',
      url:
        process.env.MODE_ENV === 'wkd'
          ? 'https://mp.weixin.qq.com/s/9T6qZAxpB-xB86He60SXNg'
          : 'https://mp.weixin.qq.com/s/n-FDyzu9RCJpWnIY-zuR1g',
    },
    6: {
      name: '联系客服',
      url: 'https://mp.weixin.qq.com/s/knS9QqVBSW6qGJQw6ssQRA',
    },
    7: {
      name: 'excel打单说明',
      url: 'https://upload.kuaidihelp.com/admin/floorPageFile/1586230677.html',
    },
    8: {
      name: '关联小程序',
      url: 'https://upload.kuaidihelp.com/admin/floorPageFile/1586230677.html',
    },
    9: {
      name: '申请加盟',
      path: 'pages/introduce/introduce,wx1609eede4db3f582',
    },
    10: {
      name: '免责声明',
      url: 'https://m.kuaidihelp.com/Pay/declare',
    },
    11: {
      name: '预约取件',
      url:
        process.env.MODE_ENV === 'wkd'
          ? 'https://mp.weixin.qq.com/s/fogbF8PHWm1veLo6-H11lw'
          : 'https://mp.weixin.qq.com/s/Vp1uPAcSNy_XDuxW1hf4pA',
    },
    12: {
      name: '物流详情',
      url:
        process.env.MODE_ENV === 'wkd'
          ? 'https://mp.weixin.qq.com/s/fogbF8PHWm1veLo6-H11lw'
          : 'https://mp.weixin.qq.com/s/AYnWB36Gm-grH1gPyypq4Q',
    },
    13: {
      name: '取件',
      url:
        process.env.MODE_ENV === 'wkd'
          ? 'https://mp.weixin.qq.com/s/fogbF8PHWm1veLo6-H11lw'
          : 'https://mp.weixin.qq.com/s/T6gtoxPNSwIi3wvYnwhtxQ',
    },
    14: {
      name: '顺丰服务协议',
      url: 'https://m.kuaidihelp.com/help/sf_pro',
    },
    15: {
      name: '天天抽',
      url: 'https://engine.sshhli.com/index/activity?appKey=44wJFS9H6FpT66DWXca5raJb9BK8&adslotId=394462',
    },
    16: {
      name: '京东服务协议',
      url: 'https://m.kuaidihelp.com/help/jd_pro',
    },
    17: {
      name: '达达保价协议',
      url: 'https://m.kuaidihelp.com/help/dada_price',
    },
    ecode: {
      name: '快递码使用帮助',
      url: 'https://m.kuaidihelp.com/wkd/ExpressCode/index',
    },
    disclaimer: {
      name: '微快递免责声明',
      url: 'https://m.kuaidihelp.com/Follow/disclaimer',
    },
    stoService: {
      name: '申通在线客服',
      url: 'https://95543.qiyukf.com/client?k=51551590dbef83c8b969e4726877a5d1&wp=1&robotShuntSwitch=1&robotId=75059&t=XXXX',
    },
    agreement: {
      name: '隐私协议',
      url: `https://m.kuaidihelp.com/help/agreementPrivacy?type=${process.env.MODE_ENV}&mini=1`,
    },
    yjvip: {
      name: '优享寄卡服务协议',
      url: 'https://m.kuaidihelp.com/help/yjvip.html',
    },
    price_rule: {
      name: '计价规则',
      url: 'https://m.kuaidihelp.com/help/price_rule.html',
    },
    price_desc: {
      name: '价格说明',
      url: 'https://m.kuaidihelp.com/help/price_desc.html',
    },
    vip_guide: {
      name: '操作示意图',
      url: 'https://m.kuaidihelp.com/help/vip_guide.html',
    },
    ai_tips: {
      name: '智能识别',
      url: 'https://m.kuaidihelp.com/help/ai_tips.html',
    },
    wxqun: {
      name: '智能识别',
      url: 'https://m.kuaidihelp.com/help/wxqun.html',
    },
    addrCode_help: {
      name: '使用说明',
      url: 'https://m.kuaidihelp.com/f/yz/addrCode',
    },
  };
  if (process.env.MODE_ENV === 'wkd') {
    doc['money-method'] = {
      name: '赚钱小攻略',
      url: 'https://m.kuaidihelp.com/help/dp_help',
    };
  }
  return doc;
}

export function getDocumentById(id = 0) {
  return getIdsMap()[`${id}`];
}
