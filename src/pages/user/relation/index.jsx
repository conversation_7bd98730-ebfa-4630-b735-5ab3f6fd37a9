/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import apis from '@/utils/apis';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import KbModal from '@base/components/modal';
import KbListBox from '@/components/_pages/order/listbox';
import KbLoginAuth from '@base/components/login/auth';
import request from '@base/utils/request';
import { importFieldHide, reportAnalytics } from '@base/utils/utils';
import { Button, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import qs from 'qs';
import { AtActionSheet, AtActionSheetItem, AtButton, AtIcon, AtInput } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '关联手机号',
  };

  constructor() {
    this.listData = {
      api: {
        url: apis['mobile.list'],
        formatResponse: ({ data }) => {
          let list = null,
            binderList = null;
          if (process.env.MODE_ENV === 'wkd') {
            list = data;
            this.getBindMyRelateMobileList();
          } else {
            const { relation, binder, ...rest } = data;
            list = relation;
            binderList = binder;
            if (rest.mobile) {
              // 缓存个人信息
              Taro.userMobileData = rest;
            }
          }
          const hasList = isArray(list) && list.length > 0;

          return {
            code: 0,
            data: {
              list: hasList ? list : [],
              binderList: binderList && binderList.length ? binderList : [],
            },
          };
        },
        onThen: (_, res) => {
          const { data: { list, binderList } = {} } = res || {};
          this.setState({
            list,
            binderList,
          });
        },
      },
    };
    this.other = '自定义';
    this.state = {
      list: [],
      binderList: [],
      tags: [
        { value: '爸爸', label: '爸爸' },
        { value: '妈妈', label: '妈妈' },
        { value: '老公', label: '老公' },
        { value: '老婆', label: '老婆' },
        { value: '儿子', label: '儿子' },
        { value: '女儿', label: '女儿' },
        { value: '小号', label: '小号' },
        { value: '舅舅', label: '舅舅' },
        { value: '自定义', label: this.other },
      ],
      isOpened: false,
      isOpenedOther: false,
      isOpenedPhone: false,
      otherValue: '',
    };
  }

  // 列表准备就绪
  onReady = (ins) => {
    this.listIns = ins;
  };

  // 监听
  onPostMessage = (type, data = {}) => {
    switch (type) {
      case 'updateMobileLabel': // 更新标签
        this.listIns.loader();
        break;
      case 'updateMobile': // 更新手机号
        this.listIns.loader();
        data.init = true;
        this.handleTagsModal('open', data);
        break;
      case 'removeMobile': // 移除手机号
        this.listIns.loader();
        break;
      default:
        break;
    }
  };

  // 获取谁关联我
  getBindMyRelateMobileList() {
    request({
      url: '/g_wkd/v2/Account/getBindMyRelateMobileList',
      toastLoading: false,
      onThen: (res) => {
        if (res.code == 0 && res.data && res.data.length > 0) {
          this.setState({
            binderList: res.data,
          });
        }
      },
    });
  }

  // 解除绑定
  handleUnbind = () => {
    Taro.kbModal({
      title: '温馨提示',
      top: false,
      rootClass: 'kb-relation-unBindModal',
      content: [
        {
          className: 'kb-relation-unBindModal--content',
          text: '确定解除关联? 解除关联后，将无法继续查看该手机号码相关的订单信息及物流变更通知',
        },
      ],
      cancelText: '取消',
      confirmText: '确定',
      onConfirm: () => {
        // const { id } = Taro.userMobileData || {};
        request({
          url: apis['mobile.unbind'],
          // data: {
          //   id,
          // },
          toastError: true,
          onThen: (res) => {
            if (res.code == 0) {
              Taro.kbUpdateUserInfo({
                mobile: '',
              });
              Taro.userMobileData = {
                ...(Taro.userMobileData || {}),
                mobile: '',
                id: '',
              };
            }
          },
        });
      },
    });
  };

  // 绑定成功
  handleAuthComplete = () => {
    const { params: { source } = {} } = this.$router;
    const reportKey = 'bind_mobile_phone';
    if (/^bd-/.test(source)) {
      reportAnalytics({
        key: reportKey,
        source,
      });
    }
  };

  // 解除关联手机号 - 谁关联我
  handleRelieve = (item) => {
    const { mobile, id } = item;
    Taro.kbModal({
      title: '确定解除关联',
      top: false,
      rootClass: 'kb-relation-unBindModal',
      content: [
        {
          className: 'kb-relation-unBindModal--content',
          text: `解除绑定后，${importFieldHide(mobile, 3, 7)}用户将不再为您代取包裹`,
        },
      ],
      cancelText: '取消',
      confirmText: '确定',
      onConfirm: () => {
        id &&
          request({
            url: apis['mobile.removeSelf'],
            data: { id },
            toastError: true,
            onThen: (res) => {
              if (res.code === 0) {
                this.listIns.loader();
              }
            },
          });
      },
    });
  };

  // 解除关联
  onRemove = (item) => {
    Taro.kbModal({
      title: '确认解除关联？',
      content: '解除关联后，将无法继续查看该手机号码对应的包裹。',
      onConfirm: () => {
        const { id } = item;
        request({
          url: apis['mobile.remove'],
          data: {
            id,
          },
          toastError: true,
          toastSuccess: '已解除',
          quickTriggerThen: true,
          onThen: ({ code }) => {
            if (code == 0) {
              this.listIns.loader();
            }
          },
        });
      },
    });
  };

  // 修改标签
  onModifyTag(item) {
    const { id, remark } = item;
    request({
      url: apis['mobile.modify.relation'],
      data: {
        id,
        mobile_type: '1',
        remark: remark == this.other ? '' : remark,
      },
      toastError: true,
      toastSuccess: '修改成功',
      quickTriggerThen: true,
      onThen: ({ code }) => {
        if (code == 0) {
          this.handleTagsModal('close');
          this.handleTagsModal('closeOther');
          this.listIns.loader();
        }
      },
    });
  }

  // 管理编辑关联号码
  onEdit = (key, data) => {
    let url = '';
    switch (key) {
      case 'bind':
        // 绑定或修改手机号
        url = `user/relation/edit?${qs.stringify({
          action: data ? 'modify' : 'bind',
          type: 'self',
        })}`;
        break;
      case 'edit':
      // 编辑关联手机号
      case 'add':
        // 添加关联手机号
        url = `user/relation/edit?${qs.stringify({
          type: 'family',
          ...data,
        })}`;
        break;
      default:
        break;
    }
    Taro.navigator({
      url,
    });
  };

  handleClickBar(key, item) {
    if (key === 'relation') {
      Taro.kbActionSheet({
        items: ['修改标签', '解除关联'],
        onClick: (index) => {
          if (index == 0) {
            this.handleTagsModal('open', item);
          } else if (index == 1) {
            this.onRemove(item);
          }
        },
      });
    } else if (key === 'bind') {
      this.handleBindPhone('open');
    }
  }

  handleBindPhone = (key) => {
    switch (key) {
      case 'open':
        this.setState({
          isOpenedPhone: true,
        });
        break;
      case 'close':
        this.setState({
          isOpenedPhone: false,
        });
        break;
    }
  };

  handleTagsModal(key, data) {
    const { isOpened, otherValue } = this.state;
    switch (key) {
      case 'close':
        this.setState({
          isOpened: false,
        });
        break;
      case 'open':
        this.setState({
          isOpened: data,
        });
        break;
      case 'closeOther':
        this.setState({
          isOpenedOther: false,
        });
        break;
      case 'openOther':
        this.setState({
          isOpenedOther: true,
        });
        break;
      case 'select':
        if (data.value === this.other) {
          this.handleTagsModal('openOther');
          return;
        }
        this.onModifyTag({ id: isOpened && isOpened.id, remark: data.value });
        break;
      case 'other_confirm':
        if (!otherValue) {
          Taro.kbToast({
            text: '请输入自定义标签',
          });
          return;
        }
        this.handleTagsModal('select', { value: otherValue });
        break;
      case 'other_input':
        this.setState({
          otherValue: data,
        });
        break;
    }
  }

  render() {
    const {
      list,
      binderList,
      tags,
      isOpened,
      selectted,
      isOpenedOther,
      otherValue,
      isOpenedPhone,
      ...rest
    } = this.state;
    const { loginData: { userInfo: { mobile } = {} } = {} } = this.props;
    const { params: { source } = {} } = this.$router;
    const extraData = {};
    if (/^bd-/.test(source)) {
      extraData.bd_source = source;
    }
    return (
      <KbPage
        {...rest}
        renderFooter={
          <View className='kb-relation-footer'>
            <AtButton type='primary' circle onClick={this.onEdit.bind(this, 'add', null)}>
              关联亲友手机代取包裹
            </AtButton>
          </View>
        }
      >
        <KbLongList data={this.listData} onReady={this.onReady} enableRefresh={false}>
          <View className='kb-relation'>
            <View
              className='kb-relation-nav'
              onClick={this.handleClickBar.bind(this, 'bind')}
              hoverClass='kb-hover'
            >
              <View className='kb-relation-nav__title'>我的手机号</View>
              <View className='kb-relation-nav__content'>
                <View className='kb-margin-sm-r'>
                  {mobile ? importFieldHide(mobile, 3, 7) : '暂未绑定'}
                </View>
                <View className='icon'>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-color__grey kb-icon-size__base'
                  />
                </View>
              </View>
            </View>
            {list && list.length > 0 ? (
              <Fragment>
                <View className='kb-color__greyer kb-size__base2 kb-margin-md-b'>关联手机号</View>
                {list.map((item, index) => {
                  return (
                    <View
                      className='kb-relation-nav'
                      key={item.mobile}
                      onClick={this.handleClickBar.bind(this, 'relation', item)}
                      hoverClass='kb-hover'
                    >
                      <View className='kb-relation-nav__title'>
                        {item.tag || `亲友${index + 1}`}
                      </View>
                      <View className='kb-relation-nav__content'>
                        <View className='kb-margin-sm-r'>{importFieldHide(item.mobile, 3, 7)}</View>
                        <View className='icon'>
                          <AtIcon
                            prefixClass='kb-icon'
                            value='arrow'
                            className='kb-color__grey kb-icon-size__base'
                          />
                        </View>
                      </View>
                    </View>
                  );
                })}
              </Fragment>
            ) : null}
            {binderList && binderList.length > 0 ? (
              <Fragment>
                <View className='kb-color__greyer kb-size__base2 kb-margin-md-b'>谁关联我</View>
                {binderList.map((item) => {
                  return (
                    <View className='kb-relation-nav' key={item.mobile}>
                      <View className='kb-relation-nav__title'>
                        {importFieldHide(item.mobile, 3, 7)}
                      </View>
                      <View className='kb-relation-nav__content'>
                        <AtButton
                          onClick={this.handleRelieve.bind(this, item)}
                          className='kb-button__link'
                        >
                          解除关系
                        </AtButton>
                      </View>
                    </View>
                  );
                })}
              </Fragment>
            ) : null}
          </View>
        </KbLongList>
        <KbModal
          title='温馨提示'
          top={false}
          isOpened={isOpened}
          className='kb-relation-tags'
          cancelText={isOpened.init ? '跳过' : '取消'}
          cancelButtonProps={{
            className: 'kb-button__link kb-color__black',
          }}
          confirmText=''
          onCancel={this.handleTagsModal.bind(this, 'close')}
          onClose={this.handleTagsModal.bind(this, 'close')}
        >
          <View>
            <View className='kb-size__base'>可点选为此亲友添加标签(选填)</View>
            <View className='kb-relation-tags__box'>
              <KbListBox list={tags} onChange={this.handleTagsModal.bind(this, 'select')} />
            </View>
          </View>
        </KbModal>
        <KbModal
          isOpened={isOpenedOther}
          top={false}
          title='自定义亲友标签'
          cancelText='取消'
          onClose={this.handleTagsModal.bind(this, 'closeOther')}
          onCancel={this.handleTagsModal.bind(this, 'closeOther')}
          onConfirm={this.handleTagsModal.bind(this, 'other_confirm')}
        >
          {isOpenedOther ? (
            <View>
              <AtInput
                type='text'
                placeholder='请输入'
                value={otherValue}
                maxLength={4}
                onChange={(value) => this.handleTagsModal('other_input', value)}
              />
            </View>
          ) : null}
        </KbModal>
        <AtActionSheet
          isOpened={isOpenedPhone}
          cancelText='取消'
          onCancel={this.handleBindPhone.bind(this, 'close')}
        >
          <AtActionSheetItem>
            <Button
              className='kb-sheet__button kb-relation-bindPhone--item'
              onClick={this.handleBindPhone.bind(this, 'close')}
            >
              <KbLoginAuth
                text={mobile ? '使用其他号码' : '绑定手机号'}
                scope='phoneNumber'
                useOpenType
                linked
                onAuthComplete={this.handleAuthComplete.bind(this)}
              />
            </Button>
          </AtActionSheetItem>
          {mobile ? (
            <AtActionSheetItem onClick={this.handleBindPhone.bind(this, 'close')}>
              <Button
                className='kb-sheet__button kb-relation-bindPhone--item'
                hoverClass='kb-sheet__button--hover'
                onClick={this.handleUnbind.bind(this)}
              >
                解除关联
              </Button>
            </AtActionSheetItem>
          ) : null}
        </AtActionSheet>
      </KbPage>
    );
  }
}

export default Index;
