/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbAgreement from '@/components/_pages/agreement';
import apis from '@/utils/apis';
import { getUserMobile, resetByBindMobile } from '@/utils/qy';
import { encryptionNew } from '@base/utils/request/encryption';
import KbCheckbox from '@base/components/checkbox';
import KbCountDown from '@base/components/count-down';
import KbLoginAuth from '@base/components/login/auth';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import Form from '@base/utils/form';
import request from '@base/utils/request';
import { getSessionIDFromLoginData, reportAnalytics } from '@base/utils/utils';
import { Image, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { AtButton, AtInput } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '登录',
  };
  constructor() {
    this.userMobileData = {};
    /**
     * this.$router.params 参数说明
     * type  默认self主要是绑定手机号/family关联手机号
     * source 可选参数 主要是统计绑定来源
     * auth 默认1 即默认授权绑定/0用户输入手机号验证码绑定
     * const { principalType } = Taro.getExtConfigSync()  principalType===1 个人开发者，只能使用用户手动输入绑定
     */
    const { type = 'self' } = this.$router.params;
    this.state = {
      type, // self绑定手机号/family关联手机号
      form: {
        data: {},
        disabled: true,
      },
      bindMode: 'auth', //绑定手机号的模式 auth手机号授权/input手动输入
      action: 'bind',
      extraData: {},
      ...this.getCurrentState(),
      agree: false,
      triggerSignAgreement: false,
      isNeedImgCode: false, //是否需要填图形验证码
    };
    this.agreeActionRef = createRef();
    this.agreeActionRef1 = createRef();
  }

  componentDidMount() {
    this.updateTitle();
  }

  onUpdate = (data) => {
    const { logined } = data;
    if (logined) {
      const { type } = this.state;
      if (type !== 'family') {
        getUserMobile()
          .then((data) => {
            this.userMobileData = {
              ...data,
            };
          })
          .catch(() => {});
      }
    }
    this.setState(
      {
        ...this.getCurrentState(),
      },
      this.createForm,
    );
  };

  getCurrentState() {
    const { loginData = {} } = this.props;
    const { type = 'self', auth = '1', source = '' } = this.$router.params || {};
    const { userInfo: { mobile: boundMobile = '' } = {} } = loginData || {};
    // 定制小程序个人开发者的使用场景
    const { principalType } = Taro.getExtConfigSync() || {};
    const extraData = {};
    if (/^bd-/.test(source)) {
      extraData.bd_source = source;
    }

    return {
      action: type === 'family' || !boundMobile ? 'bind' : 'modify',
      bindMode: type === 'family' || principalType == '1' || auth != '1' ? 'input' : 'auth',
      extraData,
    };
  }

  updateTitle = () => {
    const { type } = this.state;
    if (type === 'family') {
      Taro.setNavigationBarTitle({
        title: `关联手机号`,
      });
    }
  };

  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    const { id: rId } = this.$router.params;
    const { type, action } = this.state;
    const typeOpt = {
      self: '0',
      family: '1',
    };
    this.formIns = new Form(
      {
        form: {
          mobile: {
            reg: 'phone',
          },
          img_code: {
            required: false,
          },
          verify_code: {
            min: 4,
          },
        },
        api: {
          url: apis[`mobile.${action}`],
          toastError: true,
          toastSuccess: `操作成功`,
          mastHasMobile: type === 'family',
          autoTriggerLoginModal: true,
          formatRequest: (req) => {
            const { id = rId } = this.userMobileData;
            const { params: { source } = {} } = this.$router;
            // 公众号跳转上报参数
            if (/^bd-/.test(source) && action === 'bind') {
              req.bd_source = source;
            }
            return {
              id,
              mobile_type: typeOpt[type],
              ...req,
            };
          },
          onCustomCheck: (req) => {
            if (!req.mobile) {
              return { code: 1, msg: '手机号不能为空' };
            }
            if (req.mobile && req.mobile.length != 11) {
              return { code: 1, msg: '请输入正确的手机号' };
            }
            if (!req.verify_code) {
              return { code: 1, msg: '短信验证码不能为空' };
            }
            if (req.verify_code && req.verify_code.length < 4) {
              return { code: 1, msg: '验证码错误' };
            }
          },
          onIntercept: () => {
            if (!this.state.agree) {
              this.showAgreement();
              return true;
            }
            return false;
          },
          onThen: (res) => {
            const { code } = res;
            if (code == 0) {
              this.agreeActionRef.current.signAgreement();
              this.agreeActionRef1.current.signAgreement();
              if (action === 'bind') {
                this.handleAuthComplete();
              }
              resetByBindMobile(res, type);
            }
          },
        },
        onReady: () => {
          this.checkIsNeedImgCode();
        },
      },
      this,
    );
  };

  handleAuthClick() {
    if (!this.state.agree) {
      this.showAgreement();
    }
  }
  handleAuthMobileFail = () => {
    // 授权失败，切换为手动输入模式
    this.setState({
      bindMode: 'input',
    });
  };
  handleAuthComplete = (mobile) => {
    const { params: { source } = {} } = this.$router;
    const reportKey = 'bind_mobile_phone';
    if (/^bd-/.test(source)) {
      reportAnalytics({
        key: reportKey,
        source,
      });
    }
    if (mobile) {
      Taro.navigator({
        post: {
          type: 'updateMobile',
        },
      });
    }
  };

  // 获取验证码
  onSendCode = (start) => {
    const {
      type,
      form: {
        data: { mobile, img_code },
      },
      isNeedImgCode,
    } = this.state;
    if (isNeedImgCode && !img_code) {
      Taro.kbToast({
        text: '请先输入图形验证码',
      });
      return;
    }
    request({
      url: apis['mobile.get.code'],
      data: { mobile, is_relate: type === 'family' ? 1 : 0 },
      form: {
        mobile: {
          reg: 'phone',
        },
      },
      mastHasMobile: type === 'family',
      autoTriggerLoginModal: true,
      toastError: true,
      toastSuccess: '已发送',
      quickTriggerThen: true,
      formatRequest: (req) => {
        req.img_code = img_code;
        return req;
      },
      onThen: ({ code }) => {
        if (code == 0) {
          start();
        }
      },
    });
  };

  // 检测是否需要填图形验证码
  checkIsNeedImgCode() {
    request({
      url: '/api/weixin/mini/user/Bind/needVerifyImg',
      onThen: (res) => {
        let isNeedImgCode = !!(res.code == 0 && res.data);
        this.setState({
          isNeedImgCode,
        });
        if (isNeedImgCode) {
          this.handleCreateImageCodeUrl();
          this.formIns.resetForm(
            {
              img_code: {
                required: true,
              },
            },
            true,
          );
        }
      },
    });
  }

  // 获取图形验证码
  handleCreateImageCodeUrl = async () => {
    const sessionID = getSessionIDFromLoginData(Taro.kbLoginData, false);
    const url = await encryptionNew('/api/weixin/mini/user/Bind/generateVerifyImg');
    this.setState({
      imageCodeUrl: `${url}&sessionid=${sessionID}`,
    });
  };

  // 切换是否同意
  onSwitchAgree = (agree) => {
    this.setState({
      agree,
    });
  };

  showAgreement() {
    Taro.kbModal({
      top: false,
      closable: false,
      title: '温馨提示',
      content: '请先阅读并同意服务协议',
      cancelText: '取消',
    });
  }

  renderAgreement = (showTips) => {
    const { agree } = this.state;
    return (
      <View className='kb-agreement'>
        <View className='at-row at-row__align--center at-row__justify--center'>
          {showTips && !agree && <View className='kb-agreement-tips'>请勾选同意后再登录</View>}
          <KbCheckbox
            checked={agree}
            onChange={this.onSwitchAgree}
            className='kb-color__black'
            layout='flex'
          >
            <View className='at-row at-row__align--center kb-size__base'>
              <View className='agreement-text kb-spacing-sm-l'>我已阅读并同意</View>
              <KbAgreement agreeType='serviceAgreement' actionRef={this.agreeActionRef} />
              <View className='agreement-text'>和</View>
              <KbAgreement agreeType='bindMobileAgreement' actionRef={this.agreeActionRef1} />
            </View>
          </KbCheckbox>
        </View>
      </View>
    );
  };

  render() {
    const {
      type,
      action,
      triggerSignAgreement,
      form: { data },
      bindMode = 'auth',
      extraData,
      isNeedImgCode,
      agree,
      imageCodeUrl,
      ...rest
    } = this.state;

    return (
      <KbPage {...rest} onUpdate={this.onUpdate} autoTriggerLoginModal>
        <KbScrollView
          renderFooter={
            <Fragment>
              {bindMode === 'input' ? (
                <View className='kb-spacing-md'>{this.renderAgreement()}</View>
              ) : null}
            </Fragment>
          }
        >
          <View className={classNames('kb-bind')}>
            {bindMode === 'auth' ? (
              <Fragment>
                <View className='kb-bind__auth kb-box kb-margin-md'>
                  <Image
                    className='kb-bind__auth--image'
                    src={
                      process.env.MODE_ENV === 'third'
                        ? 'https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/phone/banner2.png'
                        : 'https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/phone/banner1.png'
                    }
                    mode='widthFix'
                    lazyLoad
                  />
                  <View className='kb-bind__auth--agreement kb-spacing-md-tb'>
                    {this.renderAgreement(true)}
                    <View className='kb-spacing-md-l kb-size__base'>
                      注：首次登录用户自动注册账号
                    </View>
                  </View>
                  <View className='kb-bind__auth--box'>
                    <KbLoginAuth
                      text='手机号快速登录'
                      scope='phoneNumber'
                      useOpenType={agree}
                      className='kb-button__middle'
                      size='normal'
                      onClick={this.handleAuthClick.bind(this)}
                      onAuthComplete={this.handleAuthComplete.bind(this)}
                      onAuthFail={this.handleAuthMobileFail}
                      extraData={extraData}
                    />
                  </View>
                </View>
              </Fragment>
            ) : bindMode === 'input' ? (
              <Fragment>
                <View
                  className={classNames(
                    'kb-bind__form kb-form kb-margin-md-lr kb-border-radius-md',
                  )}
                >
                  <View className='kb-form__item'>
                    <View className='item-content'>
                      <AtInput
                        placeholderClass='placeholder'
                        cursor={-1}
                        clear
                        maxLength={11}
                        onChange={this.onChange_form.bind(this, 'mobile')}
                        placeholder='请输入手机号'
                        type='number'
                        value={data.mobile}
                      />
                    </View>
                  </View>
                  {isNeedImgCode ? (
                    <View className='kb-form__item'>
                      <View className='item-content'>
                        <AtInput
                          placeholderClass='placeholder'
                          cursor={-1}
                          clear
                          maxLength={10}
                          onChange={this.onChange_form.bind(this, 'img_code')}
                          placeholder='请输入图形验证码'
                          type='number'
                          value={data.img_code}
                        />
                      </View>
                      <View
                        className='item-extra img_code'
                        onClick={this.handleCreateImageCodeUrl}
                        hoverClass='kb-hover-opacity'
                      >
                        {imageCodeUrl ? <Image src={imageCodeUrl} lazyLoad /> : null}
                      </View>
                    </View>
                  ) : null}
                  <View className='kb-form__item'>
                    <View className='item-content'>
                      <AtInput
                        placeholderClass='placeholder'
                        cursor={-1}
                        clear
                        maxLength={10}
                        onChange={this.onChange_form.bind(this, 'verify_code')}
                        placeholder='请输入验证码'
                        type='number'
                        value={data.verify_code}
                      />
                    </View>
                    <View className='item-extra item-extra__line'>
                      <KbCountDown onClick={this.onSendCode} />
                    </View>
                  </View>
                </View>
                <View className='kb-form__button'>
                  <AtButton
                    type='primary'
                    circle
                    onClick={this.onSubmit_form}
                    className='kb-button__middle'
                  >
                    {type === 'family' ? '保存' : '登录'}
                  </AtButton>
                </View>
              </Fragment>
            ) : null}
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
