/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import KbAdFloat from '@/components/_pages/ad-extension/ad/float';
import KbAdInsertScreen from '@/components/_pages/ad-extension/ad/insertScreen';
import { createAd } from '@/components/_pages/ad-extension/sdk/index';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import KbBars from '@base/components/bars';
import KbEmpty from '@base/components/empty';
import KbLoader from '@base/components/loader';
import KbLongList from '@base/components/long-list';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbPage from '@base/components/page';
import KbStationEntry from '@/components/_pages/store-card/stationentry/stationentry';
import { appointmentPickup, checkIsShop } from '@/components/_pages/store-card/_utils';
import KbSubscribe from '@base/components/subscribe';
import KbTimeline from '@base/components/time-line';
import KbFeedbackBar from '@/components/_pages/query/feedback-bar';
import KbNotice from '@/components/_pages/query/notice';
import KbShareBar from '@/components/_pages/query/share-bar';
import { getStoreInfoByWaybill } from '@/components/_pages/query/_utils/query.appointment';
import {
  addWaybillRecord,
  checkNeedMobile,
  createQueryBars,
  createQueryListData,
  fixQueryInfoStatus,
  getOrderDetail,
  getQueryApiUrlAndData,
  getQueryNote,
  queryAction,
  subscriptionTips,
  switchBrand,
  updateNoteModal,
} from '@/components/_pages/query/_utils/query.detail';
import KbCurtain from '@/components/_pages/welfare/activity/curtain';
import { buriedPointQueryCard, setClipboardData } from '@/utils/qy';
import {
  refreshControl,
  REFRESH_KEY_QUERY,
  REFRESH_KEY_QUERY_DETAIL,
} from '@/utils/refresh-control';
import request from '@base/utils/request';
import { scanAction } from '@/utils/scan';
import { getShareAppMessage } from '@/utils/share';
import {
  debounce,
  getStorageSpell,
  importFieldHide,
  reportAnalytics,
  setStorage,
  triggerRefresh,
} from '@base/utils/utils';
import { Image, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { AtAvatar, AtButton, AtCurtain, AtIcon, AtFloatLayout } from 'taro-ui';
import '../index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands || {},
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '物流信息',
  };
  constructor() {
    const { isVip } = Taro.kbRelationInfo || {};
    const { params } = this.$router;
    const { waybill, brand, order_no, dakId } = params;
    this.triggerRefresh = debounce(() => triggerRefresh(this));
    this.handleReceives = debounce(this.handleReceive, 300, {
      trailing: true,
    });
    //  * express_company 快递公司英文简写(必填);
    //  * deliver_no 物流单号(必填);
    //  * order_id 查物流时使用(选填);
    //  * order_no 获取订单详情时使用(选填);
    //  * order_id 查物流时使用(选填);
    //  * order_random 订单随机码，从订单分享页面进入时一般会存在(选填);
    //  * from_channel 查物流渠道(选填)/status物流状态,配合判断是否刷新查件列表使用(选填)/note物流备注(选填);
    //  * dakId/dak_id 驿站id
    this.state = {
      loading: false,
      active: !!waybill,
      data: {
        list: [],
      },
      inn_info: {},
      waybill_info: { waybill, brand },
      subscribeApi: null,
      shareData: null,
      bars: null,
      orderDetail: { id: order_no },
      pushUserMobile: '',
      bindPushMobileIsOpened: false,
      activityPop: null,
      folatIsOpened: false,
      modalName: '',
      isOpenInsertScreen: false,
      isVip,
    };

    // 空数据图标
    this.emptyImage = {
      value: 'no-package',
    };

    if (dakId) {
      // 补充dak_id
      params.dak_id = dakId;
    }
    this.listData = createQueryListData(this.$router.params, (res) => {
      let isNeedFixParam =
        this.$router.params.waybill &&
        !this.$router.params.brand &&
        process.env.PLATFORM_ENV == 'swan';
      if (isNeedFixParam) {
        Object.keys(res).map((key) => {
          this.$router.params[key] = res[key];
        });
        if (this && this.listIns) {
          this.listIns.updateApiConfig(getQueryApiUrlAndData(this.$router.params));
          this.listIns.loader();
        }
        return;
      }
      let { list, data } = res || {};
      let { GetStatus, express, huaweiPhone: pushUserMobile } = data || {};
      let { status, position: status_en } = GetStatus || {};

      const { order_no, waybill, brand: brandParams, dakId } = this.$router.params;
      express = {
        brand: brandParams,
        ...express,
      };
      const { brand, name } = express;

      // 联系客服
      this.service = express;

      const dataState = {
        list,
        status: fixQueryInfoStatus(status),
        status_en,
        order_id: order_no,
      };
      const waybill_info = {
        brand: brand,
        waybill,
        brandName: name,
      };
      this.waybill_info = waybill_info;
      this.setState({
        data: dataState,
        waybill_info,
        pushUserMobile,
      });
      // 获取取件信息
      this.getStoreInfo(express);
      this.setSubscribeApi(waybill_info);
      // 获取备注
      getQueryNote({ waybill }).then((data) => {
        let { note: remarks } = data;
        const { note: sNote } = this.$router.params;
        remarks = remarks || (sNote && decodeURIComponent(sNote));
        this.setState({
          data: {
            ...dataState,
            remarks,
          },
        });
      });
      // 检查是否需要输入手机号尾号验证
      checkNeedMobile(data, this.triggerActivityCurtain).then(this.handleRefresh);
      // 检查是否需要绑定推送过来的手机号
      if (process.env.PLATFORM_ENV == 'swan' && pushUserMobile) {
        this.handleBindPushMobile('open');
      }
      // 触发获取订单信息，判断是否可出现查看订单按钮---此处注意获取的订单号并未填充到data中，需要改进
      this.triggerGetOrderDetail(waybill_info, (orderData) => {
        this.setState({
          bars: createQueryBars({ express, dakId, orderData }),
        });
      });
    });
  }

  componentDidMount() {
    // 插屏广告
    createAd('query.detail', {
      filter: ({ NOW_TS, STORAGE_TS }) => {
        const min = (NOW_TS * 1 - STORAGE_TS * 1) / 1000 / 60;
        return min < 30;
      },
    }).catch(() => {});
    this.props.dispatchGet();
    this.showInterstitialAd();

    refreshControl(REFRESH_KEY_QUERY);
  }

  componentDidShow() {
    if (refreshControl(REFRESH_KEY_QUERY_DETAIL, 'check')) {
      this.triggerRefresh();
    }
  }
  componentWillUnmount() {
    this.triggerCloseActivityPop();
  }
  // 更新订阅api配置
  setSubscribeApi = (data) => {
    const { waybill: waybill_no, brand } = data || {};
    this.setState({
      subscribeApi: {
        toastSuccess: '订阅成功!',
        toastError: true,
        data: {
          source: 'mina',
          template_title: 'express_notice',
          waybill_no,
          brand,
        },
      },
    });
  };
  showInterstitialAd() {
    const { isVip } = this.state;
    //优寄、VIP快递员禁止插屏广告
    if (process.env.PLATFORM_ENV != 'weapp' || isVip) {
      return;
    }
    this.setState({
      isOpenInsertScreen: true,
    });
  }
  getPopActivity = () => {
    if (process.env.PLATFORM_ENV === 'alipay') {
      request({
        url: '/g_tbk/v2/AdConfig/getAdConfig',
        data: { type: 'miniapp', platform: 'wkdaliapp' },
        toastLoading: false,
        mastLogin: false,
        onThen: (res) => {
          const { data = [], code } = res;
          let adMap = {
            8: 'activityPop',
            11: 'redPacket',
          };
          let activity = {};
          if (code === 0 && data) {
            data.forEach(({ position, imgUrl, adUrl, ...rest }) => {
              if (Object.keys(adMap).includes(position)) {
                activity[adMap[position]] = {
                  imgUrl,
                  adUrl,
                  ...rest,
                };
              }
            });
            this.activity = activity;
            if (activity.activityPop) {
              getStorageSpell({
                key: 'queryDetailPop',
              }).catch(() => {
                this.setState({
                  activityPop: activity.activityPop,
                });
              });
            }
          }
        },
      });
    }
  };
  // 触发活动幕帘
  triggerActivityCurtain = () => {
    this.setState({
      welfareData: {
        type: 'guide-send',
        position: '物流结果页',
      },
    });
  };

  onPostMessage = (key, data) => {
    switch (key) {
      case 'selectBrand':
        const { brand } = data || {};
        brand &&
          this.handleRefresh({
            express_company: brand,
          });
        break;

      default:
        break;
    }
  };
  // 分享卡片进入，获取驿站及取件码信息
  // 严格一点可根据场景之区分 Taro.getLaunchOptionsSync() 1007、1008 单人或群聊回话进入；
  getStoreInfo = (express = {}) => {
    let waybill_info = this.waybill_info;
    getStoreInfoByWaybill(waybill_info).then((inn_info) => {
      const { dakId, isMarked, expressStatus, dak_auth_status } = inn_info;
      let params = this.$router.params || {};
      this.setState({
        inn_info,
        shareData: {
          ...params,
          brandName: express.name,
          ...waybill_info,
          dakId,
          isMarked,
          expressStatus,
          dak_auth_status,
        },
      });
    });
  };

  // 获取订单详情，目前可能已经无用处了：暂时保留
  triggerGetOrderDetail = (waybill_info, callback) => {
    const { order_no } = this.$router.params;
    getOrderDetail({ order_no, ...waybill_info }).then(({ code, data }) => {
      const orderData = code == 0 && data;
      if (orderData && orderData.express_number) {
        const { order_no, note } = this.$router.params;
        // 有单号触发以下逻辑
        const { express_number: deliver_no, express_rand: express_company } = orderData;
        this.setState({
          waybill_info: {
            ...waybill_info,
            brand: express_company,
            waybill: deliver_no,
          },
          orderDetail: {
            id: order_no,
            ...orderData,
          },
        });
        // 添加到查件记录
        addWaybillRecord({
          order_no,
          note: decodeURIComponent(note),
          ...orderData,
        });
      }
      callback(orderData);
    });
  };

  // 复制
  handleCopy = (data, text) => {
    if (!data) return;
    setClipboardData(data, text);
  };

  // 重新查询
  handleRefresh = (data) => {
    this.setState(
      {
        loading: true,
      },
      () => {
        this.listIns.loader(data).then(() => {
          this.setState({
            loading: false,
          });
        });
      },
    );
  };

  // 添加备注
  addNote = () => {
    const {
      orderDetail: { id: order_no },
      data: { remarks: note, ...rest },
      waybill_info,
    } = this.state;
    updateNoteModal({ order_no, note, ...waybill_info })
      .then((data) => {
        const { note: remarks } = data;
        this.setState({
          data: {
            ...rest,
            remarks,
          },
        });
        this.$router.params.note = remarks;
      })
      .catch((err) => console.log(err));
  };

  // 分享
  onShareAppMessage = (e) => {
    const { waybill_info: info } = this.state;
    return getShareAppMessage(e, {
      page: 'query.detail',
      info,
    });
  };

  // 预约取件
  handlePickup = () => {
    const {
      waybill_info: { waybill, brand },
    } = this.state;
    const {
      inn_info: { dakId: dak_id },
    } = this.state;
    appointmentPickup({
      dak_id,
      waybill,
      brand,
    }).then(() => this.getStoreInfo());
  };

  // 长列表准备就绪
  handleReady = (ins) => {
    this.listIns = ins;
  };

  // 点击按钮
  onClickBars = (key) => {
    const {
      data: { order_id },
      orderDetail: { express_number, source: orderSource, id } = {},
      inn_info,
    } = this.state;
    const { order_random } = this.$router.params || {};
    queryAction(key, {
      service: this.service,
      order_id: order_id || id,
      express_number,
      orderSource,
      order_random,
      inn_info,
    });
  };

  // 跳转预约取件
  handleAppointment = () => {
    const {
      inn_info: { dakId },
    } = this.state;
    queryAction('appointment', { dakId });
  };

  handleSelectBrand = () => {
    const { orderDetail, data } = this.state;
    const { id } = orderDetail || {};
    const { status_en } = data || {};
    const { from_channel } = this.$router.params;
    switchBrand({ id, status_en, from_channel });
  };

  // 温馨提示
  handleShowTips = () => subscriptionTips();

  // 登录状态更新
  handleUpdate = (data) => {
    if (data.logined) {
      console.log('物流详情页面参数222', this.$router.params);
      const { waybill, __key__ } = this.$router.params;
      if (waybill && !__key__) {
        buriedPointQueryCard({ waybill_no: waybill });
      }
      this.getPopActivity();
      // 扫码寄
      scanAction()
        .then((options) => {
          console.log('扫码寄.options', options);
          if (options) {
            this.$router.params = {
              ...this.$router.params,
              ...options,
            };
            this.setState({
              active: options,
            });
          }
        })
        .catch((err) => console.log(err));
    }
  };

  handleToAppointment = () => {
    const { dakId } = this.state.inn_info;
    Taro.navigator({
      url: 'query/appointment',
      options: { dakId },
    });
  };
  triggerCloseActivityPop = () => {
    const { activityPop } = this.state;
    if (activityPop) {
      setStorage({
        key: 'queryDetailPop',
        data: {
          day: 1, //暂存时间为1天
        },
      });
      this.setState({
        activityPop: null,
      });
    }
  };
  handleCurtainClose = () => {
    this.setState({
      folatIsOpened: false,
      modalName: '',
    });
  };
  handleFollowed = () => {
    Taro.showToast({ title: '关注成功' });
    reportAnalytics({
      key: 'query.detail.pop',
      title: '关注按钮',
    });
    this.setState({
      modalName: '领取按钮',
    });
  };
  handleFollow = () => {
    const { modalName } = this.state;
    //备注统计时间
    switch (modalName) {
      case '领取按钮':
        reportAnalytics({
          key: 'query.detail.pop',
          title: '立即领取',
        });
        this.handleReceives();
        break;
      case '其他福利':
        adNavigator({
          ...this.activity.redPacket,
          report: {
            key: 'query.detail.pop',
            title: modalName,
          },
        });
        this.setState({
          folatIsOpened: false,
          modalName: '',
        });
        break;
      case '已关注领取':
        adNavigator({
          ...this.activity.activityPop,
          report: {
            key: 'query.detail.pop',
            title: modalName,
          },
        });
        this.setState({
          folatIsOpened: false,
          modalName: '',
        });
        break;
      default:
        break;
    }
  };
  handleReceive = () => {
    request({
      url: '/g_wkd/v2/activity/AttentionAward/award',
      toastLoading: false,
      mastLogin: false,
      onThen: (res) => {
        const { code } = res;
        if (code == 0) {
          this.setState({
            modalName: '其他福利',
          });
        }
      },
    });
  };
  handleToAttention = () => {
    this.triggerCloseActivityPop();
    request({
      url: '/g_wkd/v2/activity/AttentionAward/awardDetail',
      toastLoading: false,
      mastLogin: false,
      onThen: (res) => {
        const {
          data: { is_award = 0 },
          code,
        } = res;
        this.activity['is_award'] = is_award;
        if (code === 0 && is_award === 0) {
          this.setState({
            folatIsOpened: true,
            modalName: '关注按钮',
          });
        } else {
          this.setState({
            folatIsOpened: true,
            modalName: '已关注领取',
          });
        }
      },
    });
  };

  // 处理绑定推送的手机号
  handleBindPushMobile = (key) => {
    const { pushUserMobile } = this.state;
    switch (key) {
      case 'open':
        this.setState({
          bindPushMobileIsOpened: true,
        });
        break;
      case 'close':
        this.setState({
          bindPushMobileIsOpened: false,
        });
        break;
      case 'bind':
        request({
          url: '/g_order_core/v2/mina/Baidu/bind',
          data: {
            authorizedMobile: 1,
            user_name: pushUserMobile,
          },
          toastError: true,
          onThen: (res) => {
            if (res.code == 0) {
              Taro.kbUpdateUserInfo({
                mobile: pushUserMobile,
              });
              Taro.kbToast({
                text: '设置成功',
              });
              this.handleBindPushMobile('close');
            }
          },
        });
        break;
    }
  };

  render() {
    const { brands } = this.props;
    const {
      subscribeApi,
      welfareData,
      agreeSignTogether,
      bars,
      data: { list, ...data },
      waybill_info: { brand, waybill },
      inn_info,
      shareData,
      active,
      loading,
      pushUserMobile,
      bindPushMobileIsOpened,
      activityPop,
      folatIsOpened,
      modalName,
      isOpenInsertScreen,
      ...rest
    } = this.state;

    const {
      short_name: brandName = '',
      officially_link,
      url: brandUrl = officially_link,
    } = (brands && brands[brand]) || {};

    const hasList = list.length > 0;

    const detailCls = classNames('kb-query__detail', {
      'kb-query__detail--height': !hasList,
      'kb-query__detail--full': !hasList && !inn_info.dakId,
      'kb-query__detail--strut__down': !hasList && inn_info.dakId,
    });
    return (
      <KbPage
        {...rest}
        renderHeader={<KbOfficialAccount navigateId='12' />}
        renderFooter={
          <KbBars
            hold
            max={2}
            onClick={this.onClickBars}
            align='center'
            options={bars}
            size='smaller'
            renderBefore={
              <View className='at-row at-row__align--center'>
                {process.env.PLATFORM_ENV !== 'swan' ? (
                  <View>
                    <KbSubscribe
                      type='primary'
                      size='smaller'
                      circle
                      openSettingWhenNeed
                      api={subscribeApi}
                    >
                      订阅通知
                    </KbSubscribe>
                  </View>
                ) : null}
                <KbFeedbackBar
                  data={shareData}
                  type='primary'
                  size='smaller'
                  className='kb-margin-sm-l'
                />
                <View className='kb-footer-bar-box'>
                  {waybill && (
                    <View className='kb-spacing-sm-l'>
                      <KbShareBar type='primary' circle size='smaller' data={shareData} />
                    </View>
                  )}
                </View>
              </View>
            }
            renderAfter={
              <Fragment>
                {inn_info.dakId &&
                  !inn_info.hidePickupButton &&
                  inn_info.expressStatus != '0' &&
                  inn_info.dak_auth_status == 1 && (
                    <View
                      className='kb-pickup__info--button'
                      hoverStopPropagation
                      onClick={(e) => e.stopPropagation()}
                    >
                      <KbSubscribe
                        type='primary'
                        className='kb-button__smaller'
                        action='appointment'
                        circle
                        disabled={inn_info.isMarked}
                        onSubscribe={this.handlePickup}
                      >
                        {inn_info.isMarked ? '已预约' : '预约取件'}
                      </KbSubscribe>
                    </View>
                  )}
              </Fragment>
            }
          />
        }
        onUpdate={this.handleUpdate}
      >
        <KbLongList
          active={active}
          data={this.listData}
          emptyImage={this.emptyImage}
          onReady={this.handleReady}
        >
          {checkIsShop(inn_info) && (
            <KbStationEntry
              dakInfo={inn_info}
              renderNavTip={
                <Fragment>
                  {inn_info.canTogether && (
                    <View
                      onClick={this.handleToAppointment}
                      className='kb-pickup__info--tips at-row at-row__align--center at-row__justify--between'
                    >
                      <View className='kb-spacing-md-l'>
                        <Text>另</Text>
                        <Text className='kb-color__brand'>{inn_info.record_num}</Text>
                        <Text>件包裹</Text>
                      </View>
                    </View>
                  )}
                </Fragment>
              }
              renderAfter={
                <View className='kb-pickup__info'>
                  {inn_info.pickupCode && (
                    <Fragment>
                      <View className='kb-pickup__info--box'>
                        <View className='kb-size__sm kb-color__grey'>取件码</View>
                        <View className='kb-pickup__info--code kb-size__xxl kb-size__blod kb-color__brand'>
                          {inn_info.pickupCode}
                        </View>
                      </View>

                      {inn_info.canTogether && (
                        <View className='kb-pickup__info--tips at-row at-row__align--center at-row__justify--between'>
                          <Text>另有</Text>
                          <Text className='kb-color__brand'>{inn_info.record_num}</Text>
                          <Text>件包裹</Text>
                        </View>
                      )}
                    </Fragment>
                  )}
                </View>
              }
            />
          )}
          <View className={detailCls}>
            <View className='kb-box kb-spacing-md'>
              <View className='at-row at-row__align--center'>
                <View
                  className='kb-spacing-md-r'
                  onClick={this.handleSelectBrand}
                  hoverClass='kb-hover-opacity'
                >
                  <AtAvatar
                    className='kb-avatar__pic'
                    image={
                      brand
                        ? `https://cdn-img.kuaidihelp.com/brand_logo/icon_${brand}.png?v=20230314`
                        : ''
                    }
                    circle
                  />
                </View>
                <View className='at-col'>
                  <View className='at-row at-row__align--center at-row__justify--between'>
                    <View>
                      <View className='at-row at-row__align--center'>
                        <View
                          hoverClass='kb-hover-opacity'
                          onClick={this.handleCopy.bind(this, waybill, '运单号已复制')}
                        >
                          {brandName && <Text className='kb-spacing-md-r'>{brandName}</Text>}
                          <Text>{waybill}</Text>
                        </View>
                      </View>
                    </View>
                    <View className='kb-color__brand'>{data.status}</View>
                  </View>
                  <View
                    className='kb-color__grey kb-size__sm kb-spacing-sm-t'
                    hoverClass='kb-hover-opacity'
                    onClick={this.addNote}
                  >
                    <Text className='kb-spacing-sm-r'>{data.remarks || '添加备注'}</Text>
                    <AtIcon
                      prefixClass='kb-icon'
                      value='edit-square'
                      className='kb-icon-size__sm'
                    />
                  </View>
                </View>
              </View>
            </View>
            <View className='kb-box kb-query__detail--list kb-query__detail--list-height'>
              <View className='kb-spacing-md-lr kb-query__detail--title at-row at-row__align--center at-row__justify--between'>
                <View>
                  <View
                    className='kb-query__detail--tips'
                    hoverClass='kb-hover-opacity'
                    onClick={this.handleShowTips}
                  >
                    <Text className='kb-icon__text--mr'>物流信息</Text>
                    <AtIcon
                      prefixClass='kb-icon'
                      value='arrow2_bottom'
                      className='kb-icon-size__sm kb-color__grey'
                    />
                  </View>
                  {data.day_consumed && (
                    <Text className='kb-color__orange kb-size__sm kb-spacing-sm-l'>
                      {data.day_consumed}
                    </Text>
                  )}
                </View>

                {process.env.PLATFORM_ENV !== 'swan' && (
                  <View>
                    <View className='at-row at-row__justify--end'>
                      <View>
                        <KbNotice type='secondary'>全程提醒</KbNotice>
                      </View>
                    </View>
                  </View>
                )}
              </View>

              {hasList ? (
                <View className='kb-query__detail--content'>
                  {data.wsShop && data.wsShop.nickname && (
                    <View className='kb-query__detail--ws'>
                      <Image
                        lazyLoad
                        className='kb-query__detail--ws-icon'
                        src='https://cdn-img.kuaidihelp.com/wkd/miniApp/wzgapp.png'
                        mode='widthFix'
                      />
                      <Text className='kb-color__grey'>“{data.wsShop.nickname}”发给您的包裹</Text>
                    </View>
                  )}
                  <KbTimeline items={list} max={-1} moreText='展示更多物流详情' />
                </View>
              ) : (
                <View className='kb-query__detail--empty'>
                  {loading ? (
                    <KbLoader centered />
                  ) : (
                    <KbEmpty centered image={this.emptyImage} description=''>
                      <View className='kb-color__grey kb-size__lg'>
                        <View>
                          <Text className='kb-display__inline-block'>点击</Text>
                          <AtButton
                            className='kb-button__link'
                            onClick={this.handleRefresh.bind(this, null)}
                          >
                            【重新查询】
                          </AtButton>
                        </View>
                        {brandUrl && (
                          <View>
                            <Text className='kb-display__inline-block'>或浏览器访问</Text>
                            <AtButton
                              className='kb-button__link'
                              onClick={this.handleCopy.bind(
                                this,
                                brandUrl,
                                '网址已复制\n请用浏览器打开',
                              )}
                            >
                              【{brandName}官网】
                            </AtButton>
                          </View>
                        )}
                      </View>
                    </KbEmpty>
                  )}
                </View>
              )}
            </View>
            <KbExternalAd adUnitIdIndex='query.detail' />
          </View>
        </KbLongList>
        <KbCurtain data={welfareData} />
        {process.env.PLATFORM_ENV !== 'swan' ? <KbAdFloat /> : null}
        <AtFloatLayout
          className='kb-bindPushMobile'
          isOpened={bindPushMobileIsOpened}
          title='手机号码授权绑定'
          onClose={this.handleBindPushMobile.bind(this, 'close')}
        >
          {bindPushMobileIsOpened && (
            <View className='kb-spacing-lg'>
              <View className='kb-size__lg kb-size-greyer'>您的手机号码为：</View>
              <View className='kb-size__xxl kb-size__bold kb-text__center kb-margin-lg'>
                {importFieldHide(pushUserMobile, 3, 7)}
              </View>
              <View className='kb-spacing-lg-tb'>
                <AtButton
                  className='kb-bindPushMobile__btn'
                  type='primary'
                  circle
                  onClick={this.handleBindPushMobile.bind(this, 'bind')}
                >
                  将此手机号设置为绑定手机号
                </AtButton>
              </View>
            </View>
          )}
        </AtFloatLayout>
        {activityPop && (
          <View className='kb-pop' hoverClass='kb-hover' onClick={this.handleToAttention}>
            <Image className='kb-pop-img' src={activityPop.imgUrl} mode='widthFix' />
          </View>
        )}
        <AtCurtain
          className='kb-follow'
          isOpened={folatIsOpened && !!modalName}
          onClose={this.handleCurtainClose}
          closeBtnPosition='bottom'
        >
          <Image
            className='kb-follow__img'
            src={
              modalName === '关注按钮'
                ? 'https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/04/13/625630fe217f6/f1.png'
                : modalName === '领取按钮'
                ? 'https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/03/30/62446c2271010/p.png'
                : modalName === '已关注领取'
                ? 'https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/03/30/62446c2271010/p.png'
                : modalName === '其他福利'
                ? 'https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/03/31/6244844313e55/f.png'
                : ''
            }
            style={{ width: '100%' }}
            mode='widthFix'
            onClick={this.handleFollow}
          />
          {process.env.PLATFORM_ENV === 'alipay' && modalName === '关注按钮' ? (
            <View className='kb-follow__lifestyle' onClick={this.handleFollow}>
              {/* eslint-disable-next-line */}
              <lifestyle publicId='2015010800024103' onFollow={this.handleFollowed} />
            </View>
          ) : null}
        </AtCurtain>
        <KbAdInsertScreen isOpen={isOpenInsertScreen} adType='query.detail' />
      </KbPage>
    );
  }
}

export default Index;
