/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbAdExtension from '@/components/_pages/ad-extension';
import { createAd } from '@/components/_pages/ad-extension/sdk/index';
import { getAdExtensionReq } from '@/components/_pages/ad-extension/_utils';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbCourierRadio from '@/components/_pages/query/courier-radio';
import KbList from '@/components/_pages/query/list';
import QueryScreen from '@/components/_pages/query/screen';
import KbShareBar from '@/components/_pages/query/share-bar';
import {
  getLightList,
  getpicktype,
  getScanScreen,
  openLights,
} from '@/components/_pages/query/_utils/query.appointment';
import KbStoreCard from '@/components/_pages/store-card';
import CabinetCard from '@/components/_pages/store-card/cabinet/cabinet';
import KbStationEntry from '@/components/_pages/store-card/stationentry/stationentry';
import { appointmentPickup } from '@/components/_pages/store-card/_utils';
import { refreshControl, REFRESH_KEY_QUERY } from '@/utils/refresh-control';
import { scanAction } from '@/utils/scan';
import { checkIsFromShare, getShareAppMessage } from '@/utils/share';
import KbPage from '@base/components/page';
import {
  debounce,
  frequencyLimitByMinute,
  importFieldHide,
  noop,
  triggerRefresh,
} from '@base/utils/utils';
import { ScrollView, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment, openLocation } from '@tarojs/taro';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import { AtButton, AtIcon } from 'taro-ui';
import Bubble from '~/components/_pages/query/appointment/bubble';
import { CABINET_SUB_KEY } from '~/components/_pages/pickup/_utils';
import './index.scss';

const adAgs = getAdExtensionReq('query.appointment');

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    enableShareTimeline: true,
    navigationBarTitleText: '预约取件',
  };

  constructor() {
    const {
      dakId: paramDakId = '',
      scene,
      bind = '0',
      action,
      batchId,
      shareKey,
      ...paramsRest
    } = this.$router.params;

    const [dakId = paramDakId, sceneSrc] = scene ? decodeURIComponent(scene).split(';') : [];
    const shareRq =
      checkIsFromShare(paramsRest) && batchId && shareKey ? { batchId, shareKey } : null; //通过分享进入时携带的参数，用来请求找人代取的数据

    this.state = {
      active: true,
      hasList: false,
      hasWithoutAppointment: true,
      list: null,
      dakId,
      dakInfo: {},
      shareData: { isAppointment: true },
      enableRefresh: true,
      src: sceneSrc, // 扫码进入统计标记
      shareRq,
      picktypeParams: getpicktype(paramsRest),
      lightList: null,
    };
    this.topID = 'top-id';
    this.triggerRefresh = debounce(() => triggerRefresh(this));
    shareRq && this.updateTitle();
  }

  triggerRefresh = () => triggerRefresh(this);

  // 更新标题
  updateTitle = () => {
    Taro.setNavigationBarTitle({
      title: '待取包裹',
    });
  };

  updateRelationInfo = () => {
    scanAction()
      .then((res) => {
        const { dakId, ...req } = res || {};
        if (!dakId) return;
        const { is_sub } = res;
        if (is_sub) {
          Taro.kbSetGlobalData(CABINET_SUB_KEY, is_sub);
        }
        this.setState({
          dakId,
          req,
        });
        this.updateTitle();
      })
      .catch((err) => {
        console.log('err', err.message);
      });
  };

  componentDidMount() {
    Taro.eventCenter.on('updatePickUpList', () => {
      this.triggerRefresh();
    });

    setTimeout(() => {
      createAd('pickup-load').catch(noop);
    }, 500);
  }

  /**
   * 登录状态更新回调，sessionID有变化时触发
   */
  handleUpdate = (data) => {
    if (data.logined) {
      createAd('query.appointment').catch(noop);
      this.updateRelationInfo();
    }
  };
  componentDidShow() {
    if (refreshControl(REFRESH_KEY_QUERY, 'check')) {
      this.triggerRefresh();
    }
    this.setState({
      onShowStamptime: Date.now(),
    });
  }

  onShareAppMessage = getShareAppMessage;

  // 滚动触发刷新开启与关闭
  handleScroll = (e) => {
    const {
      detail: { scrollTop },
    } = e;
    const enableRefresh = scrollTop <= 10;
    if (enableRefresh !== this.state.enableRefresh) {
      // 外层滚动到顶部再允许刷新
      this.setState({
        enableRefresh,
      });
    }
  };

  // 加载更多：通过加载时间戳，触发子组件加载更多
  handleScrollToLower = () => {
    if (process.env.MODE_ENV !== 'wkd') {
      this.setState({
        loadMore: new Date().getTime(),
      });
    }
  };

  handleGetted = (list) => {
    // 存在未预约件
    const hasWithoutAppointment = list.find((i) => i.isMark == 0) ? true : false;
    this.setState({
      hasList: list.length > 0,
      list,
      hasWithoutAppointment,
      listReady: true, // 已经请过列表标识
    });
    this.getLights(list);
  };

  // 预约取件
  handlePickup = (remark) => {
    const { dakId: dak_id, list } = this.state;
    appointmentPickup({
      dak_id,
      list,
      remark,
    })
      .then(() => {
        this.triggerRefresh();
        createAd('pickup', {
          MIN_TS: 0,
          filter: ({ NOW_TS, STORAGE_TS }) => {
            const limit = 30 * 60 * 60;
            if (Math.abs(STORAGE_TS - NOW_TS) < limit) {
              return true;
            }
            return false;
          },
        }).catch(noop);
      })
      .catch((err) => console.log(err));
  };

  // 驿站信息更新
  handleGetDakInfo = (data) => {
    if (!data) return;
    const {
      address,
      dakId,
      dak_auth_status = 1,
      latitude,
      longitude,
      time,
      start_time: startTime,
      end_time: endTime,
      phone = '',
      home_deliver = '0',
    } = data;
    // 取件大屏扫码
    getScanScreen({ ...this.state.req, dak_id: dakId });
    const [start_time, end_time] = startTime && endTime ? [startTime, endTime] : ['08:00', '20:00'];
    this.setState({
      dakInfo: {
        ...data,
        address,
        dak_auth_status,
        time,
        latitude,
        longitude,
      },
      shareData: {
        dakAddress: address,
        dakId,
        support_apply: true,
        is_self: 1,
        start_time,
        end_time,
        isAppointment: true,
        dakPhone: phone,
        home_deliver,
        dak_auth_status,
      },
    });
    // 建立下单关系
    const { action } = this.$router.params;
    if (action === 'location') {
      // 自动跳转定位
      openLocation(data);
    }
    if (data.cabinet_info && data.cabinet_info.id) {
      this.updateTitle();
    }
  };

  // 跳转
  handleNavigator = (key) => {
    if (key === 'self-out') {
      Taro.navigator({
        url: 'query/appointment/self-out',
        options: {
          cm_id: this.state.dakId,
        },
      });
      return;
    }
    Taro.navigator({
      url: key === 'IDcode' ? key : `user/relation/edit?type=${key}`,
      options: {
        action: key === 'IDcode' ? 'turnstiles' : '',
      },
    });
  };

  getLights = (list) => {
    const { dakInfo, dakId, picktypeParams, shareRq } = this.state;
    const isCabinet = dakInfo.cabinet_info && dakInfo.cabinet_info.id;
    const { picktype } = picktypeParams;
    if (isCabinet || picktype == 'gate' || shareRq) return;
    getLightList({
      dak_id: dakId,
      waybill: list.map((i) => i.waybill).join(','),
    }).then((res) => {
      this.setState({
        lightList: res,
      });
    });
  };

  handleSetLight = () => {
    const { dakId, lightList } = this.state;
    frequencyLimitByMinute('check', 'batch_light', 1).then((isLimit) => {
      const tempData = Taro.kbGetGlobalData('batch_light_data');
      if (!isLimit || !tempData) {
        openLights({
          dak_id: dakId,
          waybill: lightList.join(','),
          source: '1',
        }).then((res) => {
          const { code, data = {}, msg } = res || {};
          if (code == 0) {
            frequencyLimitByMinute('limit', 'batch_light');
            Taro.kbSetGlobalData('batch_light_data', data);
            this.handleShowLightModal(data);
          } else {
            Taro.kbToast({
              text: msg,
            });
          }
        });
      } else {
        this.handleShowLightModal(tempData);
      }
    });
  };
  handleShowLightModal = (data) => {
    if (!data) return;
    Taro.kbModal({
      isOpened: true,
      top: false,
      centered: true,
      closable: false,
      title: ' ',
      template: [
        {
          tag: 'view',
          className: 'kb-display__inline-block kb-spacing-xl-b kb-spacing-md-t kb-size__xl',
          value: '您的包裹亮',
        },
        {
          tag: 'view',
          className: 'kb-display__inline-block kb-spacing-xl-b kb-spacing-md-t kb-size__xl',
          style: `color: ${data.bg_color}`,
          value: data.color,
        },
      ],
      confirmText: false,
      actionClass: 'kb-modal__action__white',
      action: [
        {
          key: 'confirm',
          label: '我知道了',
        },
      ],
    });
  };

  render() {
    const {
      hasList,
      enableRefresh,
      loadMore,
      list,
      hasWithoutAppointment,
      dakId,
      active,
      dakInfo = {},
      shareData,
      shareRq,
      picktypeParams,
      onShowStamptime,
      listReady,
      req,
      lightList,
      ...rest
    } = this.state;
    const {
      params: { mobile: paramMobile, waybill },
    } = this.$router;
    const { loginData: { userInfo: { mobile: userMobile } = {} } = {} } = this.props;
    const showMobile = paramMobile || userMobile;
    const listCls = classNames({
      'kb-appointment__list--absolute': !hasList,
      'kb-spacing-md-t': shareRq,
    });
    const isReadyGetDakInfo = !isEmpty(dakInfo);
    const { view_picture } = dakInfo || {};

    // 快递柜
    const isCabinet = isReadyGetDakInfo
      ? !!(dakInfo.cabinet_info && dakInfo.cabinet_info.id)
      : null;

    const { picktype, source } = picktypeParams;

    const is_show_query_button =
      hasList && (dakInfo.dak_auth_status == 1 || dakInfo.home_deliver == '1');

    const hasLight = lightList && lightList.length > 0;

    const BatchLight = hasLight ? (
      <View className='kb-footer-bars-item'>
        <AtButton circle type='primary' onClick={this.handleSetLight}>
          全部亮灯
        </AtButton>
      </View>
    ) : null;

    return (
      <KbPage
        {...rest}
        onUpdate={this.handleUpdate}
        renderHeader={
          <Fragment>{!shareRq ? <KbOfficialAccount navigateId='13' /> : null}</Fragment>
        }
        renderFooter={
          <Fragment>
            {isCabinet ? null : dakInfo.auto_confirm == 1 ? (
              <View className='kb-spacing-md kb-background__white'>
                <View className='at-row at-row__justify--between'>
                  <View className='kb-footer-bars-item'>
                    <AtButton
                      circle
                      type='primary'
                      onClick={this.handleNavigator.bind(this, 'self-out')}
                    >
                      自助出库
                    </AtButton>
                  </View>
                  {BatchLight}
                </View>
              </View>
            ) : picktype == 'gate' ? (
              <QueryScreen
                hasList={hasList}
                dakInfo={dakInfo}
                onShowStamptime={onShowStamptime}
                listReady={listReady}
                source={source}
                gateInfo={req}
              />
            ) : !shareRq ? (
              <View className='kb-footer-bars'>
                {is_show_query_button ? (
                  <Fragment>
                    {hasWithoutAppointment || dakInfo.home_deliver == '1' ? (
                      <View className='kb-spacing-md  bar-item at-row at-row__justify--between'>
                        <View className='kb-footer-bars-item'>
                          <KbShareBar
                            circle
                            type='primary'
                            onSubscribe={this.handlePickup}
                            data={shareData}
                            list={list}
                            source='326'
                          />
                        </View>
                        {BatchLight}
                      </View>
                    ) : (
                      <View className='kb-spacing-md bar-item at-row at-row__justify--between'>
                        <View className='kb-footer-bars-item'>
                          <AtButton circle type='primary' className='at-button--disabled' disabled>
                            已预约
                          </AtButton>
                        </View>
                        {BatchLight}
                      </View>
                    )}
                  </Fragment>
                ) : (
                  BatchLight
                )}
              </View>
            ) : null}
          </Fragment>
        }
      >
        <ScrollView
          scrollY
          className='kb-scrollview'
          onScroll={this.handleScroll}
          onScrollToLower={this.handleScrollToLower}
          enableFlex
        >
          {dakId && (
            <Fragment>
              {isCabinet ? (
                <CabinetCard
                  data={dakInfo}
                  dakId={dakId}
                  req={req}
                  lite={!!shareRq}
                  onChange={this.handleGetDakInfo}
                  showTips
                />
              ) : shareRq ? (
                <KbStoreCard req={req} hideBars dakId={dakId} onChange={this.handleGetDakInfo} />
              ) : (
                <Fragment>
                  <KbStationEntry
                    dakId={dakId}
                    req={req}
                    onChange={this.handleGetDakInfo}
                    showSearch
                  />
                  <View className='kb-appointment__phone--box kb_preix '>
                    <View className='kb-appointment__phone kb-box at-row at-row__align--center at-row__justify--between'>
                      <View>
                        {showMobile && (
                          <View
                            onClick={this.handleNavigator.bind(this, 'self')}
                            hoverClass='kb-hover-opacity'
                          >
                            <AtIcon
                              prefixClass='kb-icon'
                              value='mobile'
                              className='kb-color__brand kb-icon-size__base'
                            />
                            <Text className='kb-icon__text--ml'>
                              {importFieldHide(showMobile, 3, 7)}
                            </Text>
                          </View>
                        )}
                      </View>
                      <View>
                        <View className='at-row'>
                          <View>
                            <AtButton
                              type='secondary'
                              className='kb-button__mini'
                              circle
                              onClick={this.handleNavigator.bind(this, 'IDcode')}
                            >
                              身份码
                            </AtButton>
                          </View>
                          <View className='kb-spacing-md-l'>
                            <AtButton
                              type='secondary'
                              circle
                              className='kb-button__mini'
                              onClick={this.handleNavigator.bind(this, 'family')}
                            >
                              关联亲友
                            </AtButton>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                  {/* 快递员消息 */}
                  <KbCourierRadio />
                </Fragment>
              )}
            </Fragment>
          )}
          {process.env.MODE_ENV.includes('third') ? (
            <View className='kb-margin-md-lr kb-margin-lg-b'>
              <KbAdExtension data={adAgs} />
            </View>
          ) : (
            ''
          )}
          <View className='kb-appointment__list'>
            <View className={listCls}>
              {isReadyGetDakInfo && (
                <KbList
                  adUnitIdIndex='query.appointment'
                  enableRefresh={enableRefresh}
                  loadMore={loadMore}
                  dakId={dakId}
                  mode='appointment'
                  dakInfo={dakInfo}
                  dakAddress={dakInfo.address}
                  onGetted={this.handleGetted}
                  active={active}
                  height='fixed'
                  {...shareRq}
                  mobile={paramMobile}
                  waybill={waybill}
                  picktype={picktype}
                  viewPicture={view_picture}
                  isCabinet={isCabinet}
                />
              )}
            </View>
          </View>
        </ScrollView>
        <Bubble />
      </KbPage>
    );
  }
}

export default Index;
