/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import { Text, View } from '@tarojs/components';
import KbPage from '@base/components/page';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbSendMessage from '@/components/_pages/query/conversation-send';
import { answer, getAnswerDetail } from '@/components/_pages/query/_utils/query.feedback';
import KbConversationList from '@/components/_pages/query/conversation-list';
import { debounce, triggerRefresh } from '@base/utils/utils';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '投诉反馈',
  };

  constructor(props) {
    super(props);
    this.state = {
      active: false,
      problemInfo: {},
    };
    this.triggerRefresh = debounce(() => triggerRefresh(this));
  }

  onUpdate = (data) => {
    const { logined } = data;
    if (logined) {
      const { problemId } = this.$router.params;
      getAnswerDetail(problemId).then((res) => {
        const { problemInfo = {} } = res || {};
        this.setState({
          problemInfo,
          active: true,
        });
      });
    }
  };

  handleSend = (comment = {}) => {
    const { problemId } = this.$router.params;
    answer({
      problemId,
      ...comment,
    }).then(() => {
      this.triggerRefresh();
    });
  };

  render() {
    const { active, problemInfo, ...rest } = this.state;
    const { problemId } = this.$router.params;
    const {
      accept_user_name,
      waybill_no,
      comment,
      pic_list = [],
      have_annex,
      status,
      des,
    } = problemInfo || {};

    const insertData = [];

    if (comment || des || pic_list.length > 0 || have_annex == 1) {
      insertData.push(problemInfo);
    }

    return (
      <KbPage
        onUpdate={this.onUpdate}
        {...rest}
        renderHeader={
          <View className='kb-conversation__header'>
            {process.env.MODE_ENV !== 'xyt' && <KbOfficialAccount navigateId='13' />}
            <View className='kb-margin-md kb-spacing-md kb-background__white kb-border-radius'>
              <View>
                <Text className='kb-color__grey-3'>投诉驿站：</Text>
                {accept_user_name}
              </View>
              <View>
                <Text className='kb-color__grey-3'>包裹单号：</Text>
                {waybill_no}
              </View>
            </View>
          </View>
        }
        renderFooter={
          <View className='kb-conversation__footer kb-background__white'>
            <KbSendMessage
              onSend={this.handleSend}
              disabled={['2', '3', '4'].includes(`${status}`)}
            />
          </View>
        }
      >
        <KbConversationList reqParams={{ problemId }} insertData={insertData} active={active} />
      </KbPage>
    );
  }
}

export default Index;
