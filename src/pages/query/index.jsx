/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbAdExtension from '@/components/_pages/ad-extension';
import { createAd, preloadAd } from '@/components/_pages/ad-extension/sdk';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbWaitPay from '@/components/_pages/order/waitpay';
import KbQueryBar from '@/components/_pages/query-bar';
import KbList from '@/components/_pages/query/list';
import { createGridBars } from '@/components/_pages/query/_utils';
import KbStoreCardUsed from '@/components/_pages/store-card/used';
import KbUserActivity from '@/components/_pages/user/activity';
import KbCurtain from '@/components/_pages/welfare/activity/curtain';
import { tabItemTapCall } from '@/components/_pages/_utils';
import { refreshControl, REFRESH_KEY_QUERY } from '@/utils/refresh-control';
import { scanAction } from '@/utils/scan';
import { getShareAppMessage } from '@/utils/share';
import { getShareTimeline } from '@/utils/shareTimeline';
import KbPage from '@base/components/page';
import { getLaunchParams } from '@base/utils/navigator';
import { debounce, isAvailableValue, noop, triggerRefresh } from '@base/utils/utils';
import { ScrollView, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  isVip: global.isVip,
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: process.env.MODE_ENV === 'wkd' ? '查快递' : '取件',
  };

  constructor() {
    this.state = {
      active: true,
      enableRefresh: true,
      bars: createGridBars(),
    };
    // 顶部的id
    this.topID = 'top-id';
    this.triggerRefresh = debounce(() => triggerRefresh(this));
  }

  onTabItemTap = tabItemTapCall;

  componentDidMount() {
    Taro.eventCenter.on('updatePickUpList', () => {
      this.triggerRefresh();
    });
    // this.triggerRefresh();
  }
  componentWillUnmount() {
    Taro.eventCenter.off('updatePickUpList');
  }
  componentDidShow() {
    if (refreshControl(REFRESH_KEY_QUERY, 'check')) {
      this.triggerRefresh();
    }
    if (process.env.MODE_ENV === 'wkd') {
      if (this.secondShow) {
        const { source } = getLaunchParams(this);
        if (source === 'question' && !this.state.welfareData) {
          // 反馈获取
          this.setState({
            welfareData: { type: 'question' },
          });
        }
      }
      this.secondShow = true;
      preloadAd(['0', '1']);
    }
  }

  componentWillReceiveProps(props) {
    if (process.env.MODE_ENV === 'wkd') {
      const { isVip } = props;
      const { isVip: nextIsVip } = this.props;
      if (
        isAvailableValue(nextIsVip) &&
        nextIsVip !== isVip &&
        !nextIsVip &&
        !this.state.welfareData
      ) {
        // 有效值
        this.setState({
          welfareData: {
            type: 'guide-send',
            position: '首页',
          },
        });
      }
    }
  }

  // 分享
  onShareAppMessage = getShareAppMessage;
  // 朋友圈分享
  onShareTimeline = getShareTimeline;

  // 滚动触发刷新开启与关闭
  handleScroll = (e) => {
    const {
      detail: { scrollTop },
    } = e;
    const enableRefresh = scrollTop <= 10;
    if (enableRefresh !== this.state.enableRefresh) {
      // 外层滚动到顶部再允许刷新
      this.setState({
        enableRefresh,
      });
    }
  };

  // 加载更多：通过加载时间戳，触发子组件加载更多
  handleScrollToLower = () => {
    this.setState({
      loadMore: new Date().getTime(),
    });
  };

  // 点击工具条按钮
  handleClickBars = (item) => {
    const { url, id = -1, options, appId, target, extraData, envVersion } = item;
    Taro.navigatorAndDocument({
      url,
      options,
      id,
      appId,
      target,
      extraData,
      envVersion,
    });
  };

  handleRelationInfoUpdate = (data) => {
    this.relationInfo = data;
    this.setState({
      bars: createGridBars(data.is_vip),
    });
  };

  handleUpdate = (data) => {
    if (process.env.PLATFORM_ENV === 'alipay' || process.env.MODE_ENV === 'wkd') {
      if (!data.logined) return;
      scanAction(null, this).catch(noop);
    }
    if (!data.logined) return;
    createAd('query').catch(noop);
  };

  handleListGet(list) {
    Taro.kbSetGlobalData('queryListFirstData', list && list.length > 0 ? list[0] : null);
  }

  render() {
    const { enableRefresh, active, loadMore, bars, ...rest } = this.state;
    const { isKdg } = this.props; // 如果快递柜，不展示信息卡片组件

    return (
      <KbPage
        {...rest}
        renderHeader={
          <View className='kb-query__header kb-spacing-md' id={this.topID}>
            <KbQueryBar placeholder='输入单号、单号后四位、手机号查快递' autoClean />
          </View>
        }
        onUpdate={this.handleUpdate}
      >
        <ScrollView
          scrollY
          className='kb-scrollview'
          onScroll={this.handleScroll}
          onScrollToLower={this.handleScrollToLower}
        >
          <View className='kb-query'>
            {process.env.MODE_ENV !== 'xyt' && <KbOfficialAccount navigateId='13' />}
            <View className='kb-box__group'>
              {/* 驿站信息卡片 */}
              {process.env.MODE_ENV !== 'xyt' && (
                <KbStoreCardUsed
                  onRelationUpdate={this.handleRelationInfoUpdate}
                  pageSource='query'
                />
              )}
              {/* 功能条 */}
              {bars && (
                <View className='kb-box at-row at-row__justify--between kb-query__bars'>
                  {bars.map((item) => (
                    <View
                      className='kb-box__item'
                      hoverClass='kb-hover'
                      key={item.icon}
                      onClick={this.handleClickBars.bind(this, item)}
                    >
                      <View className='at-row at-row__align--center'>
                        <View className='kb-icon__around kb-icon__around--small kb-icon__around--brand-lighter'>
                          <AtIcon
                            prefixClass='kb-icon'
                            value={item.icon}
                            className='kb-icon-size__base kb-icon__multicolor-brand'
                          />
                          {item.new && <View className='kb-box__item-new'>{item.new}</View>}
                        </View>
                        <View className='kb-spacing-md-l'>
                          <View className='at-row at-row__align--center'>
                            <View className='kb-box__item-label kb-color__black'>
                              {item.label}
                              {item.rightLabel && (
                                <View className='kb-box__item-rightLabel'>{item.rightLabel}</View>
                              )}
                            </View>
                            {item.tag && <View className='kb-box__item-tag'>{item.tag}</View>}
                          </View>
                          <View className='kb-size__base kb-color__grey kb-spacing-xs-t'>
                            {item.desc}
                          </View>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              )}
              {/* 广告位 */}
              <KbAdExtension />
            </View>

            {/* 取件列表 */}
            <KbList
              adUnitIdIndex='query'
              active={active}
              enableRefresh={enableRefresh}
              loadMore={loadMore}
              topID={this.topID}
              onGet={this.handleListGet.bind(this)}
            />
          </View>
        </ScrollView>
        {process.env.MODE_ENV === 'wkd' && (
          <Fragment>
            <KbUserActivity />
            {process.env.PLATFORM_ENV == 'weapp' && (
              <Fragment>
                <KbCurtain data={this.state.welfareData} />
              </Fragment>
            )}
            <KbWaitPay mode='float' />
          </Fragment>
        )}
      </KbPage>
    );
  }
}

export default Index;
