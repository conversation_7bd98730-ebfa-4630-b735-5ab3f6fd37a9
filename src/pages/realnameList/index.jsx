/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import KbLongList from '@base/components/long-list';
import { AtIcon } from 'taro-ui';
import KbPhone from '@/components/_pages/order/detail/phone';
import request from '@base/utils/request';
import KbModal from '@base/components/modal';
import { KB_SEND_REALNAMES } from '@/constants/realname';
import apis from '@/utils/apis';
import { setRealnameStatus } from '@/components/_pages/order/_utils';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '实名认证',
  };
  constructor() {
    this.state = {
      isOpened: false,
      list: undefined,
    };
    this.listData = {
      api: {
        url: apis['realname.list'],
        formatResponse: (res) => {
          const { code, data = [] } = res;
          if (code == 0 && data.length > 0) {
            return {
              data: {
                list: data,
              },
            };
          }

          return {
            data: void 0,
          };
        },
        onThen: (_, res) => {
          const { data: { list } = {} } = res || {};
          const realnameList = list ? list.map((v) => v.mobile) : [];
          Taro.kbSetGlobalData(KB_SEND_REALNAMES, realnameList);
          this.setState({
            list,
          });
        },
      },
    };
  }

  onListReady = (ins) => {
    this.listIns = ins;
  };

  handleDelete = (mobile) => {
    this.setState({
      isOpened: true,
      mobile,
    });
  };

  onConfirm = () => {
    const { mobile } = this.state;
    request({
      url: apis['realname.delete'],
      toastLoading: true,
      toastError: true,
      data: {
        mobile,
      },
    }).then((res) => {
      const { code } = res;
      if (code == 0) {
        if (process.env.MODE_ENV !== 'wkd') {
          setRealnameStatus(mobile, { realnamed: false });
        }
        this.listIns.loader();
        this.setState({
          isOpened: false,
        });
      }
    });
  };

  handleJump = (phone) => {
    Taro.navigator({
      url: 'realname',
      options: { phone },
    });
  };

  onCancel = () => {
    this.setState({
      isOpened: false,
    });
  };

  onPostMessage = (type) => {
    switch (type) {
      case 'realnameBack':
        this.listIns.loader();
        break;
    }
  };

  render() {
    const { list, isOpened, ...rest } = this.state;
    return (
      <KbPage {...rest}>
        <KbLongList
          data={this.listData}
          enableMore={false}
          onReady={this.onListReady}
          noDataText='暂未维护实名信息'
        >
          <View className='kb-list kb-realNameList'>
            {list &&
              list.map((v) => {
                const { mobile, name, no } = v || {};
                return (
                  <View key={mobile} className='kb-list__item--wrapper'>
                    <View className='kb-margin-md-lr kb-spacing-md-tb kb-border-b'>
                      <View className='at-row at-row__align--center at-row__justify--between'>
                        <View className='kb-size__32'>
                          <KbPhone mobile={mobile} />
                        </View>
                        <View
                          hoverClass='kb-hover-opacity'
                          onClick={this.handleDelete.bind(this, mobile)}
                          className='kb-spacing-sm-lr'
                        >
                          <AtIcon
                            prefixClass='kb-icon'
                            value='delete'
                            className='kb-icon-size__base kb-color__grey'
                          />
                        </View>
                      </View>
                    </View>
                    <View
                      className='kb-list__item'
                      hoverClass='kb-hover-opacity'
                      onClick={this.handleJump.bind(this, mobile)}
                    >
                      <View className='item-icon'>
                        <AtIcon
                          prefixClass='kb-icon'
                          value='realname2'
                          className='kb-color__brand'
                          size={36}
                        />
                      </View>
                      <View className='item-content'>
                        <View className='kb-size__32 kb-margin-sm-b'>{name}</View>
                        <View className='kb-color__grey kb-size__sm'>{no}</View>
                      </View>
                      <View className='item-extra '>
                        <AtIcon
                          prefixClass='kb-icon'
                          value='arrow'
                          className='kb-icon-size__base kb-color__grey'
                        />
                      </View>
                    </View>
                  </View>
                );
              })}
          </View>
        </KbLongList>
        <KbModal
          top={false}
          closable={false}
          isOpened={isOpened}
          title='删除实名'
          cancelText='确认删除'
          confirmText='我再想想'
          onConfirm={this.onCancel}
          onCancel={this.onConfirm}
        >
          <View className='kb-text__center kb-size__lg'>是否删除该条实名信息记录？</View>
        </KbModal>
      </KbPage>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
