/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCheckbox from '@base/components/checkbox';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import { Text, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtAvatar, AtButton } from 'taro-ui';

class Index extends Component {
  config = {
    navigationBarTitleText: '选择品牌',
  };

  constructor(props) {
    super(props);
    this.state = {
      data: {},
      balance: 0,
      list: [],
      selectBrands: [],
    };
    this.listData = {
      api: {
        url:
          process.env.MODE_ENV == 'wkd'
            ? '/g_wkd/v2/StopArea/supportBrandList'
            : '/StopArea/supportBrandList',
        formatResponse: (res) => {
          const { data = {}, code } = res;
          const list = [];
          if (code == 0) {
            Object.keys(data).forEach((key) => {
              list.push({
                brand: key,
                brandName: data[key],
              });
            });
          }
          return {
            data: list.length > 0 ? list : void 0,
          };
        },
        onThen: (_, { code, data }) => {
          if (code == 0) {
            this.setState({ list: data });
          }
        },
      },
    };
  }

  componentDidMount() {
    const { selectBrands } = this.$router.params;
    if (selectBrands) {
      this.setState({
        selectBrands: selectBrands.split(','),
      });
    }
  }

  onConfirm = () => {
    const { selectBrands } = this.state;
    Taro.navigator({
      post: {
        index: -2,
        type: 'brandSelect',
        data: {
          selectBrands,
        },
      },
    });
  };

  onCheckboxChange = (checked, brand) => {
    const { selectBrands = [] } = this.state;
    if (checked) {
      selectBrands.push(brand);
    } else {
      const index = selectBrands.findIndex((val) => val === brand);
      selectBrands.splice(index, 1);
    }
    this.setState({
      selectBrands,
    });
  };

  render() {
    const { list, selectBrands = [], ...rest } = this.state;
    return (
      <KbPage
        {...rest}
        renderFooter={
          <View className='kb-spacing-md kb-background__white'>
            <AtButton onClick={this.onConfirm} type='primary' circle>
              确定
            </AtButton>
          </View>
        }
      >
        <KbLongList data={this.listData} noDataText='暂无数据'>
          <View className='kb-list'>
            {list.map((item) => {
              const { brand, brandName } = item;
              return (
                <View
                  hoverClass='kb-hover'
                  className='kb-list__item--wrapper'
                  key={brand}
                  onClick={() => this.onCheckboxChange(!selectBrands.includes(brand), brand)}
                >
                  <View className='kb-list__item'>
                    <View className='item-content'>
                      <View className='at-row at-row__align--center'>
                        <AtAvatar
                          circle
                          image={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${brand}.png?v=20230314`}
                        />
                        <View className='at-col kb-spacing-md-l'>
                          <View className='item-content__title'>
                            <Text className='item-content__title--text'>{brandName}</Text>
                          </View>
                        </View>
                        <View className='item-content__extra'>
                          <KbCheckbox
                            checked={selectBrands.includes(brand)}
                            onChange={(e) => this.onCheckboxChange(e, brand)}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
