/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbArea from '@/components/_pages/closedArea/area';
import getStopAreaBrands from '@/components/_pages/closedArea/utils';
import KbAvatar from '@/components/_pages/user/avatar';
import { STOP_AREA_BRANDS } from '@/constants/business';
import { setClipboardData } from '@/utils/qy';
import { getShareAppMessage } from '@/utils/share';
import KbPage from '@base/components/page';
import KbTextarea from '@base/components/textarea';
import Form from '@base/utils/form';
import request from '@base/utils/request';
import { getStorageSync, setStorage } from '@base/utils/utils';
import { Block, Text, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtButton, AtIcon, AtTextarea } from 'taro-ui';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '停发区查询',
  };
  constructor(props) {
    super(props);

    this.state = {
      form: {
        disabled: true,
        data: {},
      },
      batchInput: false, // 批量输入
      selectBrands: [],
    };
  }

  componentDidMount() {
    this.createForm();
    const { data: stopAreaBrands = [] } = getStorageSync(STOP_AREA_BRANDS) || {};
    if (stopAreaBrands && stopAreaBrands.length > 0) {
      this.setState({
        selectBrands: stopAreaBrands,
      });
    } else {
      getStopAreaBrands(true).then((res) => {
        const brands = Object.keys(res);
        setStorage({
          key: STOP_AREA_BRANDS,
          data: brands,
        });
        this.setState({
          selectBrands: brands,
        });
      });
    }
  }

  onShareAppMessage = (e) => {
    return getShareAppMessage(e, {
      page: 'stopArea',
    });
  };

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    const form = {
      province: {
        required: true,
      },
      city: {
        required: true,
      },
      district: {
        required: true,
      },
      address: {
        required: true,
      },
      batchAddress: {
        required: false,
      },
    };
    this.formIns = new Form({
      form,
      enableEmpty: false,
      api: {
        url: process.env.MODE_ENV == 'wkd' ? '/g_wkd/v2/StopArea/search' : '/StopArea/search',
        quickTriggerThen: true,
        toastError: true,
        onCustomCheck: () => {
          const { selectBrands, form: stateForm, batchInput } = this.state;
          const { data = {} } = stateForm;
          const { province, city, district, address, batchAddress } = data;
          if (selectBrands.length == 0) {
            return { code: 11, msg: '缺少品牌' };
          }

          if (batchInput) {
            if (!batchAddress) {
              return { code: 11, msg: '缺少地址信息' };
            }
          } else {
            if (!province || !city || !district) {
              return { code: 11, msg: '缺少省市区信息' };
            }
            if (!address) {
              return { code: 11, msg: '缺少地址详细信息' };
            }
          }
        },
        formatRequest: () => {
          const { form: stateForm, selectBrands, batchInput } = this.state;
          const { data = {} } = stateForm;
          const { address, batchAddress = '', ...rest } = data;
          let addressArr = [];

          if (batchInput) {
            batchAddress
              .split(/\n|,/g)
              .filter((i) => !!i)
              .forEach((val) => {
                let obj = {};
                obj.recipient_detail = val;
                addressArr.push(obj);
              });
          } else {
            addressArr.push({
              recipient_province: rest.province,
              recipient_city: rest.city,
              recipient_district: rest.district,
              recipient_detail: address,
            });
          }

          return {
            brand: selectBrands,
            address: addressArr,
          };
        },
        onThen: (res) => {
          const { code, data } = res;
          if (code == 0 && data && isArray(data)) {
            request({
              url: process.env.MODE_ENV == 'wkd' ? '/g_wkd/v2/StopArea/share' : '/StopArea/share',
              data: { share_data: data },
              toastError: true,
              quickTriggerThen: true,
              onThen: (response) => {
                const { code, data: tokenData } = response;
                if (code == 0) {
                  Taro.navigator({
                    url: 'closedArea/result',
                    options: {
                      token: tokenData.token,
                    },
                  });
                }
              },
            });
          }
        },
      },
    });
  };

  changeBatchOrSingle() {
    const { batchInput } = this.state;
    this.setState({
      batchInput: !batchInput,
    });
  }

  onSelectBrand() {
    const { selectBrands = [] } = this.state;
    Taro.navigator({
      url: 'closedArea/selectBrand',
      options: {
        selectBrands: selectBrands.join(','),
      },
    });
  }

  onPostMessage(key, e) {
    const { province, city, district, selectBrands = [] } = e || {};
    switch (key) {
      case 'brandSelect':
        this.setState(
          {
            selectBrands,
          },
          () => {
            setStorage({
              key: STOP_AREA_BRANDS,
              data: selectBrands,
            });
          },
        );
        break;
      case 'citySelect':
        this.formIns.update({
          province,
          city,
          district,
        });
        break;

      default:
        break;
    }
  }

  onCopy = () => {
    setClipboardData('www.kbydy.cn', '网址已复制');
  };

  goWzg = () => {
    if (process.env.PLATFORM_ENV === 'weapp') {
      Taro.navigator({
        appId: 'wx9a713fd690f464c5',
        url: 'pages/closedArea/index',
      });
    }
  };

  render() {
    const { form, batchInput, selectBrands = [], mode, ...rest } = this.state;
    const { data = {} } = form;
    const selectedTop4 = [...selectBrands].splice(0, 4);
    const area = data.province
      ? {
          province: data.province,
          city: data.city,
          district: data.district,
        }
      : null;
    return (
      <KbPage
        {...rest}
        renderFooter={
          <View>
            <View className='kb-text__center kb-margin-md'>
              <View>
                电商订单批量查询推荐使用{' '}
                <Text onClick={this.goWzg} hoverClass='kb-hover' className='kb-color__brand'>
                  微掌柜
                </Text>
              </View>
              <View onClick={this.onCopy} className='kb-color__brand' hoverClass='kb-hover'>
                网页地址：www.kbydy.cn
              </View>
            </View>
            <View className='kb-spacing-md kb-background__white'>
              <AtButton circle onClick={this.onSubmit_form} type='primary'>
                确定
              </AtButton>
            </View>
          </View>
        }
      >
        <View className='kb-form'>
          <View className='kb-list kb-spacing-md-b'>
            <View className='kb-list__item--wrapper kb-list-arrow'>
              <View className='kb-list__item' hoverClass='kb-hover'>
                <View className='item-content' onClick={this.onSelectBrand}>
                  <View className='at-row at-row__align--center'>
                    <View>请选择快递品牌</View>
                    <View className='at-col brands'>
                      <View className='kb-color__grey-3 at-row at-row__align--center at-row__justify--end'>
                        {selectedTop4.map((val) => (
                          <View key={val} className='kb-margin-sm-r'>
                            <KbAvatar
                              circle
                              size='small'
                              src={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${val}.png?v=20230314`}
                            />
                          </View>
                        ))}
                        {selectBrands.length > 4 ? '...' : ''}
                      </View>
                    </View>
                  </View>
                </View>
                <View className='item-extra item-extra__line'>
                  <AtIcon
                    prefixClass='kb-icon'
                    className='kb-color__grey-3 kb-icon-size__base'
                    value='arrow'
                    color='#999'
                  />
                </View>
              </View>
            </View>
          </View>
          <View className='kb-form__item kb-spacing-lg'>
            <View className='item-content'>收件地址</View>
          </View>
          {batchInput ? (
            <View className='kb-spacing-lg kb-background__white'>
              <AtTextarea
                value={data.batchAddress}
                onChange={this.onChange_form.bind(this, 'batchAddress')}
                count={false}
                placeholder='请输入收件地址，多个请用换行分割开'
              />
            </View>
          ) : (
            <Block>
              <View className='kb-form__item kb-clear__form--item'>
                <KbArea value={area} />
              </View>
              <View className='kb-form__item'>
                <View className='item-content item-content__edit'>
                  <View className='at-row at-row__align--center'>
                    <View className='at-col'>
                      <KbTextarea
                        placeholder='详细地址'
                        value={data.address}
                        onChange={this.onChange_form.bind(this, 'address')}
                        maxLength={100}
                        count={false}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </Block>
          )}
        </View>
      </KbPage>
    );
  }
}

export default Index;
