/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import getStopAreaBrands from '@/components/_pages/closedArea/utils';
import { getShareAppMessage } from '@/utils/share';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import { Text, View } from '@tarojs/components';
import { Component } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtAvatar } from 'taro-ui';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '停发区查询结果',
  };

  constructor(props) {
    super(props);
    this.state = {
      data: {},
      balance: 0,
      list: [],
      selectBrands: [],
      active: false,
      loading: false,
    };
    const { token } = this.$router.params;
    this.listData = {
      api: {
        mastHasMobile: false,
        toastLoading: false,
        url:
          process.env.MODE_ENV == 'wkd'
            ? '/g_wkd/v2/StopArea/getShareData'
            : '/StopArea/getShareData',
        data: { token },
        formatResponse: (res) => {
          const { data = [] } = res;
          const list = this.formatRequest(data);
          return {
            data: list.length > 0 ? list : void 0,
          };
        },
        onThen: (_, { code, data }) => {
          if (code == 0) {
            this.setState({ list: data });
          }
        },
      },
    };
  }

  componentDidMount() {
    const { token } = this.$router.params;
    getStopAreaBrands().then((res) => {
      this.setState({
        brands: res,
      });
    });

    if (token) {
      this.setState({
        active: true,
      });
    }
  }

  onShareAppMessage = (e) => {
    const { token } = this.$router.params;
    return getShareAppMessage(e, {
      page: 'stopArea.result',
      info: {
        token,
      },
    });
  };

  formatRequest(data = []) {
    const list = [];
    const isBatch = data.length > 1;
    if (isArray(data)) {
      data.forEach((val = {}) => {
        const address = Object.keys(val)[0];
        const resultList = val[address] || [];
        const { address: { recipient_city, recipient_district, recipient_province } = {} } =
          resultList[0] || {};
        // 单条地址拼接详细地区
        const fullAddress = `${recipient_province}${recipient_city}${recipient_district}${address}`;
        list.push({
          address: isBatch ? address : fullAddress,
          resultList,
        });
      });
    }
    return list;
  }

  // 长列表准备完毕
  onReady = (ins) => {
    this.listIns = ins;
  };

  render() {
    const { list, brands, ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <KbLongList onReady={this.onReady} data={this.listData} noDataText='暂无数据'>
          <View className='kb-list__group'>
            {list.map((item) => {
              const { address, resultList = [] } = item;
              return (
                <View className='kb-list' key={address}>
                  <View className='kb-list__content'>
                    <View className='kb-list__title kb-list__title--fill kb-size__lg kb-spacing-md-l'>
                      {address}
                    </View>
                    {resultList.map((result) => {
                      const { brand, msg } = result;
                      return (
                        <View className='kb-list__item--wrapper kb-spacing-md-l' key={brand}>
                          <View className='kb-list__item'>
                            <View className='item-content'>
                              <View className='item-content__title'>
                                <View className='at-row at-row__align--center'>
                                  <AtAvatar
                                    className='avatar'
                                    size='small'
                                    circle
                                    image={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${brand}.png?v=20230314`}
                                  />
                                  <View className='at-col kb-spacing-md-l'>
                                    <View className='item-content__title'>
                                      <Text className='item-content__title--text'>
                                        {brands[brand]}
                                      </Text>
                                    </View>
                                  </View>
                                  <View className={`kb-color__${msg === '成功' ? 'green' : 'red'}`}>
                                    {msg === '成功' ? '可达' : '不可达'}
                                  </View>
                                </View>
                              </View>
                              {msg !== '成功' && (
                                <View className='item-content__desc reason'>原因：{msg}</View>
                              )}
                            </View>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              );
            })}
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
