/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.avatar {
  width: 40px;
  height: 40px;
  line-height: 40px;
}
.reason {
  margin-right: 100px;
  margin-left: 60px;
}

.kb-list__group {
  .kb-list {
    margin-bottom: 0;
    &__content {
      overflow: hidden;
      border-radius: $border-radius-xl;
    }
    &__title {
      border-bottom: $border-lightest;
    }
    &__item {
      padding-left: 0;
      &--wrapper {
        margin-bottom: 0;
        border-bottom: $border-lightest;
        border-radius: 0;
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
