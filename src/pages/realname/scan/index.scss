/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$border-radius-xxl: 26px;

page {
  background-color: #000;
}

.kb-scan {
  position: relative;

  &,
  &__camera,
  &__camera--wrapper {
    height: 96%;
    image {
      width: 100%;
    }
  }

  &__image {
    &--wrapper {
      position: absolute;
      top: 0;
      z-index: 5;
      width: 100%;
      background-color: #000;
    }
    image: {
      width: 100%;
    }
  }

  &__camera {
    width: 100%;

    &--wrapper {
      position: relative;
      z-index: 1;
    }

    &--canvas {
      position: fixed;
      bottom: -100%;
      left: -100%;
    }
  }

  &__border,
  &__warn,
  &__btn,
  &__position,
  &__title {
    position: absolute;
    z-index: 2;
  }

  &__title {
    top: 50px;
    left: 0;
    width: 100%;
    color: $color-white;
    text-align: center;
  }

  &__warn {
    bottom: -80px;
    width: 100%;
  }

  &__border {
    top: 50%;
    left: 50%;
    z-index: 6;
    transform: translate(-50%, -50%);

    &--idCard {
      width: 55%;
      height: 60%;
      border: 4px dashed $color-brand;
    }

    &--face {
      width: 85%;
      height: 75%;
    }

    &--square {
      width: 600rpx;
      height: 600rpx;
    }

    &--inner {
      position: absolute;
      width: 48px;
      height: 48px;
      border: 4px solid $color-brand;
    }
  }
  .left_top {
    border-right: none;
    border-bottom: none;
  }
  .right_top {
    top: 0;
    right: 0;
    border-bottom: none;
    border-left: none;
  }
  .left_bottom {
    bottom: 0;
    left: 0;
    border-top: none;
    border-right: none;
  }
  .right_bottom {
    right: 0;
    bottom: 0;
    border-top: none;
    border-left: none;
  }

  &__btn {
    position: absolute;
    bottom: -$spacing-v-xl;
    width: 100%;
    &--circle {
      width: 98px;
      height: 98px;
      border: 6px solid $color-white;
      border-radius: 50%;
    }
    &--circleInside {
      width: 74px;
      height: 74px;
      background-color: $color-white;
      border-radius: 50%;
    }
    &--flashLight {
      position: absolute;
      left: 40px;
      width: 58px;
      height: 58px;
      line-height: 58px;
      text-align: center;
      background: rgb(30, 30, 30);
      border-radius: $border-radius-circle;
    }
  }

  &__edit {
    right: $spacing-h-md;
    bottom: 200px;
    padding: $spacing-v-md $spacing-h-md;

    .kb-icon-edit {
      &::after {
        display: inline-block;
        width: 15px;
        margin-left: -10px;
        border-bottom: $width-base solid $color-white;
        content: '';
      }
    }
  }
  &__position {
    right: $spacing-h-md;
    bottom: 300px;
    padding: $spacing-v-md $spacing-h-md;
  }
}
