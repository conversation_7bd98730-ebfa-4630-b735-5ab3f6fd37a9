/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import CameraScan from '@/components/_pages/realname/camera';
import { getKdgInfo } from '@/components/_pages/store-card/_utils';
import KbPage from '@base/components/page';
import { debounce, noop, scanParse } from '@base/utils/utils';
import { Camera, Image, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '拍摄身份证',
    navigationBarTextStyle: 'white',
  };

  constructor() {
    const { type, orderInfo } = this.$router.params;
    const descVal = {
      idCard: '请横屏拍摄整张身份证',
      cabinet: '请扫描快递柜动态寄件码开门',
    };

    if (type == 'cabinet') {
      Taro.setNavigationBarTitle({
        title: '扫码开柜',
      });
    }

    this.state = {
      authCamera: false,
      canvasStyle: '',
      devicePosition: 'back',
      flash: 'true',
      tempImagePath: null,
      cameraMode: type == 'cabinet' ? 'scanCode' : 'normal',
      type,
      desc: descVal[type] || descVal.idCard,
      orderInfo: orderInfo ? JSON.parse(orderInfo) : {},
    };
    this.handleOcr = debounce(this.handleOcr);
  }

  createCamera = () => {
    this.setState({
      authCamera: true,
    });
  };

  triggerAuth = (res, then = noop) => {
    if (!res.authSetting['scope.camera']) {
      then();
    } else {
      this.createCamera();
    }
  };

  componentDidShow() {
    this.setState({
      scanStatus: false,
    });
  }

  componentDidMount() {
    Taro.setNavigationBarColor({
      backgroundColor: process.env.MODE_ENV === 'wkd' ? '#099dff' : '#1abba1',
      frontColor: '#ffffff',
    });
    Taro.getSetting()
      .then((res) => {
        this.triggerAuth(res, () => {
          if (process.env.PLATFORM_ENV === 'alipay') {
            my.showAuthGuide({
              authType: 'CAMERA',
              success: this.createCamera,
              fail: noop,
            });
          } else {
            Taro.authorize({ scope: 'scope.camera' }).then(this.createCamera).catch(noop);
          }
        });
      })
      .catch(noop);
    this.cameraIns = new CameraScan();
  }

  // 拍照
  handleOcr = () => {
    // 拍照类型
    const { type } = this.$router.params;
    const { authCamera } = this.state;
    if (!authCamera) {
      Taro.openSetting().then(this.triggerAuth).catch(noop);
      return;
    }
    if (!this.cameraIns) {
      Taro.kbToast({
        text: '请稍候',
      });
      return;
    }
    this.cameraIns.takePhoto((data) => {
      const { tempImagePath } = data || {};
      this.setState({
        tempImagePath,
      });
      Taro.kbToast(
        {
          text: '拍照成功！',
          duration: 1000,
          onClose: () => {
            Taro.navigator({
              post: {
                type: 'shotPic',
                data: {
                  type,
                  ...data,
                },
              },
            });
          },
        },
        this,
      );
    });
  };

  // 相机准备完毕
  handleCameraDown = () => {
    this.cameraIns = new CameraScan();
  };

  changeDevicePosition = () => {
    const { devicePosition } = this.state;
    this.setState({
      devicePosition: devicePosition == 'front' ? 'back' : 'front',
    });
  };

  onFlashChange = () => {
    const { flash } = this.state;
    this.setState({
      flash: flash == 'off' ? 'torch' : 'off',
    });
  };

  handleScanCode = (event) => {
    const { type, scanStatus } = this.state;

    if (scanStatus) return;
    if (type == 'cabinet') {
      const { result } = event.detail;
      if (result.includes('kdg?cabinetToken')) {
        Taro.vibrateLong({
          type: 'heavy',
        });
        this.setState({
          scanStatus: true,
        });
        const { query } = scanParse(result);
        const { cabinetToken } = query || {};
        if (cabinetToken.includes('cabinetTmp_')) {
          getKdgInfo(query, false, ({ code, data, msg }) => {
            if (code == 0) {
              // 扫码成功后处理
              this.scanCabinetSuccess(data);
            } else {
              Taro.kbModal({
                closable: false,
                content: msg,
                confirmText: '确定',
                cancelText: '',
                onConfirm: () => {
                  this.setState({
                    scanStatus: false,
                  });
                },
              });
            }
          });
        } else {
          Taro.kbModal({
            closable: false,
            content: '请扫描快宝快递柜寄件码！',
            confirmText: '重新扫码',
            cancelText: '',
            onConfirm: () => {
              this.setState({
                scanStatus: false,
              });
            },
          });
        }
      }
    }
  };

  scanCabinetSuccess = async (data) => {
    const { orderInfo } = this.state;
    console.info('下单关系 ====> 156', data);
    console.info('orderInfo=====> 159', orderInfo);
    const { relation_info = {} } = data;
    const { reserveCabinet, order_id, brand, collect_courier_id } = orderInfo || {};
    const { cabinet_id, dak_id } = relation_info;

    if (dak_id != collect_courier_id) {
      Taro.kbModal({
        closable: false,
        content: '您预约寄件的不是本驿站快递柜，不可开柜！',
        confirmText: '我知道了',
        cancelText: '',
        onConfirm: () => {
          this.setState({
            scanStatus: false,
          });
        },
      });
      return;
    }

    if (reserveCabinet != cabinet_id) {
      await this.checkCabinet();
    }
    Taro.navigator({
      url: 'kdg/express',
      target: 'self',
      options: {
        ...relation_info,
        order_id,
        brand,
        source: 'order',
      },
    });
  };

  checkCabinet = () => {
    return new Promise((resolve, reject) => {
      Taro.kbModal({
        closable: false,
        content: '您预约的寄件非本快递柜，是否变更到本快递柜下单？',
        confirmText: '确定',
        cancelText: '取消',
        onConfirm: () => {
          resolve();
        },
        onCancel: () => {
          this.setState({
            scanStatus: false,
          });
          reject();
        },
      });
    });
  };

  render() {
    const { authCamera, flash, devicePosition, tempImagePath, cameraMode, desc, type, ...rest } =
      this.state;

    return (
      <KbPage {...rest}>
        <View className='kb-scan'>
          <View className='kb-scan__title kb-size__lg'>{desc}</View>
          <View
            className={`kb-scan__border kb-scan__border--${type == 'cabinet' ? 'square' : 'face'}`}
          >
            <View className='kb-scan__border--inner left_top' />
            <View className='kb-scan__border--inner right_top' />
            <View className='kb-scan__border--inner left_bottom' />
            <View className='kb-scan__border--inner right_bottom' />
          </View>
          {(cameraMode == 'normal' || !authCamera) && (
            <View className='kb-scan__btn at-row at-row__align--center at-row__justify--around'>
              <View
                hoverClass='kb-hover-opacity'
                className='kb-scan__btn--flashLight'
                onClick={this.onFlashChange}
              >
                <AtIcon
                  prefixClass='kb-icon'
                  value='flashlight'
                  className='kb-icon-size__base kb-color__white'
                />
              </View>
              <View
                hoverClass='kb-hover-opacity'
                onClick={this.handleOcr}
                className='kb-scan__btn--circle at-row at-row__align--center at-row__justify--center'
              >
                <View className='kb-scan__btn--circleInside' />
              </View>
            </View>
          )}
          {!authCamera && (
            <View className='kb-scan__warn kb-text__center kb-color__white'>
              点击拍照，开启摄像头权限
            </View>
          )}
          <View className='kb-scan__camera--wrapper'>
            {authCamera && (
              <Camera
                id='scan_camera'
                mode={cameraMode}
                devicePosition='back'
                flash={flash}
                resolution='high'
                frameSize='large'
                className='kb-scan__camera'
                onInitDone={this.handleCameraDown}
                onReady={this.handleCameraDown}
                device-position={devicePosition}
                onScanCode={this.handleScanCode.bind(this)}
              />
            )}
          </View>
          <View className='kb-scan__image--wrapper'>
            {tempImagePath && <Image mode='widthFix' src={tempImagePath} />}
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
