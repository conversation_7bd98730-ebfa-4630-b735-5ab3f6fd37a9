/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getCardListApi } from '@/components/_pages/order/card-bar/sdk';
import KbLongList from '@base/components/long-list';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import { Component } from '@tarojs/taro';
import KbCardList from './card-list';
import './index.scss';
import { checkIsCoupon, filterCouponList } from './utils';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  constructor(props) {
    super(props);
    let {
      courier_id,
      dak_id,
      card_id = '',
      // action = 'normal',
      // 以下为微快递参数
      s_id,
      s_type,
      order_number,
      type,
      phone,
      price,
      f_weight,
      shipping_province,
    } = this.$router.params;
    const { action } = this.props;
    this.state = {
      list: [],
      disabledList: [],
      price,
    };
    this.listData = {
      pageKey: 'page_num',
      api: {
        ...getCardListApi(
          {
            action,
            card_id,
            s_id,
            s_type,
            order_number,
            type,
            phone,
            shipping_province,
            price,
            $router: this.$router,
          },
          this,
        ),
        data: {
          page_size: 30,
          pageSize: 30,
          card_id,
          dak_id,
          courier_id,
          phone,
          purge_red_spot: action === 'normal' ? 1 : 0,
          price,
          order_number,
          order_id: order_number,
        },
        onThen: (list) => {
          const filterRes = filterCouponList({
            list,
            price: this.state.price,
            f_weight,
            action,
          });
          this.setState({
            list: filterRes.list,
            disabledList: filterRes.disabledList,
            selected: card_id ? card_id : this.state.selected,
          });
        },
      },
    };
  }

  //Ins
  onReady() {
    // 非扫码进入
    // this.setState({
    //   active: true,
    // });
  }

  // 获取类型信息
  getTypeInfo = () => {
    const { type, dak_id, courier_id } = this.$router.params;
    const title = !checkIsCoupon(type) ? '权益次卡' : '优惠券';
    if (!type) return { title };
    return {
      type,
      title,
      tag: dak_id ? '驿站' : courier_id ? '快递员' : '',
    };
  };

  render() {
    const { selected, list, disabledList } = this.state;
    const { active, action, relationInfo, mode, source } = this.props;
    return (
      <KbLongList
        data={this.listData}
        enableMore={process.env.MODE_ENV !== 'wkd'}
        onReady={this.onReady.bind(this)}
        active={active}
        enableRefresh={source == 'tabs'}
      >
        <View className={source == 'tabs' ? 'kb-card__group' : ''}>
          <KbCardList
            list={action == 'normal' ? list : disabledList} // disabledList
            // onTouchItem={this.handleClickItem}
            selected={selected}
            action={action}
            relationInfo={relationInfo}
            mode={mode}
          />
        </View>
      </KbLongList>
    );
  }
}

export default Index;
