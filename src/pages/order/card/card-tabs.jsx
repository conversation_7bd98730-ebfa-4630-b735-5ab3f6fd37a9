/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { connect } from '@tarojs/redux';
import { Component } from '@tarojs/taro';
import { AtTabs, AtTabsPane } from 'taro-ui';
import KbCardListContent from './card-list-content';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '寄件优惠券',
  };

  constructor(props) {
    super(props);
    this.state = {
      active: true,
      tabs: [
        {
          title: '未使用',
          key: 'normal',
        },
        {
          title: '已过期',
          key: 'invalid',
        },
      ],
      current: 0,
    };
  }

  handleSwitchTab = (current) => {
    this.setState({
      current,
    });
  };

  render() {
    const { tabs, current, active } = this.state;

    return (
      <AtTabs
        tabList={tabs}
        onClick={this.handleSwitchTab}
        current={current}
        // height='auto'
        swipeable={false}
      >
        {tabs.map((val, index) => {
          const { key } = val;
          const mode = key == 'normal' ? 'normal' : 'disabled';
          return (
            <AtTabsPane key={key} index={index} current={current}>
              <KbCardListContent
                source='tabs'
                action={key}
                mode={mode}
                active={active && index == current}
              />
            </AtTabsPane>
          );
        })}
      </AtTabs>
    );
  }
}

export default Index;
