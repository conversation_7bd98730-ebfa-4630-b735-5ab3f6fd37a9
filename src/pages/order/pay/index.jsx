/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { getBrandsTCJS } from '@/actions/tcjsBrand';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import { getAvailableCardList } from '@/components/_pages/order/card-bar/sdk';
import KbAddressInfo from '@/components/_pages/order/detail/address-info';
import KbRelationInfo from '@/components/_pages/order/detail/relation-info';
import KbIntegralBar from '@/components/_pages/order/integral-bar';
import {
  getIntegralByMony,
  getIntegralOptimalItem,
  getValidPoints,
  integralToMoney,
} from '@/components/_pages/order/integral-bar/_utils';
import { getCourierConfig } from '@/components/_pages/order/_utils';
import {
  formatResponseOrderDetail,
  getApiUrlAndData,
} from '@/components/_pages/order/_utils/order.detail';
import { getServiceList } from '@/components/_pages/order/_utils/order.detail.service-list';
import {
  checkSwitchCoupon,
  countMoney,
  createFeeList,
  formatBatchPayResponse,
  payOrder,
  rollbackOnCancelPay,
  setFeeListItem,
} from '@/components/_pages/order/_utils/order.pay';
import {
  getQuotationFee,
  getQuotationFeeApiData,
} from '@/components/_pages/order/estimated-fee/utils';
import { filterCouponList } from '@/pages/order/card/utils';
import { app_uid } from '@/utils/config';
import { mergeParamsByScan } from '@/utils/scan';
import KbAntForest from '@base/components/ant-forest';
import KbCountDownClock from '@base/components/count-down-clock';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import KbSubscribe from '@base/components/subscribe';
import KbSwitch from '@base/components/switch';
import logger from '@base/utils/logger';
import { createCompletePath } from '@base/utils/navigator';
import {
  createListener,
  debounce,
  getPage,
  getSessionIDFromLoginData,
  noop,
} from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isObject from 'lodash/isObject';
import isString from 'lodash/isString';
import qs from 'qs';
import { AtAvatar, AtIcon } from 'taro-ui';
import KbInputNumber from '@base/components/input-number';
import './index.scss';

@connect(
  ({ global }) => ({
    loginData: global.loginData,
    brands: global.brands,
    brands_tcjs: global.brands_tcjs,
  }),
  { get, getBrandsTCJS },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '支付',
  };
  constructor() {
    mergeParamsByScan(this.$router);
    const { params } = this.$router;
    const { type: orderType, batchNo } = params;
    const isBatchPay = !!batchNo;
    this.state = {
      feeList: [],
      reckonInfo: null,
      integral: {
        disabled: false,
        num: 0,
        money: 0,
        points_kind: null,
      },
      coupon: { money: 0, num: 0, id: '', type: 'coupon' },
      serviceMoney: 0,
      relationInfo: null,
      data: {},
      orderType,
      disabledPay: true,
      integralRefresh: new Date().getTime(),
      write: 0, //手动输入的价格
      courierConfig: null,
      isBatchPay, // 是否批量收款
    };
    //会获取预估运费
    //有些下单对象不支持编辑运费，例如微快递同城急送、微商支付等，需要增加开关
    //订单中如果返回price不能编辑订单，适用于推送账单的场景
    this.canEditFee = !['tcjs', 'wzg'].includes(orderType);
    this.listData = {
      api: isBatchPay
        ? {
            url: '/g_wkd/v2/GrabOrder/getBatchPayOrderInfo',
            data: {
              batch_no: batchNo,
            },
            nonceKey: 'batch_no',
            formatResponse: ({ data }) => formatBatchPayResponse({ data, batchNo }),
            onThen: (_, { code, data }) => {
              if (code == 0) {
                const { relationData, price } = data || {};
                if (!price || price <= 0) {
                  // 订单已经支付过跳转订单列表
                  Taro.navigator({
                    url: 'order',
                    target: 'self',
                  });
                  return;
                }
                this.setState({
                  disabledPay: price > 0 ? false : true,
                  relationInfo: relationData,
                  data,
                });
              }
            },
          }
        : {
            ...getApiUrlAndData(params),
            formatResponse: ({ data }, req) =>
              formatResponseOrderDetail({ data, orderType }, req, this),
            onThen: (_, { data }) => {
              if (!data) return;
              if (data.relationData && data.relationData.type === 'courier') {
                getCourierConfig({ courier_id: data.relationData.id }).then((courierConfig) => {
                  this.setState({
                    courierConfig,
                  });
                });
              }
              let param = getQuotationFeeApiData(data);
              getQuotationFee(param, {
                formatRequest: false,
                ignore: !this.canEditFee,
              }).then(({ data: reckonFee, code }) => {
                const feeList =
                  process.env.MODE_ENV == 'wkd' && this.canEditFee
                    ? []
                    : createFeeList({ ...reckonFee, ...data }, { orderType });
                const hasReckon = code === 0 && isObject(reckonFee);
                let state = {
                  data,
                  relationInfo: data.relationData,
                  feeList,
                  disabledPay: false,
                  write:
                    data.freight && data.freight > 0
                      ? data.freight
                      : (reckonFee && reckonFee.price) || 0,
                };
                hasReckon && (state.reckonInfo = reckonFee);
                this.setState(state, () => {
                  this.getCardList().then((list) => this.setDefaultDiscount(list));
                  // 获取增值服务列表
                  getServiceList(data, { orderType })
                    .then((serviceData) => {
                      const { extraText, money: serviceMoney = 0, list } = serviceData;
                      if (orderType === 'tcjs') {
                        extraText &&
                          this.setState({
                            feeList: createFeeList(data, {
                              orderType,
                              extraText,
                            }),
                          });
                      } else {
                        this.setState({
                          serviceMoney,
                          feeList: [
                            ...feeList,
                            ...list
                              .filter((item) => item.isActualcost)
                              .map(({ name, price }) => setFeeListItem(name, price)),
                          ],
                        });
                      }
                    })
                    .catch((err) => console.log(err));
                });
              });
            },
          },
    };
    this.handlePay = debounce(this.handlePay);
    this.handleWriteMoney = debounce(this.handleWriteMoney, 300, {
      trailing: true,
    });
  }
  setDefaultDiscount = debounce(
    (resList) => {
      Promise.all([
        this.updateCouponOrIntegral(null, 'integral'),
        this.updateCouponOrIntegral({
          id: '',
        }),
      ]).then(() => {
        const { integral = {}, couponList: list } = this.state;
        const { price } = this.getPriceAndType();
        const couponList = resList || this.filterCouponList(price, list);
        if (couponList.length) {
          let correspond = couponList[0];
          // 当没有权益次卡时，获得最大面值的优惠券并使用（权益次卡索引位始终靠前）
          correspond.card_type === 'coupon' &&
            couponList.forEach((item) => {
              const { discount_fee } = item;
              const { discount_fee: current } = correspond;
              if (parseFloat(discount_fee) > current) {
                correspond = item;
              }
            });
          this.updateCoupon({ num: couponList.length, ...correspond });
        } else if (price > 0 && !integral.num) {
          this.updateIntegral('default');
        }
      });
    },
    { trailing: true },
  );
  // 列表加载完毕
  onReady = (ins) => {
    this.listIns = ins;
  };
  onPluginPayCallback = (e) => {
    this.payCallback = () => {
      this.timer && clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.toastIns && this.toastIns.close();
        if (e.type === 'success') {
          this.handleResult();
        } else {
          this.refreshCoupon();
          const { detail } = e;
          const { code, msg } = detail || {};
          code && Taro.kbToast({ text: msg });
        }
        this.payCallback = null;
      }, 500);
    };
  };

  // 切换优惠券
  handleSwitchCoupon = () => {
    const { isBatchPay } = this.state;
    if (isBatchPay) {
      Taro.kbToast({
        text: '批量支付时，无法使用优惠券',
      });
      return;
    }
    this.triggerCheckSwitchCoupon('coupon')
      .then(() => {
        const {
          data: { order_id, customer_id },
          coupon: { id: current },
          relationInfo,
          reckonInfo,
        } = this.state;
        const { dakId: dak_id, id: courier_id, phone } = relationInfo || {};
        const { price, type: priceType } = this.getPriceAndType();
        Taro.navigator({
          url: 'order/card',
          options: {
            action: 'select',
            order_number: order_id,
            customer_id,
            courier_id,
            phone,
            f_weight: this.getFirstWeightFee() * 1,
            dak_id,
            current,
            type: !reckonInfo || priceType == 'order' ? 'coupon' : null,
            price,
          },
        });
      })
      .catch((err) => console.log(err));
  };
  componentDidShow() {
    if (this.payCallback) {
      this.payCallback();
    } else {
      this.toastIns && this.toastIns.close();
    }
  }
  componentDidMount() {
    this.props.get();
    if (process.env.MODE_ENV === 'wkd') {
      this.props.getBrandsTCJS();
    }
    if (process.env.MODE_ENV === 'third.pro' || process.env.MODE_ENV === 'third.post') {
      this.payPlugin = Taro.requirePlugin('payPlugin');
      const { setPayCallback } = this.payPlugin;
      setPayCallback && setPayCallback(this.onPluginPayCallback);
    }
  }
  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
  }

  // 获取到的积分项
  handleIntegralChange = (data) => {
    if (data) {
      const city_shop = data.city_shop;
      if (city_shop && city_shop.points > 0) {
        city_shop.points_kind = 1;
        data.multipleIntegral = true;
      }
      data.inn && (data.inn.points_kind = 0);
    }
    this.setState({ integralData: data }, () => {
      this.setDefaultDiscount();
    });
  };
  //筛出可用的积分项并格式化
  filterIntegral = (data) => {
    const { points, id, points_kind } = data || {};
    const integral = {};
    integral.num = points;
    integral.points_id = id;
    integral.points_kind = points_kind;
    return points ? integral : null;
  };
  updateIntegral = (data, callback) => {
    const change =
      data === 'default'
        ? this.filterIntegral(getIntegralOptimalItem(this.state.integralData))
        : !data
        ? null
        : data;
    this.updateCouponOrIntegral(change, 'integral').then(() => {
      callback && callback();
    });
  };
  // 切换积分
  handleSwitchIntegral = () => {
    const {
      integralData,
      integral: { points_id, points_kind },
    } = this.state;
    const { multipleIntegral, max_points } = integralData || {};
    if (multipleIntegral) {
      Taro.navigator({
        url: `order/integral`,
        options: {
          data: qs.stringify(integralData),
          action: 'select',
          selected: points_kind,
        },
      });
      createListener('integralSelect', (data) => {
        if (data) {
          this.triggerCheckSwitchCoupon('integral')
            .then(() => {
              this.updateIntegral(this.filterIntegral(getValidPoints(data, max_points)));
            })
            .catch((err) => console.log(err));
        } else {
          this.updateIntegral(null);
        }
      });
    } else {
      if (!points_id) {
        this.triggerCheckSwitchCoupon('integral').then(() => {
          this.updateIntegral('default');
        });
      } else {
        this.updateIntegral(null);
      }
    }
  };

  // 微快递暂不支付
  handleNotPay = () => {
    if (process.env.MODE_ENV == 'wkd') {
      const { $router: { path = '' } = {} } = getPage(-2);
      Taro.navigator(
        path.includes('order/edit')
          ? {
              url: 'order/result',
              target: 'self',
              options: this.$router.params,
            }
          : null,
      );
    } else {
      this.handleResult('submit');
    }
  };

  // 跳转支付结果页
  handleResult = (status = 'pay') => {
    const {
      data: { order_id, is_open_order },
      isBatchPay,
    } = this.state;
    if (isBatchPay) {
      // 批量收款跳转订单列表
      Taro.navigator({
        url: 'order',
        target: 'self',
      });
      return;
    }

    let url = 'order/result';
    let options = {
      order_id,
      status,
    };
    const { $router: { path = '' } = {} } = getPage(-2) || {};
    const { orderType } = this.state;

    if (process.env.MODE_ENV === 'wkd') {
      options = {
        order_id,
        source: 'pay',
      };
      switch (orderType) {
        case 'wzg':
          url = 'order/detail';
          break;
        case 'tcjs':
          const {
            params: { brand },
          } = this.$router;
          options = {
            ...options,
            source: orderType,
            brand,
          };
          if (is_open_order == 1) {
            url = 'order/detail';
            options = {
              ...options,
              type: orderType,
              brand,
            };
          }
          break;
        default:
          break;
      }
    }
    if (
      path === createCompletePath(url) ||
      (path !== '/pages/order/edit/index' && orderType != 'tcjs')
    ) {
      console.log('上一页与目标页相同或者上一页并不是下单页面，则直接返回');
      // 上一页与目标页相同或者上一页并不是下单页面，则直接返回
      Taro.navigator({
        post: {
          type: 'paySuccess',
          data: {
            status: 'success',
          },
        },
      });
      return;
    }
    Taro.navigator({
      url,
      options,
      target: 'self',
    });
  };

  refreshCoupon() {
    this.setState({
      integralRefresh: new Date().getTime(),
    });
    this.getCardList().then((list) => this.setDefaultDiscount(list));
  }
  // 支付
  handlePay = () => {
    const MODE_ENV = process.env.MODE_ENV || 'yz';
    this.setState({ disabledPay: true });
    const fee = this.innCountMoney();
    const { loginData } = this.props;
    const ori_sessionid = getSessionIDFromLoginData(loginData, false);
    if (
      (process.env.PLATFORM_ENV === 'alipay' && MODE_ENV === 'third.pro') ||
      (MODE_ENV !== 'third.pro' && MODE_ENV !== 'third.post') ||
      Number(fee) === 0
    ) {
      payOrder({
        ori_sessionid,
        pageParams: this.$router.params,
        ...this.state,
      })
        .then(this.handleResult)
        .catch((error) => {
          const reqData = Taro.kbGetGlobalDataOnce('pay_req');
          const { order_id, coupon_id, equity_cardId } = reqData || {};
          const signFail = error && error.code > 0 && error.code != 2903;
          const userCancel = error && isString(error) && error.includes('cancel');
          if (signFail || userCancel) {
            logger.info('支付失败退券', signFail, userCancel, order_id, coupon_id || equity_cardId);
            rollbackOnCancelPay(order_id, coupon_id || equity_cardId);
          }
          this.setState({ disabledPay: false });
          if (userCancel) return;
          Taro.kbToast({
            text: error.message || error.msg,
          });
        });
    } else {
      const { setPayData } = this.payPlugin || {};
      let extraData = {};
      const {
        data,
        coupon: { id: coupon_id, type: couponType },
        integral: { num: points, points_id, points_kind },
        reckonInfo,
      } = this.state;
      const { price, type: priceType } = this.getPriceAndType();
      const { relationData, courier_id } = data;
      const { order_id, order_random } = this.$router.params;
      extraData.ori_sessionid = ori_sessionid;
      extraData.fee = fee;
      extraData.ori_app_uid = app_uid;
      if (relationData) {
        extraData.relationName =
          relationData.name + (relationData.type === 'dak' ? '驿站' : '快递员');
      }
      extraData.paymentArgs = {
        price,
        order_id: order_id || data.order_id,
      };
      const concatPaymentArgs = (res) => {
        extraData.paymentArgs = { ...extraData.paymentArgs, ...res };
      };
      if (reckonInfo && priceType === 'manual') {
        concatPaymentArgs({ ...reckonInfo, price });
      }
      if (coupon_id) {
        concatPaymentArgs({
          [couponType === 'coupon' ? 'coupon_id' : 'card_id']: coupon_id,
        });
      } else if (points_id && points_kind) {
        const orderPrice = parseFloat(price);
        const availablePoints = getIntegralByMony(points, orderPrice);
        concatPaymentArgs({
          points: availablePoints,
          points_id,
          points_kind,
        });
      }
      if (courier_id) {
        concatPaymentArgs({ courier_id });
      }
      if (order_random) {
        concatPaymentArgs({ order_random });
      }

      const params = { ...extraData };
      setPayData && setPayData(params);
      Taro.navigateTo({
        url: `plugin://payPlugin/pay`,
      });
      this.toastIns = Taro.kbToast({
        status: 'loading',
        text: '支付中...',
      });
      this.setState({ disabledPay: false });
    }
  };

  // 切换积分优惠券，先验证是否需要清空另一项
  triggerCheckSwitchCoupon = (type) => {
    return checkSwitchCoupon(type, this.state, this).then((types) => {
      if (types) {
        this.updateCouponOrIntegral(null, types[0]);
      }
      return types;
    });
  };

  filterCouponList = (price, list = this.state.couponList) => {
    return filterCouponList({ list, price, f_weight: this.getFirstWeightFee() * 1 }).list;
  };
  // 根据state中的复合参数计算最终价格
  innCountMoney = () => {
    const { orderType, data, integral, coupon, serviceMoney, reckonInfo } = this.state;
    const { price: fee, type: priceType } = this.getPriceAndType();
    const availablePoints = getIntegralByMony(integral.num, fee);
    const pointsMoney = integralToMoney(availablePoints);
    return countMoney({
      orderType,
      data,
      integral: { ...integral, integralMoney: pointsMoney },
      coupon,
      serviceMoney,
      reckonInfo,
      priceType,
      fee,
    });
  };
  getPriceAndType = (state = this.state) => {
    const { data: { price: orderPrice, pay_type } = {}, write } = state;
    const { price: sharePrice } = this.$router.params;
    /**
     * orderPrice 推送账单的价格
     * write 手动输入价格
     * price 报价单价格
     * sharePrice 订单分享价格
     */
    const isOrderType = process.env.MODE_ENV === 'wkd' ? orderPrice : orderPrice && pay_type == 0;
    const priceType = isOrderType ? 'order' : 'manual';
    return {
      type: priceType,
      price: parseFloat(sharePrice ? sharePrice : priceType === 'order' ? orderPrice : write),
    };
  };
  // 获取优惠券列表(包含权益次卡)
  getCardList = () => {
    return new Promise((resolve, reject) => {
      const {
        orderType,
        data: { order_id, use_preferential, second_pay_order, activeEquityId, customer_id },
        relationInfo,
        reckonInfo,
      } = this.state;
      if (orderType === 'tcjs' || !!use_preferential) return reject();
      const { dakId, id, phone } = relationInfo || {};
      const { price, type: priceType } = this.getPriceAndType();
      if (!dakId && !id && !phone) return reject();
      const params = {
        page_size: 30,
        dak_id: dakId,
        courier_id: id,
        phone,
        order_id,
        order_number: order_id,
        orderPrice: price,
        price,
        customer_id,
      };
      if (activeEquityId) return;
      (!reckonInfo || priceType == 'order' || !!second_pay_order) && (params.type = 'coupon');
      getAvailableCardList({ ...params })
        .then((list) => {
          let resList = this.filterCouponList(price, list);
          this.setState({ couponList: list }, () => {
            resolve(resList);
          });
          this.updateCouponOrIntegral({
            num: resList.length,
          });
        })
        .catch(() => reject);
    });
  };

  // 更新优惠券或积分
  updateCouponOrIntegral = (data, type = 'coupon') => {
    const defaultDataMap = {
      coupon: { id: '', type: 'coupon' },
      integral: { points_id: '', num: 0, points_kind: null },
    };
    return new Promise((resolve) => {
      const state = this.state[type];
      const update = {
        ...state,
        ...(data || defaultDataMap[type]),
      };

      this.setState(
        {
          [type]: update,
        },
        () => {
          resolve();
        },
      );
    });
  };
  updateCoupon = (data) => {
    const { discount_fee: money = 0, card_id, record_id, card_type, num = 0 } = data || {};
    const id = card_id || record_id;
    this.updateCouponOrIntegral({
      money,
      id,
      num,
      type: card_type === 'discount_card' ? 'discount_card' : 'coupon',
    });
  };
  // 监听数据传递
  onPostMessage = (type, data) => {
    switch (type) {
      case 'cardSelect':
        this.updateCoupon(data);
        break;
    }
  };
  handleWriteMoney = (value, ev) => {
    const { reckonInfo } = this.state;
    const { price = 0 } = reckonInfo || {};
    let finalVal = (ev.type == 'blur' && !value ? price : value) * 1 || 0;
    const resList = this.filterCouponList(finalVal);
    this.updateCouponOrIntegral({
      num: resList.length,
      id: '',
    });
    this.setState(
      {
        write: finalVal,
      },
      () => {
        this.setDefaultDiscount(resList);
      },
    );
  };

  getFirstWeightFee() {
    const { reckonInfo } = this.state;
    const { range_one = {} } = reckonInfo || {};
    return range_one.f_weight;
  }

  render() {
    const {
      disabledPay,
      feeList,
      orderType,
      integral,
      coupon,
      relationInfo,
      data,
      reckonInfo,
      disable_reason,
      integralRefresh,
      integralData,
      write,
      courierConfig,
      isBatchPay,
      ...rest
    } = this.state;
    const { brands_tcjs } = this.props;
    const { f_weight, f_kg, f_fee, s_weight, s_kg, s_fee, update_price } = reckonInfo || {};
    const relationType = relationInfo ? relationInfo.type : null;
    // 使用权益次卡
    const useEquityCard = coupon && coupon.type == 'discount_card' && coupon.id;
    const { price: _fee, type: priceType } = this.getPriceAndType();
    const {
      use_preferential,
      activeEquityId,
      list: orderList = [],
      is_open_order,
      pay_packing_money,
    } = data;
    const fee = (_fee * 1000 - pay_packing_money * 1000) / 1000;
    const availableIntegral = this.filterIntegral(getIntegralOptimalItem(integralData));
    const { multipleIntegral } = integralData || {};
    const integralCls = classNames('kb-navigator kb-navigator-noborder kb-pay__item', {
      'kb-clear__pseudo-ele-after': !multipleIntegral,
    });
    const availablePoints = getIntegralByMony(
      multipleIntegral ? integral.num : availableIntegral ? availableIntegral.num : 0,
      fee,
    );
    const pointsMoney = integralToMoney(availablePoints);
    const total = this.innCountMoney();

    const isUsedDiscounts = !!use_preferential;
    const couponLabelCls = classNames('kb-navigator-noborder kb-navigator', {
      'kb-clear__after': activeEquityId,
    });
    let disableEquityCard =
      priceType == 'manual' && reckonInfo && write < this.getFirstWeightFee() * 1;
    const addressInfoCls = classNames('kb-box kb-pay__order-info kb-margin-lg-lr', {
      'kb-pay__order-info-batch': isBatchPay,
    });
    return (
      <KbPage
        {...rest}
        renderFooter={
          <View className='kb-spacing-md'>
            {process.env.MODE_ENV === 'wkd' ? (
              <Fragment>
                <KbAntForest />
                {data.currentTime && data.waitPayEndTime && (
                  <View className='at-row at-row__align--center at-row__justify--center kb-spacing-md-b kb-size__base'>
                    <Text>请在</Text>
                    <View className='kb-pay-clock kb-color__red'>
                      <KbCountDownClock
                        start={data.currentTime * 1000}
                        end={data.waitPayEndTime * 1000}
                        as='asMinutes'
                      />
                      <Text>分钟内</Text>
                    </View>
                    <Text>完成支付，超时将自动取消订单</Text>
                  </View>
                )}
              </Fragment>
            ) : (
              ''
            )}
            <View className='at-row at-row__justify--around'>
              {this.canEditFee && (
                <View className='at-col kb-margin-sm-lr'>
                  <KbSubscribe
                    type='secondary'
                    className='kb-button__middle kb-pay__notPay'
                    onSubscribe={this.handleNotPay}
                    circle
                  >
                    {process.env.MODE_ENV !== 'wkd' && (priceType == 'order' || !reckonInfo)
                      ? '取消支付'
                      : '暂不支付'}
                  </KbSubscribe>
                </View>
              )}

              <View className='at-col kb-margin-sm-lr'>
                <KbSubscribe
                  type='primary'
                  className='kb-button__middle'
                  onSubscribe={this.handlePay}
                  action='pay'
                  disabled={
                    (orderType != 'tcjs' &&
                      (fee && (integral.num || coupon.num || useEquityCard)
                        ? total < 0
                        : total <= 0)) ||
                    disabledPay
                  }
                  circle
                >
                  确认支付
                </KbSubscribe>
              </View>
            </View>
          </View>
        }
      >
        <KbLongList data={this.listData} onReady={this.onReady}>
          <View className='kb-order-wrap kb-spacing-lg-tb'>
            <View className='at-row at-row__align--center kb-color__white at-row__justify--center'>
              <AtIcon
                prefixClass='kb-icon'
                value='unpaid'
                className='kb-color__white kb-icon-size__lg'
              />
              订单待支付
            </View>
          </View>
          {data.order_id && (
            <View className='kb-spacing-md kb-order-content'>
              <View className='kb-box'>
                {orderType !== 'tcjs' && (
                  <View className='kb-pay__title'>
                    费用详情
                    {isBatchPay ? (
                      <Text className='kb-color__brand'>(共{data.order_count}单)</Text>
                    ) : null}
                  </View>
                )}
                {this.canEditFee && (
                  <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-lr kb-fee-write'>
                    <View>预估运费</View>
                    <View className='kb-pay__editFreight'>
                      {priceType == 'manual' &&
                      (process.env.MODE_ENV == 'yz'
                        ? update_price == 1
                        : !courierConfig || courierConfig.isModifyPrice == 1) ? (
                        <Fragment>
                          <KbInputNumber
                            unit=''
                            step={0.01}
                            mode={2}
                            border={false}
                            min={0}
                            max={20000}
                            className='input'
                            type='digit'
                            placeholder='可手动填写'
                            placeholderClass='kb-color__black'
                            cursor={-1}
                            onChange={this.handleWriteMoney}
                            value={fee}
                            controlled
                          />
                          <AtIcon
                            className='kb-color__grey'
                            prefixClass='kb-icon'
                            value='edit'
                            size='18'
                          />
                        </Fragment>
                      ) : (
                        <Text className='kb-spacing-sm-t'>¥{fee}</Text>
                      )}
                    </View>
                  </View>
                )}
                {pay_packing_money && (
                  <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg kb-fee-write'>
                    <View>包装费</View>
                    <View>¥{pay_packing_money}</View>
                  </View>
                )}
                {priceType === 'manual' && reckonInfo && (
                  <View className='kb-fee-detail kb-tip kb-margin-lg-lr kb-spacing-sm'>
                    <Text className='kb-spacing-xl-r'>
                      首重({f_weight}元/{f_kg}kg): {f_fee || 0}元
                    </Text>
                    <Text>
                      续重({s_weight}元/{s_kg}kg): {s_fee || 0}元
                    </Text>
                  </View>
                )}
                {feeList.map((item) => (
                  <View
                    key={item.name}
                    className='at-row at-row__align--center at-row__justify--between kb-pay__item'
                  >
                    <View>{item.name}</View>
                    <View>{item.info}</View>
                  </View>
                ))}
                {orderType !== 'tcjs' && (
                  <Fragment>
                    {!isUsedDiscounts && (
                      <View
                        className={couponLabelCls}
                        hoverClass={activeEquityId || disableEquityCard ? '' : 'kb-hover'}
                        onClick={
                          activeEquityId || disableEquityCard
                            ? noop
                            : this.handleSwitchCoupon.bind(this)
                        }
                      >
                        <View className='kb-navigator__label'>抵扣券</View>
                        {useEquityCard || activeEquityId ? (
                          <View className='kb-color__red'>已使用权益次卡抵扣首重</View>
                        ) : coupon.num ? (
                          <View className='kb-color__red'>
                            {coupon.id ? (
                              <Text>-￥{coupon.money}</Text>
                            ) : (
                              <Text>{coupon.num}张可用</Text>
                            )}
                          </View>
                        ) : (
                          <View className='kb-color__grey'>
                            {disable_reason || '暂无可用抵扣券'}
                          </View>
                        )}
                      </View>
                    )}

                    {fee && !isUsedDiscounts ? (
                      <Fragment>
                        {process.env.MODE_ENV !== 'wkd' ? (
                          <KbIntegralBar
                            onChange={this.handleIntegralChange}
                            mode='custom'
                            dakId={relationInfo.dakId}
                            isOpened={availableIntegral}
                            refresh={integralRefresh}
                          >
                            <View
                              hoverClass={multipleIntegral ? 'kb-hover' : ''}
                              className={integralCls}
                              onClick={this.handleSwitchIntegral}
                            >
                              <View className='kb-navigator__label'>积分抵扣</View>
                              <Fragment>
                                {multipleIntegral ? (
                                  <Fragment>
                                    {availablePoints ? (
                                      <View className='kb-color__red'>
                                        使用{availablePoints}积分抵扣
                                        {pointsMoney}元
                                      </View>
                                    ) : (
                                      <View>选择积分进行抵扣</View>
                                    )}
                                  </Fragment>
                                ) : (
                                  <Fragment>
                                    <KbSwitch
                                      title={`可用${availablePoints}积分抵扣${pointsMoney}元`}
                                      checked={!!integral.points_id}
                                      onChange={this.handleSwitchIntegral}
                                      disabled={integral.disabled}
                                    />
                                  </Fragment>
                                )}
                              </Fragment>
                            </View>
                          </KbIntegralBar>
                        ) : (
                          ''
                        )}
                      </Fragment>
                    ) : null}

                    <View className='at-row at-row__align--center at-row__justify--between kb-pay__total'>
                      <View>合计</View>
                      <View>¥{total}</View>
                    </View>
                  </Fragment>
                )}
              </View>
            </View>
          )}
          {orderType === 'tcjs' && data.brand && is_open_order == 1 ? (
            <Fragment>
              <View className='kb-box kb-spacing-md kb-margin-md-lr kb-margin-md-b'>
                <View className='at-row at-row__align--center at-row__justify--between'>
                  <View>下单品牌</View>
                  <View>
                    <View className='kb-brand-avatarList at-row'>
                      {data.brand.split(',').map((item) => {
                        return (
                          <AtAvatar
                            key={item}
                            image={brands_tcjs[item] && brands_tcjs[item].logo_link}
                            circle
                            size='small'
                          />
                        );
                      })}
                    </View>
                  </View>
                </View>
              </View>
              <View className='kb-color__grey kb-size__base kb-margin-md'>
                温馨提示：多品牌发单时，系统将按照品牌最大运费值进行支付，当实际接单品牌运费小于支付金额时，系统会在订单配送完成后自动退回差价
              </View>
            </Fragment>
          ) : null}
          <View className={addressInfoCls}>
            {!['tcjs', 'wzg'].includes(orderType) && relationInfo ? (
              <KbRelationInfo
                data={process.env.MODE_ENV === 'wkd' ? data : relationInfo}
                type={relationType}
                className={
                  process.env.MODE_ENV !== 'wkd' && relationType == 'dak'
                    ? 'kb-spacing-md-lr kb-spacing-md-t'
                    : ''
                }
              />
            ) : (
              ''
            )}
            {isBatchPay ? (
              <View>
                {orderList.map((item) => {
                  return (
                    <View className='address'>
                      <View className='address-time'>{item.create_at}</View>
                      <View className='address-info'>
                        <KbAddressInfo data={item} border />
                      </View>
                    </View>
                  );
                })}
              </View>
            ) : (
              <View className='kb-spacing-xl-tb kb-spacing-md-lr'>
                {data.order_id && <KbAddressInfo data={data} border />}
              </View>
            )}
          </View>
          <View className='kb-margin-lg-lr kb-margin-lg-b'>
            <KbExternalAd adUnitIdIndex='order.pay' wrapper />
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
