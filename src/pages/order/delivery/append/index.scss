/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-append {
  &-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-md $spacing-h-md;
    background: $color-white;
    .at-button {
      height: 80rpx;
      font-size: $font-size-lg;
    }
  }
  &-list {
    &--title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-v-md $spacing-h-md;
    }
    &--list {
      .kb-estimatedFeeList-list-item {
        margin: 0;
        color: $color-grey-2;
        background: $color-white;
        &::after {
          top: 0;
          right: $spacing-h-md;
          bottom: auto;
          left: $spacing-h-md;
        }
      }
    }
  }
  &-fee {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 $spacing-h-md;
  }
  .kb-brand-avatarList {
    display: flex;
    .at-avatar {
      margin-left: -$spacing-h-lg;
    }
  }
}
