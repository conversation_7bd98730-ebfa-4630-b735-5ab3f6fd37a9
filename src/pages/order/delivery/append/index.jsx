/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbEstimatedFeeContent from '@/components/_pages/delivery/estimated-fee/content';
import { getQuotation } from '@/components/_pages/delivery/estimated-fee/utils.js';
import KbTipFee from '@/components/_pages/delivery/tip-fee';
import { transferTcjsAddress } from '@/components/_pages/delivery/_utils';
import { requestPayment } from '@/utils/qy';
import KbNoticeBar from '@base/components/notice-bar';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import request from '@base/utils/request';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef } from '@tarojs/taro';
import { AtAvatar, AtButton } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  brands: global.brands_tcjs,
}))
class Index extends Component {
  static options = {
    addGlobalClass: true,
  };
  config = {
    navigationBarTitleText: '追加运力/小费',
  };
  constructor(props) {
    super(props);
    this.state = {
      tipsFee: '0',
      chooseBrand: [],
      quotationList: [],
      loading: false,
    };
    this.tipFeeRef = createRef();
  }

  componentDidMount() {
    this.getList();
  }

  getList() {
    const orderData = Taro.kbGetGlobalData('order_data');
    this.setState({
      orderData,
      loading: true,
    });
    const { brands } = this.props;
    const { brand } = orderData || {};
    const brandArr = brand ? brand.split(',') : [];
    getQuotation({ brands, addrData: transferTcjsAddress(orderData), extraInfo: orderData }).then(
      ({ quotation }) => {
        this.setState({
          loading: false,
          quotationList:
            quotation && quotation.length > 0
              ? quotation
                  .map((item) => {
                    if (brandArr.includes(item.brand)) {
                      item.available = 0;
                      item.unavailable_msg = '已发单运力不可取消';
                      item.selected = 1;
                    }
                    return item;
                  })
                  .filter((item) => item.available != 0)
              : [],
        });
      },
    );
  }

  onPostMessage(key, e) {
    console.log('key, e', key, e);
    switch (key) {
      case 'routerParamsChange':
        break;
    }
  }

  //处理子组件数据更新
  handleChange = (key, e) => {
    const { tipsFee, selected } = e || {};
    switch (key) {
      case 'tipsFee':
        this.setState({ tipsFee });
        break;
      case 'chooseBrand':
        this.setState({
          chooseBrand: selected,
        });
        break;
    }
  };

  countMoney() {
    const { chooseBrand = [], orderData = {}, quotationList = [], tipsFee } = this.state;
    const { pay } = orderData || {};
    let val = 0;
    let maxBrandFee = 0;
    if (quotationList && quotationList.length > 0) {
      quotationList.map((item) => {
        if (chooseBrand.includes(item.brand)) {
          if (item.total_fee * 1 > maxBrandFee) {
            maxBrandFee = item.total_fee;
          }
        }
      });
    }
    const deliveryFee = maxBrandFee * 1 > pay * 1 ? maxBrandFee * 1 - pay * 1 : 0;
    val = deliveryFee * 1 + tipsFee * 1;
    return val && val > 0 ? val.toFixed(2) : 0;
  }

  handleSubmit = () => {
    const { chooseBrand, tipsFee, orderData } = this.state;
    const { order_id } = this.$router.params;
    const { brand } = orderData || {};
    const brandArr = brand ? brand.split(',') : [];
    let arr = chooseBrand.filter((i) => !brandArr.includes(i));
    const appendBrand = arr && arr.length > 0;
    const appendTipsFee = tipsFee && tipsFee > 0;
    request({
      url: '/g_wkd/v2/rushOrder/Order/appendBrand',
      data: {
        order_id,
        brand: arr ? arr.join(',') : '',
        tips: tipsFee > 0 ? tipsFee : '',
      },
      toastError: true,
      onThen: ({ code, data }) => {
        const completeFn = () => {
          let text = '成功';
          if (appendBrand) {
            text = `已追加${arr.length}家运力品牌，请耐心等待平台分配`;
          } else if (appendTipsFee) {
            text = `已追加${tipsFee}元小费，请耐心等待平台分配`;
          }
          Taro.kbToast({
            text,
            onClose: () => {
              Taro.navigator();
            },
          });
        };
        if (code == 0) {
          if (data && (data.appId || data.trade_no)) {
            requestPayment(data)
              .then(completeFn)
              .catch((e) => {
                console.log('e', e);
              });
          } else {
            completeFn();
          }
        }
      },
    });
  };

  render() {
    const { chooseBrand, orderData, tipsFee, loading, quotationList, ...rest } = this.state;
    const { brands } = this.props;
    const hasChooseBrand = (orderData && orderData.brand) || '';

    return (
      <KbPage {...rest}>
        <KbScrollView
          renderFooter={
            <View className='kb-append-footer'>
              <View>
                <Text>费用：</Text>
                <Text className='kb-color__red'>￥</Text>
                <Text className='kb-size__xl kb-color__red'>{this.countMoney()}</Text>
              </View>
              <AtButton circle onClick={this.handleSubmit} type='primary'>
                追加运力/小费
              </AtButton>
            </View>
          }
        >
          <View className='kb-append'>
            <KbNoticeBar>追加运力/小费，可更快接单</KbNoticeBar>
            <View className='kb-append-list kb-box kb-margin-md'>
              <View className='kb-append-list--title'>
                <View>已选运力</View>
                <View>
                  <View className='kb-brand-avatarList at-row'>
                    {hasChooseBrand
                      ? hasChooseBrand.split(',').map((item) => {
                          return (
                            <AtAvatar
                              key={item}
                              image={brands[item].logo_link}
                              circle
                              size='small'
                            />
                          );
                        })
                      : null}
                  </View>
                </View>
              </View>
              <View className='kb-append-list--list'>
                <KbEstimatedFeeContent
                  loading={loading}
                  brands={brands}
                  quotationList={quotationList}
                  onChange={this.handleChange.bind(this)}
                />
              </View>
            </View>
            <View
              className='kb-append-fee kb-box kb-margin-md'
              onClick={() => {
                this.tipFeeRef.current.open();
              }}
              hoverClass='kb-hover-opacity'
            >
              <View>给骑手加小费</View>
              <View className='kb-color__grey'>
                {tipsFee > 0 ? `小费:${tipsFee}元` : '不加小费'}
              </View>
            </View>
            <KbTipFee
              actionRef={this.tipFeeRef}
              current={tipsFee}
              onChange={(e) => this.handleChange('tipsFee', e)}
            />
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
