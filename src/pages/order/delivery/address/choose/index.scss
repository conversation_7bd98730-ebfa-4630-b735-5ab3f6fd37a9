/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-address {
  &__search {
    display: flex;
    align-items: center;
    padding: $spacing-v-md $spacing-h-md;
    background: $color-white;
    &--city {
      display: flex;
      flex-wrap: nowrap;
      .text {
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .rotate {
      transform: rotate(90deg);
    }
  }
}
