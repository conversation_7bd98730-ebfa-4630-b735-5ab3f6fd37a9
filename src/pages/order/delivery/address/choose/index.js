/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { formatDistance } from '@/components/_pages/delivery/_utils';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import KbSearch from '@base/components/search';
import { resolveAddressByLocation } from '@base/utils/gps';
import { debounce } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtIcon } from 'taro-ui';
import './index.scss';

class Index extends Taro.Component {
  static config = {
    navigationBarTitleText: '选择地址',
    backgroundColorTop: '#f2f2f2',
  };

  constructor() {
    super(...arguments);
    console.log('this.$router.params', this.$router.params);
    const { type, province, city, district, address, location } = this.$router.params;
    this.state = {
      list: [],
      type,
      province,
      city,
      district,
      address,
      location,
      keywords: null,
    };
    this.listData = {
      api: {
        url: '/g_wkd/v1/rush/Rush/getRound',
        data: {
          location,
          offset: 10,
          address,
        },
        formatResponse: ({ data: list }) => {
          if (isArray(list) && list.length > 0) {
            return {
              data: { list },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          this.setState({
            list,
          });
        },
      },
    };
    this.handleSearch = debounce(this.handleSearch, 500, {
      leading: false,
      trailing: true,
    });
  }

  componentDidMount() {
    this.updateTitle();
    const { search } = this.$router.params;
    if (search) {
      this.handleSearch(search);
    }
  }

  updateTitle() {
    const { type } = this.state;
    Taro.setNavigationBarTitle({
      title: `选择${type == 'send' ? '寄件' : '收件'}地址`,
    });
  }

  onReady = (ins) => {
    this.listIns = ins;
  };

  // 监听
  onPostMessage = (key, data) => {
    switch (key) {
      case 'citySelect':
        const { province, city, district } = data;
        let address = province + city + district;
        this.setState({
          province,
          city,
          district,
          address,
          location: '',
        });
        this.listIns.loader({
          address,
          location: '',
        });
        break;
    }
  };

  handleSearch(value) {
    const { city, address } = this.state;
    if (value) {
      this.listIns.loader(
        {
          keywords: value,
          cityName: city,
          address: address,
        },
        {
          url: '/g_wkd/v1/rush/Rush/getInputTips',
        },
      );
    } else {
      this.listIns.loader(
        {
          keywords: '',
          cityName: '',
          address,
        },
        {
          url: '/g_wkd/v1/rush/Rush/getRound',
        },
      );
    }
  }

  handleClick(item) {
    Taro.navigator({
      post: {
        type: 'addressSelect_choose',
        data: item,
      },
    });
  }

  onChangeCity = () => {
    Taro.navigator({
      url: 'city',
    });
  };

  handleMapAddr() {
    // 地图选点
    const { province, city, district, location, list } = this.state;
    let listLocation = '';
    if (list && list.length > 0) {
      listLocation = list[0].location;
    }
    let resLocation = location || listLocation;
    const locationArr = resLocation ? resLocation.split(',') : [];
    if (!province || !city || !district) {
      Taro.kbToast({
        text: '请先选择城市',
      });
      return;
    }
    Taro.chooseLocation({
      longitude: locationArr[0] * 1,
      latitude: locationArr[1] * 1,
      success: (res) => {
        resolveAddressByLocation(res).then((gpsInfo) => {
          if (gpsInfo) {
            gpsInfo.name = gpsInfo.name || res.name;
            const { name, address, latitude, longitude } = gpsInfo;
            Taro.navigator({
              post: {
                type: 'addressSelect',
                data: {
                  province: gpsInfo.province,
                  city: gpsInfo.city,
                  district: gpsInfo.district,
                  address,
                  location: `${longitude},${latitude}`,
                  name,
                },
              },
            });
          }
        });
      },
    });
  }

  render() {
    const { type, list, city, ...reset } = this.state;
    const { search } = this.$router.params;
    const placeholder = `请输入${type == 'send' ? '寄件' : '收件'}地址`;
    return (
      <KbPage
        {...reset}
        renderHeader={
          <View className='kb-address__search'>
            <View className='kb-address__search--city kb-spacing-md-r' onClick={this.onChangeCity}>
              <Text className='text kb-spacing-sm-r'>{city ? city : '选择城市'}</Text>
              <AtIcon
                className='kb-color__brand rotate'
                prefixClass='kb-icon'
                value='triangle-right'
                size='14'
              />
            </View>
            <KbSearch
              placeholder={placeholder}
              showSearchButton={false}
              clear={false}
              defaultValue={search}
              onChange={this.handleSearch.bind(this)}
            />
            <View
              className='kb-text__center kb-size__sm kb-margin-sm-l'
              onClick={this.handleMapAddr.bind(this)}
              hoverClass='kb-hover-opacity'
            >
              地图选址
            </View>
          </View>
        }
      >
        <KbLongList
          noDataText='没有地址信息哦~'
          data={this.listData}
          onReady={this.onReady}
          enableMore
        >
          <View className='kb-list'>
            {list.map((item) => (
              <View
                key={item.name}
                className='kb-list__item--wrapper'
                onClick={this.handleClick.bind(this, item)}
                hoverClass='kb-hover'
              >
                <View className='kb-list__item'>
                  <View className='item-content'>
                    <View className='item-content__title'>{item.name}</View>
                    <View className='item-content__desc'>
                      {item.district}
                      {item.address}
                    </View>
                  </View>
                  <View className='item-checkbox'>{formatDistance(item.distance)}</View>
                </View>
              </View>
            ))}
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
