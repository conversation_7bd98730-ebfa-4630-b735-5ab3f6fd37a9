/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { addressKeys, getFormItem, prefixMap } from '@/components/_pages/delivery/_utils';
import KbAddressAiEdit from '@/components/_pages/order/address-edit/edit-ai';
import KbCheckbox from '@base/components/checkbox';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import Form from '@base/utils/form';
import gpsSDK from '@base/utils/gps';
import { View } from '@tarojs/components';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtButton, AtInput } from 'taro-ui';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '地址编辑',
  };

  constructor(props) {
    super(props);
    const { type, id, action } = this.$router.params;
    // action = create 新增，可选是否保存 / createAndSave 创建并自动保存 / update 编辑更新
    this.state = {
      type,
      action: action ? action : id ? 'update' : 'create',
      id,
      addressName: '',
      location: '',
      isSave: true,
    };
    this.addressAiEditRef = createRef();
  }

  componentDidMount() {
    const {
      type,
      name,
      mobile,
      province,
      city,
      district,
      address,
      house_num,
      longitude,
      latitude,
    } = this.$router.params;
    Taro.setNavigationBarTitle({
      title: `填写${prefixMap[type]}地址`,
    });
    this.createForm(() => {
      if (type == 'send' && !longitude && !name && !mobile) {
        this.onLocation();
      } else {
        this.setState({
          addressName: address,
          location: `${longitude},${latitude}`,
        });
        this.formIns.update({
          name,
          mobile,
          province,
          city,
          district,
          address,
          house_num,
          longitude,
          latitude,
        });
      }
    });
  }

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onThen) => {
    const { type, action, id } = this.state;
    const form = getFormItem({
      keys: addressKeys,
    });
    this.formIns = new Form({
      form,
      api: {
        url: action == 'update' ? '/g_wkd/v1/rush/Address/update' : '/g_wkd/v1/rush/Address/create',
        quickTriggerThen: true,
        toastError: true,
        toastSuccess: false,
        formatRequest: (req) => {
          if (action == 'update') {
            req.id = id;
          }
          req.type = type;
          req.is_top = '0';
          req.house_number = req.house_num;
          return req;
        },
        onIntercept: () => {
          const { isSave } = this.state;
          if ((action == 'create' && !isSave) || this.submitAction === 'addressListSubmit') {
            return {
              code: 0,
              data: {},
            };
          }
          return false;
        },
        onThen: (res, req) => {
          if (res.code == 0) {
            const { extraData } = req || {};
            const [extraData0] = extraData || [];
            let id = res.data.id || req.id || (extraData0 && extraData0.id) || '';
            Taro.navigator({
              post: {
                type: 'editAddress',
                data: {
                  type,
                  id,
                  address: req,
                },
              },
            });
          }
        },
      },
      onReady: onThen,
    });
  };

  // 地址信息输入变化
  handleChange = (e) => this.formIns.update(e.data);

  handleAiEditChange = (data, dataSource) => {
    const fillForm = (data) => {
      let formData = isArray(data) ? data[0] : data;
      if (dataSource === 'list') {
        // 从地址薄选择的完整地址
        this.formIns.update(formData);
        this.submitAction = 'addressListSubmit';
        this.onSubmit_form({ id: formData.id });
      } else {
        const { name, mobile } = formData;
        this.formIns.update({
          name,
          mobile,
        });
        this.addressAiEditRef.current.clear();
        if (!formData.address || !formData.city) {
          Taro.kbToast({
            text: '未能识别出有效地址信息',
          });
          return;
        }
        this.onJumpTo(formData);
      }
    };
    if (data == 'clear') {
      this.formIns.clean();
    } else {
      fillForm(data);
    }
  };

  // 监听
  onPostMessage = (key, data) => {
    // console.log('key, data', key, data)
    switch (key) {
      case 'addressSelect':
      case 'addressSelect_choose':
        // 从poi搜索地址列表选择数据
        const { name, location, province, city, district, address } = data;
        const [longitude, latitude] = location ? location.split(',') : [];
        this.setState({
          addressName: name,
          location,
        });
        this.formIns.update({
          province,
          city,
          district,
          address,
          longitude,
          latitude,
        });
        break;
    }
  };

  onLocation() {
    gpsSDK()
      .then((res) => {
        console.log('gpsSDK.res', res);
        const { latitude, longitude, province, city, district, address } = res;
        this.setState({
          addressName: address,
          location: `${longitude},${latitude}`,
        });
        this.formIns.update({
          province,
          city,
          district,
          address,
          longitude,
          latitude,
        });
      })
      .catch(() => {});
  }

  onJumpTo = (oFormData) => {
    console.log('oFormData', oFormData);
    const {
      type,
      location,
      form: { data: formData },
    } = this.state;
    const { province, city, district, address } = oFormData || formData || {};
    const address_poi = address || `${province}${city}${district}`;
    Taro.navigator({
      url: 'order/delivery/address/choose',
      options: {
        type,
        location: oFormData ? oFormData.location || '' : location,
        province,
        city,
        district,
        address: address_poi,
        search: oFormData ? address_poi : '',
      },
    });
  };

  onSwitchAgree = (isSave) => {
    this.setState({
      isSave,
    });
  };

  render() {
    const {
      type,
      action,
      addressName,
      form,
      form: { disabled, data: formData = {} } = {},
      isSave,
      ...rest
    } = this.state;
    const { pageSource } = this.$router.params;
    const { name, mobile, district, address, house_num } = formData || {};
    const typeText = prefixMap[type];
    console.log('formData', formData);
    return (
      <KbPage {...rest}>
        <KbScrollView
          renderFooter={
            <View className='kb-spacing-md'>
              <AtButton type='primary' disabled={disabled} onClick={this.onSubmit_form} circle>
                保存并使用
              </AtButton>
            </View>
          }
        >
          <View>
            <KbAddressAiEdit
              source='tcjs'
              actionRef={this.addressAiEditRef}
              type={type}
              supportAi={
                pageSource === 'addressList'
                  ? ['ai', 'camera', 'mic']
                  : ['ai', 'camera', 'mic', 'book']
              }
              stopAutoAiCopyBoard
              manualAi
              placeholder='输入或粘贴文本，自动识别姓名、电话和地址，多条收件地址请换行输入'
              onFormDataUpdate={this.handleAiEditChange}
              className='kb-background__white kb-margin-md kb-spacing-md kb-border-radius-lg'
            />
          </View>
          <View className='kb-form kb-spacing-md'>
            <View className='kb-form__item' onClick={() => this.onJumpTo()}>
              <View className='kb-navigator at-row'>
                <View className='kb-navigator__content kb-spacing-md-l'>
                  {!addressName ? (
                    <View>请选择{typeText}地址</View>
                  ) : (
                    <Fragment>
                      <View>{addressName}</View>
                      <View className='kb-size__sm kb-color__grey kb-margin-sm-t'>
                        {district}
                        {address}
                      </View>
                    </Fragment>
                  )}
                </View>
              </View>
            </View>
            <View className='kb-form__item'>
              <AtInput
                placeholderClass='placeholder'
                cursor={-1}
                value={house_num}
                placeholder='请输入楼层/门牌号'
                onChange={this.onChange_form.bind(this, 'house_num')}
              />
            </View>
            <View className='kb-form__item'>
              <AtInput
                placeholderClass='placeholder'
                cursor={-1}
                value={name}
                placeholder='请输入姓名'
                onChange={this.onChange_form.bind(this, 'name')}
              />
            </View>
            <View className='kb-form__item'>
              <AtInput
                placeholderClass='placeholder'
                cursor={-1}
                value={mobile}
                type='text'
                placeholder='请输入手机号'
                onChange={this.onChange_form.bind(this, 'mobile')}
              />
            </View>
            {action == 'create' && (
              <View className='kb-form__item kb-spacing-md-tb'>
                <KbCheckbox
                  label='保存到地址库'
                  checked={isSave}
                  onChange={this.onSwitchAgree}
                  className='kb-color__black'
                />
              </View>
            )}
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
