/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getFormItem, goodsKeys } from '@/components/_pages/delivery/_utils';
import { requestGoodsList } from '@/components/_pages/delivery/_utils/goods';
import Kb<PERSON>istBox from '@/components/_pages/order/listbox';
import KbButton from '@base/components/button';
import KbInputNumber from '@base/components/input-number';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import KbTextarea from '@base/components/textarea';
import Form from '@base/utils/form';
import { debounce } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import './index.scss';

class Index extends Component {
  static options = {
    addGlobalClass: true,
  };
  config = {
    navigationBarTitleText: '物品信息',
  };
  constructor(props) {
    super(props);
    this.weightRange = [0.1, 999];
    this.state = {
      loading: false,
      form: { data: {}, disabled: true },
      packages: [],
      selectted: '',
    };
    this.onPackageInfoAction = debounce(this.onPackageInfoAction, 0, {
      trailing: true,
    });
    this.handleDebounceChange = debounce(this.handleDebounceChange, 100, {
      trailing: true,
    });
  }

  onPostMessage(key, e) {
    const { params } = e;
    switch (key) {
      case 'routerParamsChange':
        this.onPostChange(params);
        break;
    }
  }

  onPostChange(params) {
    const { data = {} } = params || {};
    this.createForm(() => {
      const { clean = false, ...rest } = data;
      if (clean) {
        this.formIns.clean();
        return;
      }
      this.formIns.update(rest);
      this.setPackageInfo();
    });
  }

  // 表单输入变化
  createForm = (cb) => {
    const form = getFormItem({
      keys: [...goodsKeys],
      data: { goods_name: '' },
      merge: {
        goods_name: { required: true },
      },
    });
    this.formIns = new Form(
      {
        form,
        enableEmpty: false,
        onUpdate: (data) => {
          const {
            data: { goods_weight: changeWeight },
            formEvent,
          } = data;
          if (formEvent === 'blur' && changeWeight) {
            return;
          }
        },
        onReady: () => {
          cb && cb();
        },
      },
      this,
    );
  };
  onChange_form = () => {};
  onSubmit_form = () => {};

  // 设置物品类型列表
  setPackageInfo = (then) => {
    requestGoodsList().then((GoodList = []) => {
      const packages = GoodList && GoodList.length > 0 ? GoodList : [];
      this.setState({
        packages: packages.map((item) => {
          return {
            ...item,
            label: item.title,
          };
        }),
      });
      then && then(packages);
    });
  };

  // 选择物品类型
  onPackageInfoAction = (key, value) => {
    let {
      form: {
        data: { goods_name: selectted },
      },
    } = this.state;
    switch (key) {
      case 'select':
        // 勾选物品类型
        const { label } = value;
        if (selectted === label) return;
        this.formIns.update({
          goods_name: label,
        });
        break;
    }
  };

  // 处理物品重量
  handleDebounceChange = (key, value) => {
    this.onChange_form(key, { target: { value }, type: 'change' });
  };

  // 处理物品备注
  handleRemarkSelect = (item) => {
    const {
      form: { data },
    } = this.state;
    this.onChange_form('goods_remark', {
      target: { value: data.goods_remark + item.label },
      type: 'change',
    });
  };

  // 提交物品信息
  handleSubmit = () => {
    const jump = (data) => {
      if (!data.goods_name) {
        Taro.kbToast({ text: '请选择物品信息' });
        return;
      }
      this.formIns.submit(data);
      Taro.navigator({
        post: {
          type: 'routerParamsChange',
          data: {
            source: 'goods',
            data,
          },
        },
      });
    };
    const {
      form: { data },
    } = this.state;
    jump(data);
  };

  render() {
    const {
      form: { data },
      packages,
      ...rest
    } = this.state;

    return (
      <KbPage {...rest}>
        <KbScrollView
          renderFooter={
            <View className='kb-spacing-lg-tb kb-margin-lg-lr'>
              <KbButton circle onClick={this.handleSubmit} type='primary'>
                确定
              </KbButton>
            </View>
          }
        >
          <View className='kb-extra-info'>
            <View className='kb-form__group'>
              <View className='kb-form__item--content kb-form-box'>
                <View className='kb-form-tag'>物品类型</View>
                <View>
                  <KbListBox
                    className='kb-goods__list kb-spacing-lg-b'
                    list={packages}
                    onChange={this.onPackageInfoAction.bind(this, 'select')}
                    selectted={data.goods_name}
                  />
                </View>
              </View>
              <View className='item-extra__line kb-spacing-md-r kb-form-box kb-form__item--content'>
                <View className='kb-form-tag'>物品重量</View>
                <View className='at-row at-row__align--center at-row__justify--between kb-margin-lg-tb'>
                  <Text className='kb-color__grey kb-size__sm'>注：实际重量以快递员确定为准</Text>
                  <KbInputNumber
                    className='kb-input-center'
                    width={120}
                    step={1}
                    max={999}
                    placeholder='重量'
                    alwaysEmbed
                    value={data.goods_weight || ''}
                    type='digit'
                    onChange={this.handleDebounceChange.bind(this, 'goods_weight')}
                  />
                </View>
              </View>
              <View className='kb-form__item--content  kb-form-box '>
                <View className='kb-form-tag'>填写备注</View>
                <View className='item-content item-content__right'>
                  <KbTextarea
                    placeholder='补充说明～'
                    className='kb-spacing-lg kb-margin-lg-t kb-background-grey'
                    maxLength='40'
                    value={data.goods_remark}
                    onChange={this.onChange_form.bind(this, 'goods_remark')}
                  />
                </View>
              </View>
            </View>
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
