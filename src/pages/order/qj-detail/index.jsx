/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbAddressInfo from '@/components/_pages/order/qj-detail/address-info';
import KbGoodsInfo from '@/components/_pages/order/qj-detail/goods-info';
import KbOrderStatus from '@/components/_pages/order/qj-detail/order-status';
import KbPayInfo from '@/components/_pages/order/qj-detail/pay-info';
import { formatResponseOrderDetail } from '@/components/_pages/order/qj-detail/utils';
import { refreshControl, REFRESH_KEY_ORDER } from '@/utils/refresh-control';
import KbEmpty from '@base/components/empty';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import { debounce } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import './index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    onGet: get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '订单详情',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
  };
  constructor() {
    const { params } = this.$router;
    const { out_order_no } = params;

    this.state = {
      switchBrandData: null,
      waybill: '',
      isOpened: false,
      data: {},
      isOpened: false,
    };
    this.listData = {
      api: {
        url: '/g_order_core/v2/Orders/getOrderIdByTradeNumber',
        data: {
          trade_number: out_order_no,
        },
        formatResponse: ({ data }) => {
          return formatResponseOrderDetail(data, false);
        },
        onThen: (_, { data }) => {
          if (!data) return;
          this.setState({
            data,
          });
        },
      },
    };
    this.handleRefresh = debounce(this.handleRefresh);
    Taro.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline'],
    });
  }

  componentDidMount() {
    Taro.hideHomeButton();
  }

  componentDidShow() {
    this.handleRefresh('show');
  }

  triggerRefreshControl = (status) => {
    // 触发刷新后标记订单列表需要刷新
    if (status === 'show') return;
    refreshControl(REFRESH_KEY_ORDER);
  };

  onPostMessage = (type) => {
    switch (type) {
      case 'temporaryComplete':
        this.handleRefresh();
        break;
      case 'paySuccess':
        this.handleRefresh();
        break;
      case 'realnameBack':
        this.handleRefresh();
        break;
      default:
        break;
    }
  };

  handleRefresh = (status) => {
    if (this.listIns) {
      this.listIns.loader();
      this.triggerRefreshControl(status);
    }
  };

  // 列表加载完毕
  handleReady = (ins) => {
    this.listIns = ins;
  };

  handleUpdate = (data) => {
    if (!data.logined) return;
    this.props.onGet();
  };

  render() {
    const { data, isOpened, ...rest } = this.state;
    const { addressInfo, statusInfos, brandInfo, goodsInfo, payInfo } = data || {};

    return (
      <KbPage {...rest} onUpdate={this.handleUpdate}>
        <KbLongList
          useRenderEmpty
          renderEmpty={<KbEmpty centered description='该订单无法查看' />}
          data={this.listData}
          onReady={this.handleReady}
        >
          {data.order_number && (
            <View className='kb-detail kb-spacing-md-b'>
              <KbOrderStatus data={{ ...statusInfos }} />
              <KbAddressInfo data={{ ...addressInfo, ...brandInfo, ...statusInfos }} />
              <KbGoodsInfo data={{ ...goodsInfo }} />
              <KbPayInfo data={payInfo} statusInfos={statusInfos} />
            </View>
          )}
        </KbLongList>
      </KbPage>
    );
  }
}

export default Index;
