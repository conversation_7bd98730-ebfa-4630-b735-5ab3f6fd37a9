/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-box-center {
  text-align: center;
}

.kb-box {
  position: relative;

  &__item {
    padding: $spacing-v-xl $spacing-h-md;
    border-bottom: $border-lightest;

    &:last-child {
      border-bottom: 0;
    }

    &--package {
      padding: 0;
    }

    &--title {
      box-sizing: border-box;
      padding: $spacing-v-xl $spacing-h-md;
    }
  }
}

.kb-detail {
  &__status {
    font-weight: bold;
    font-size: 34px;
  }

  .kb-order-detail__tag .at-tag {
    padding: 0 $spacing-h-sm;
    border-radius: 0 $border-radius-lg;
  }

  .kb-detail-yhj__tag {
    position: absolute;
    top: 0;
    left: 0;
    padding: $spacing-v-xs $spacing-h-md;
    color: $color-white;
    background-color: $color-brand;
    border-radius: $border-radius-lg 0 $border-radius-lg 0;
  }

  &__title {
    padding-right: 65px;
  }

  &__package {
    display: flex;
    border-top: $border-lightest;

    &--item {
      flex-grow: 1;
      box-sizing: border-box;
      padding: $spacing-v-md $spacing-h-xl;
      color: $color-grey-2;
      font-size: $font-size-xs;
      text-align: center;
    }

    &--text {
      padding-top: $spacing-v-sm;
    }

    &--goods_name {
      max-width: 150px;
      margin: 0 auto;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &--value {
      white-space: nowrap;
    }

    &--pic__bar {
      display: inline;
    }
  }
}
.kb-detail--icon {
  position: relative;
  &::after {
    position: absolute;
    top: 0;
    right: 44px;
    display: inline-block;
    color: $color-grey-3;
    font-size: 30px;
    font-family: 'kb-icon';
    content: '\e60c';
  }
}
.kb-platband-top {
  border-top: $border-lightest;
}
