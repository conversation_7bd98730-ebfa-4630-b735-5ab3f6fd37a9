/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { receiveStorageKey } from '@/components/_pages/address/_utils';
import { getPureSaveAddress, saveAddress } from '@/components/_pages/address/_utils/address.edit';
import KbAddressEdit from '@/components/_pages/order/address-edit/index';
import KbCardBar from '@/components/_pages/order/card-bar';
import KbScanCoupon from '@/components/_pages/order/coupon/scan-coupon';
import {
  getQuotationFee,
  getQuotationFeeApiData,
} from '@/components/_pages/order/estimated-fee/utils';
import KbBrandInfo from '@/components/_pages/order/extra-info/brand';
import KbBrandCashCheckbox from '@/components/_pages/order/extra-info/brandCashCheckbox';
import KbExtraEnter from '@/components/_pages/order/extra-info/entrance';
import { verifyDynamicForm } from '@/components/_pages/order/extra-info/_utils';
import Kb<PERSON>eeList from '@/components/_pages/order/fee-list';
import KbIntegralBar from '@/components/_pages/order/integral-bar';
import KbPrintMore from '@/components/_pages/order/print-more';
import KbSubmitBar from '@/components/_pages/order/submit-bar';
import KbUnPayOrders from '@/components/_pages/order/unPayOrders';
import {
  batchSubmitOrder,
  fixDynamicFormsData,
  getForm,
  getRealnameStatus,
  globalOrderIDs,
  isFresh,
  orderAction,
} from '@/components/_pages/order/_utils';
import {
  getEditDefaultFormData,
  getUnPayOrders,
  guideRealName,
} from '@/components/_pages/order/_utils/order.edit';
import KbStoreCardSelector from '@/components/_pages/store-card/selector';
import { checkIsBrand } from '@/components/_pages/store-card/_utils';
import KbCurtainActivity from '@/components/_pages/welfare/activity/curtain/edit';
import { verifyServiceForm } from '@/components/_pages/_utils/comm.order';
import { refreshControl, REFRESH_KEY_ORDER } from '@/utils/refresh-control';
import { scanAction } from '@/utils/scan';
import { save } from '@base/actions/global';
import KbLoginAuthAndBind from '@base/components/login/authAndBind';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import ServiceAgreement from '@base/components/service-agreement';
import Form from '@base/utils/form';
import request from '@base/utils/request';
import {
  checkIsReLogin,
  debounce,
  extractData,
  getPage,
  getUserStateByComplete,
  mergeBySpace,
  noop,
  removeStorage,
  reportAnalytics,
  setStorage,
} from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import isObject from 'lodash/isObject';
import isString from 'lodash/isString';
import numeral from 'numeral';
import qs from 'qs';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import KbDeclarationAgreement from '~/components/_pages/order/edit/declaration';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
  serviceConfig: global.serviceConfig,
  ...global,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '寄件',
    backgroundColorTop: '#f2f2f2',
  };
  constructor(props) {
    super(props);

    this.state = {
      extraInfo: getEditDefaultFormData(),
      extraPropData: null,
      isComplete: false,
      feeList: [],
      feeData: {},
      isOpened: false,
      isOpenedFee: false,
      order_id: '',
      relationInfo: {
        dakId: '',
        courierId: '',
        relation_id: '',
      },
      realnameTips: false,
      reckonFee: '--',
      getting: false,
      form: { data: {}, disabled: true },
      dynamicForms: fixDynamicFormsData(),
      agree: false,
      addressData: null,
      total: 1,
      cardInfo: null,
      curtainShow: false,
      isOpenedPay: false, // 是否开启支付
      closedPay: false,
      scanCoupon: '',
    };
    this.showEquityCard = !(
      process.env.PLATFORM_ENV === 'alipay' && process.env.MODE_ENV === 'third.pro'
    );
    this.getConfig = debounce(this.getConfig, 300, { trailing: true });
    this.getReckonFee = debounce(this.getReckonFee, 300, {
      trailing: true,
    });
    this.checkRealname = debounce(this.checkRealname, 300, {
      trailing: true,
    });
    this.countFee = debounce(this.countFee, 300, { trailing: true });
    this.addressEditRef = createRef();
    this.customerBar = createRef();
  }

  componentDidMount() {
    const { dakId } = this.$router.params;
    dakId && Taro.kbUpdateRelationInfo({ dakId });
    this.createForm();
    this.getConfig();
  }
  componentDidUpdate(preProps) {
    const { loginData: nextLoginData } = this.props;
    const { loginData } = preProps;
    checkIsReLogin(nextLoginData, loginData, this.getConfig);
  }
  componentWillReceiveProps(preProps) {
    const { serviceConfig: nextServiceConfig } = this.props;
    const { serviceConfig } = preProps;
    if (nextServiceConfig !== serviceConfig) {
      const { extraInfo: { goods_name, ...rest } = {} } = this.state;
      const { isFresh: freshConfig } = nextServiceConfig || {};
      if (!freshConfig && isFresh(goods_name)) {
        this.setState({
          extraInfo: {
            ...rest,
            goods_name: '日用品',
          },
        });
      }
    }
  }
  componentDidShow() {
    // 兼容支付宝隐藏元素无法获取高度
    this.getOrderInfo();
  }

  handleCashCheckBoxChange = (value) => {
    this.setState({
      checkedCash: value,
    });
  };
  // 更新下单关系数据
  updateRelationInfo = (data, then) => {
    const { placeOrderConfig, dynamicForms } = data || {};
    let state = {
      relationInfo: {
        dakId: '',
        courierId: '',
        relation_id: '',
        ...data,
      },
    };
    state.dynamicForms = fixDynamicFormsData(dynamicForms ? dynamicForms : {});
    if (isObject(placeOrderConfig)) {
      // 中邮特殊体检报告下单关系
      const { goodsType, remarkAs, remarkPlaceholder, brand, brandName } = placeOrderConfig;
      state.dynamicForms = fixDynamicFormsData({
        goods_name: {
          value: goodsType,
          desc: goodsType,
          locked: true,
        },
        goods_weight: {
          isShow: false,
        },
        goods_remark: {
          placeholder: remarkPlaceholder,
          label: remarkAs,
          maxLength: 30,
        },
        brand: {
          value: brand,
          desc: brandName,
          locked: true,
        },
        service: {
          arrive_pay: {
            disabled: true,
          },
        },
      });
    }
    if (state.relationInfo.type !== 'customer') {
      state.customerInfo = {};
    }

    this.setState({ ...state }, then);
  };
  getSupportPay = (relation_id) => {
    const { loginData: { logined } = {} } = this.props;
    if (!logined) return;
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/minpost/order/supportPay',
        directTriggerThen: true,
        data: { relation_id },
        onThen: ({ code, data }) => {
          resolve(!code);
          if (code === 0) {
            //不支持在线付时可以使用权益次卡
            this.setState({
              closedPay: !data,
            });
          }
        },
      });
    });
  };
  // 获取是否允许支付
  getConfig = () => {
    const { loginData: { logined } = {} } = this.props;
    if (!logined) return;
    if (process.env.MODE_ENV == 'wkd') {
      let isOpenedPay = true;
      this.setState({
        isOpenedPay,
      });
    } else {
      request({
        url: '/api/weixin/mini/user/Config/getConfig',
        toastLoading: false,
        onThen: ({ data: { pay_status } = {} }) => {
          const isOpenedPay = pay_status == '0';
          this.setState({
            isOpenedPay,
          });
        },
      });
    }
  };
  // 获取运单详情
  getOrderInfo = () => {
    const order_id = Taro.modifyOrderId;
    this.setState(
      {
        order_id,
      },
      () => {
        if (order_id && !this.editLocked) {
          request({
            url: '/api/weixin/mini/minpost/MiniDak/orderInfo',
            toastError: true,
            data: {
              order_id,
            },
            nonceKey: process.env.MODE_ENV === 'yz' ? 'third_order_id,order_id,order_random' : '',
            onThen: ({ code, data }) => {
              if (code == 0 && data) {
                const {
                  pay_status,
                  receive_mobile,
                  receive_tel,
                  send_mobile,
                  send_tel,
                  dak_id: dakId,
                  courier_id: courierId,
                  brand_id: brandId,
                } = data;
                const addressInfo = extractData(data, [
                  ['send_mobile', () => mergeBySpace(send_mobile, send_tel)],
                  ['receive_mobile', () => mergeBySpace(receive_mobile, receive_tel)],
                ]);
                const goodsInfo = extractData(data, [
                  'brand',
                  'goods_name',
                  'goods_remark',
                  'goods_weight',
                ]);
                this.updateRelationInfo({
                  dakId,
                  courierId,
                  brandId,
                });
                Taro.kbUpdateRelationInfo({ dakId });
                this.setState({
                  addressData: {
                    ...data,
                    ...addressInfo,
                  },
                  extraInfo: {
                    ...goodsInfo,
                    pay_status,
                  },
                  extraPropData: {
                    brand: goodsInfo.brand,
                  },
                });
              }
            },
          });
          Taro.hideTabBar();
          this.editLocked = true;
        }
      },
    );
  };

  // 预估费用与权益次卡核算
  countFee = () => {
    const { price, range_one, f_fee } = this.reckonInfo || {};
    const { f_weight } = range_one || {};
    const { used: cardUsed, desc } = this.getCardId();
    let total = '--';
    const {
      feeData: { integral },
      extraInfo: { service },
    } = this.state;
    const { cost_value } = service || {};
    let feeList = [
      {
        label: '首重费',
        ...(cardUsed ? { fee: 0, desc } : { fee: f_fee }),
      },
      {
        label: '续重费',
        fee: numeral(parseFloat(price) - parseFloat(f_fee)).format('0.00'),
        desc: '由驿站线下收取',
      },
    ];
    if (price > 0) {
      total = price;
      feeList.push(
        {
          label: '优惠券',
          key: 'coupon',
        },
        {
          label: '积分',
          key: 'integral',
          value: integral,
        },
      );
    }
    if (cost_value > 0) {
      feeList.push({
        label: '保价费',
        key: 'pro_price',
        fee: cost_value,
      });
      // total += parseFloat(cost_value);
      total = numeral(total).add(cost_value).format('0.00');
    }

    total = numeral(cardUsed && f_weight ? total - f_weight : total).format('0.00');
    this.setState({
      feeList,
      reckonFee: total,
      reckonPrice: price || 0,
    });
  };

  // 预估费用拦截
  triggerGetReckonFee = (changedData) => {
    const {
      total,
      relationInfo,
      extraInfo,
      form: { data: formData },
    } = this.state;
    const formsData = { ...formData, ...changedData };
    const { receive_province, send_province } = formsData;
    const { goods_weight, brand, service, product_type } = extraInfo;
    const { cost_value, arrive_pay } = service || {};
    const { relation_id, type, courierId, dakId } = relationInfo || {};
    let data = getQuotationFeeApiData({
      ...formsData,
      goods_weight,
      brand,
      relationData: {
        type,
        relation_id,
        id: courierId,
        dakId,
      },
      arrive_pay,
      waybill_type: product_type,
    });
    type == 'brand' && (data = { ...formsData, ...data });
    const isComplete = receive_province && send_province;
    const reckonByReady = isComplete && (type !== 'brand' ? brand : true);
    if (total > 1) return; // 批量订单，跳过费用预估

    this.setState(
      {
        isComplete,
      },
      () => {
        if (reckonByReady) {
          // 预估费用
          if (!relation_id && !dakId && !courierId) return;
          let feeJson = { ...data };
          if (cost_value) feeJson.cost_value = cost_value;
          const feeby = JSON.stringify(feeJson);
          if (feeby === this.feeby) return;
          this.getReckonFee(data);
          this.feeby = feeby;
        } else {
          this.setState({
            feeList: [],
            reckonFee: '--',
          });
          this.reckonInfo = {};
        }
      },
    );
  };

  // 预估费用
  getReckonFee = (data) => {
    getQuotationFee(data, {
      formatRequest: false,
    }).then(({ data: res }) => {
      this.reckonInfo = res;
      this.countFee();
    });
  };

  // 清除编辑状态或清除批量寄件列表
  cleanEditStatus = (then = noop) => {
    const { order_id: editById, relationInfo } = this.state;
    const { notUniversalCode, isKdg = false, isKdg_tmp } = relationInfo;
    if (notUniversalCode || editById) {
      this.updateRelationInfo();
      Taro.kbUpdateRelationInfo({});
    }
    if (editById) {
      this.formIns.clean();
      Taro.modifyOrderId = null;
      this.editLocked = false;
      Taro.showTabBar().then(() => {
        this.setState(
          {
            order_id: '',
          },
          () => {
            then(editById);
          },
        );
      });
    } else {
      // 清空
      then();
      if (isKdg && isKdg_tmp) return;
      this.clearAddress();
    }
  };
  clearAddress = () => {
    this.formIns.clean();
    this.updateReceiveList();
    removeStorage({
      key: receiveStorageKey,
    });
  };
  triggerVerifyIsCashPay = (noVerifyCheckbox = false) => {
    const { relationInfo } = this.state;
    const { checkedCash } = this.state;
    const mode = checkIsBrand(relationInfo, 'sto');
    return mode && (noVerifyCheckbox ? true : checkedCash);
  };
  navigatorToOrder = () => {
    this.handleClean();
    Taro.navigator({
      url: 'order',
      target: 'tab',
    });
  };
  // 取消编辑订单，恢复默认
  handleClean = () => this.cleanEditStatus(() => this.formIns.clean());
  handleSubmitForm = async (action = '') => {
    const {
      checkedCash,
      form: { data },
      relationInfo: { relation_id },
      declarationAgree,
    } = this.state;
    const pureData = getPureSaveAddress(data, this.receiveList, 'receive');
    saveAddress({
      type: 'collect',
      address: pureData,
    });
    if (this.checkIsShowDeclaration() && !declarationAgree && action != 'agreeDeclaration') {
      this.setState({
        isOpenDeclaration: Math.random() + 1,
      });
      return;
    }
    if (relation_id && process.env.MODE_ENV == 'yz') {
      const unPayOrders = await getUnPayOrders({
        relation_id,
      });
      if (unPayOrders.length > 0) {
        this.setState({
          unPayOrders,
        });
        return;
      }
    }

    if (process.env.TARO_ENV !== 'alipay') {
      this.onSubmit_form();
      return;
    } else if (this.triggerVerifyIsCashPay(true) && !checkedCash) {
      this.cashModal().then((status) => {
        switch (status) {
          case 'cancel':
            this.onSubmit_form();
            break;
          case 'confirm':
            this.setState(
              {
                checkedCash: true,
              },
              () => {
                this.onSubmit_form();
              },
            );
            break;
          default:
            break;
        }
      });
    } else {
      this.onSubmit_form();
    }
  };

  changeSubmitType = (type) => {
    this.setState({
      printType: type,
    });
  };
  onPrintted = ({ action, count }) => {
    this.setState(
      {
        printType: action,
        mprintNm: count,
        isOpened: false,
      },
      () => {
        this.onSubmit_form();
      },
    );
  };

  onChangeCustomerInfo = (data) => {
    const { customerInfo = {} } = this.state;
    if (isFunction(data)) {
      this.customerBar.getUserAuthStatus = data;
      return;
    }
    const _customerInfo = {
      ...customerInfo,
      ...data,
    };

    this.setState({
      customerInfo: { ..._customerInfo },
    });
  };
  handleExtraInfoUpdate = (data) => {
    const { formData, dynamicVerify } = data;
    if (formData && Object.keys(formData).length) {
      this.onChange('info', { data: formData });
    }
    dynamicVerify &&
      this.setState({
        dynamicVerify,
      });
  };
  cashModal = () => {
    return new Promise((resolve) => {
      Taro.kbModal({
        content: '退出支付0.01元,您将错失订单成功签收后0.5元现金补贴',
        confirmText: '继续支付',
        cancelText: '确定提交',
        closable: false,
        confirmButtonProps: {
          className: 'kb-clear__margin kb-button__middle',
        },
        onCancel: () => {
          resolve('cancel');
        },
        onConfirm: () => {
          resolve('confirm');
        },
      });
    });
  };
  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    // 所有表单只包含地址信息，其他数据通过formatRequest合并
    // 这种方式可以利用form.disabled 判断是否展开物品信息
    // this.receiveList 批量下单时的收件人列表数据
    this.formIns = new Form(
      {
        form: getForm(),
        enableEmpty: false,
        api: {
          url: '/api/weixin/mini/minpost/order/add',
          toastError: false,
          onIntercept: (req, onThen) => {
            // 拦截批量请求
            if (this.receiveList) {
              batchSubmitOrder(
                {
                  ...req,
                  receiveList: this.receiveList,
                },
                this,
              )
                .then((res) => onThen(res, req))
                .catch((err) => console.log(err));
              return true;
            }

            // 一单多打，传入formIns，多打操作时使用
            if (this.submitAction === 'mprint') {
              orderAction({
                action: this.submitAction,
                formIns: this.formIns,
              }).catch((err) => console.log(err));
              return true;
            }
          },
          formatRequest: (req) => {
            if (process.env.MODE_ENV === 'xyt') {
              // 提交行为 submit|print|pprint|mprint
              this.submitStatus = req.status || 'submit';
              const { ...rest } = req;
              rest && delete rest.extraData;
              const {
                order_id,
                relationInfo: { relation_id },
                cardInfo,
                extraInfo,
                // printType,
                mprintNm,
              } = this.state;
              const { card_id: equity_card } = this.getCardId(cardInfo);
              const modifyData = order_id
                ? {
                    order_id,
                    order_platform: '',
                  }
                : null;
              const { service = {}, ...restExtraInfo } = extraInfo || {};
              restExtraInfo && delete restExtraInfo.reserve_time;
              const { is_arrive_pay, ...restService } = service || {};
              return {
                ...modifyData,
                order_num: this.submitAction == 'xytmprint' ? mprintNm : void 0,
                // print_type: formatXytPrintType(printType)??formatXytPrintType函数丢失,
                relation_id,
                order_info: {
                  ...rest,
                  ...restExtraInfo,
                  ...restService,
                },
                extra_info: {
                  is_fresh: '0',
                  equity_card,
                  is_arrive_pay,
                },
              };
            } else {
              const {
                extraData: [{ action = 'submit' } = {}] = [],
                order_num,
                status = 'submit',
                print_type,
                ...rest
              } = req;
              const { order_id, relationInfo, cardInfo, extraInfo, reckonPrice, closedPay } =
                this.state;
              const {
                relation_id,
                placeOrderConfig,
                dynamicForms = {},
                isKdg,
                isKdg_tmp,
              } = relationInfo || {};
              const { printType: customPrintType } = placeOrderConfig || {};
              const { card_id: equity_card } = this.getCardId(cardInfo);
              const modifyData = order_id
                ? {
                    order_id,
                    order_platform: '',
                  }
                : null;
              // 提交行为 submit|print|pprint|mprint
              this.submitAction = action;
              this.submitStatus = status;
              const { service = {}, product_type = '', volume, ...restExtraInfo } = extraInfo;
              const { goods_name: dynamicGoods_name } = dynamicForms || {};
              const { is_arrive_pay, ...restService } = service || {};
              const { goods_name } = restExtraInfo || {};
              const fee_data = {};
              reckonPrice && reckonPrice > 0 && !closedPay && (fee_data.orderPrice = reckonPrice);

              let _volume = {};
              if (isObject(volume)) {
                if (volume.checked) {
                  _volume = { ...volume };
                }
              }

              // 增加兔优达和航空件参数
              const brandProductCode = {
                兔优达: 'TYD',
                航空件: 'HKJ',
              };

              return {
                ...modifyData,
                order_num,
                print_type: customPrintType || print_type,
                relation_id,
                order_info: {
                  product_type,
                  ...fee_data,
                  ...rest,
                  ...restExtraInfo,
                  ...restService,
                  ..._volume,
                },
                form_info: { type: 'submit', scene: 'order_place' },
                extra_info: {
                  is_fresh:
                    (dynamicGoods_name && dynamicGoods_name.customFresh) || isFresh(goods_name)
                      ? '1'
                      : '0',
                  equity_card,
                  is_arrive_pay,
                  is_cabinet_reserve: isKdg && !isKdg_tmp ? '1' : '', // 快递柜下单，且此码非临时码，则此单判断为快递柜预约单
                  brandProductCode: brandProductCode[product_type] || '',
                },
              };
            }
          },
          onCustomCheck: (req) => {
            const {
              relationInfo,
              dynamicVerify: { service: dynamicServiceVerify },
              agree,
            } = this.state;
            if (!agree) {
              this.setState({
                agreeModal: Date.now(),
              });
              return { code: 106, msg: '请阅读并同意服务协议' };
            }
            const { serviceConfig } = this.props;
            const { placeOrderConfig, relation_id } = relationInfo || {};
            const {
              order_info: { goods_name, print_value, goods_remark, keep_account, collection },
            } = req;
            const dynamicRes = verifyDynamicForm(req.order_info, dynamicServiceVerify);
            const verifyServiceRes = verifyServiceForm(
              {
                keep_account,
                collection,
              },
              serviceConfig,
            );
            if (verifyServiceRes.code !== 0) {
              return verifyServiceRes;
            }
            if (dynamicRes.code !== 0) {
              return dynamicRes;
            }
            if (!relation_id) {
              return { code: 101, msg: '请选择下单对象' };
            }
            if (!goods_name) {
              return { code: 101, msg: '请选择物品类型' };
            }
            if (print_value === '') {
              return { code: 102, msg: '请选择派件备注' };
            }
            if (placeOrderConfig && !goods_remark) {
              return { code: 103, msg: '请输入体检人姓名' };
            }
          },
          onThen: this.createDone,
        },
        onUpdate: (data) => {
          const { eventType, data: addressData } = data;
          if (eventType === 'clean') {
            // 清除操作
            const { extraInfo: { brand } = {} } = this.state;
            const clear = {
              ...getEditDefaultFormData(),
              brand,
              goods_remark: '',
              shipper_zipcode_del: '',
              reserve_time: '',
              package_images: [],
              service: null,
            };
            this.setState({
              extraInfo: clear,
              addressData: {
                ...addressData,
                clean: true,
              },
              extraPropData: clear,
              mprintNm: '',
            });
            this.feeby = '';
            setStorage({
              key: 'extraInfo',
              data: clear,
            });
          }
        },
      },
      this,
    );
  };

  // 完成后跳转引导
  // 编辑订单：跳转详情
  // 可支付订单：跳转支付页
  // 其他：跳转结果页
  doneNavigator = ({ data, req, extraData = null, editById = '', support_pay }) => {
    const { relationInfo } = this.state;
    const {
      relation_id,
      isKdg = false,
      isKdg_tmp = false,
      name,
      desc,
      cabinet_name = name,
      cabinet_address = desc,
      device_id,
      cabinet_id,
      dak_id,
    } = relationInfo || {};
    const { order_id: createdId, brand: _brand } = data;
    const order_id = editById || (isString(createdId) ? createdId : '');
    const {
      order_num,
      order_info: { send_mobile = '', brand: orderInfoBrand = '' } = {},
      brand = orderInfoBrand,
    } = req;
    const { total = order_num || this.state.total } = data;
    const options = editById
      ? { order_id }
      : {
          order_id,
          total,
          brand,
          status: this.submitStatus,
          send_mobile,
          isKdg: isKdg ? '1' : '',
          isKdg_qr: isKdg ? (isKdg_tmp ? '' : '1') : '',
          cabinet_name: name,
          ...extraData,
        };
    // 快递柜下单，且此码非临时码，则此单判断为快递柜预约单，预约单为正常下单，无需引导存柜，在订单列表及详情中执行存柜操作
    if (isKdg) {
      if (isKdg_tmp) {
        Taro.navigator({
          url: 'kdg/express',
          options: {
            ...options,
            relation_id,
            device_id,
            cabinet_id,
            cabinet_name,
            cabinet_address,
            dak_id,
            brand: _brand,
          },
        });
      } else {
        if (isArray(createdId)) {
          // 缓存订单数据
          Taro.kbSetGlobalData(globalOrderIDs, createdId);
        }
        Taro.navigator({
          url: `order/${editById ? 'detail' : 'result'}`,
          options: options,
        });
      }
    } else if (support_pay) {
      Taro.navigator({
        url: 'order/pay',
        options: options,
      });
    } else {
      if (isArray(createdId)) {
        // 缓存订单数据
        Taro.kbSetGlobalData(globalOrderIDs, createdId);
      }
      Taro.navigator({
        url: `order/${editById ? 'detail' : 'result'}`,
        options: options,
      });
    }
  };
  // 下单对象为品牌类型支付宝支付宝保证金
  cashPay = (order_id) => {
    const {
      relationInfo: { brand_id },
    } = this.state;
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/minpost/Pay/payDeposit',
        toastLoading: false,
        data: { order_id, brand_id },
        onThen: ({ data, code, msg }) => {
          if (code == 0) {
            if (!data || !data.trade_no) {
              // 从钱包扣除成功
              resolve();
            } else {
              // 余额不足，调起支付宝让用户支付
              my.tradePay({
                tradeNO: data.trade_no,
                success: (res) => {
                  const { resultCode } = res;
                  if (resultCode == 6001) {
                    this.cashModal().then((status) => {
                      switch (status) {
                        case 'confirm':
                          this.cashPay(order_id).then(() => {
                            resolve();
                          });
                          break;
                        case 'cancel':
                          resolve();
                          break;
                        default:
                          break;
                      }
                    });
                    return;
                  }
                  resolve();
                },
                fail: function (res) {
                  Taro.kbToast({
                    text: JSON.stringify(res),
                    onThen: () => {
                      resolve();
                    },
                  });
                },
              });
            }
          } else {
            Taro.kbModal({
              content: `${msg}，是否重新发起支付`,
              centered: true,
              onConfirm: () => {
                this.cashPay(order_id).then(() => {
                  resolve();
                });
              },
              onCancel: () => {
                resolve();
              },
            });
          }
        },
      });
    });
  };
  triggerCashPay = (order_id) => {
    return new Promise((resolve) => {
      if (this.triggerVerifyIsCashPay() && order_id) {
        this.cashPay(order_id).then(() => {
          resolve('sto');
        });
      } else {
        resolve('');
      }
    });
  };

  // 创建订单完成
  createDone = ({ code, data, msg }, req) => {
    if (code == 0) {
      // 创建成功后刷新订单列表
      refreshControl(REFRESH_KEY_ORDER);
      const { order_id, support_pay } = data;
      this.triggerCashPay(order_id).then((id) => {
        if (id == 'sto') {
          reportAnalytics({
            key: 'cash.pay',
          });
        }
        // 不需要打印的action
        const unPrintKeys = ['submit', 'mprint'];
        // 清除编辑状态
        this.cleanEditStatus((editById) => {
          // 下单完成后判断是否需要打印
          if (!unPrintKeys.includes(this.submitAction)) {
            orderAction(
              {
                action: this.submitAction,
                data,
              },
              this,
            )
              .then((extraData) =>
                this.doneNavigator({
                  data,
                  req,
                  extraData,
                  editById,
                  support_pay,
                }),
              )
              .catch((err) => console.log(err));
          } else {
            this.doneNavigator({
              data,
              req,
              editById,
              support_pay,
            });
          }
        });
      });
    } else if (code == 9101) {
      Taro.kbToast({
        text: msg,
      });
      // 提交成功打印失败
      this.handleClean();
    } else if (code == 231024) {
      const { order_info } = req;
      guideRealName(JSON.parse(order_info).send_mobile);
    } else {
      Taro.kbToast({
        text: msg,
      });
    }
  };

  // 监听
  onPostMessage = (key, data) => {
    switch (key) {
      case 'realnameBack':
        // 实名认证返回后重新触发实名检查
        this.checkRealname();
        break;
      case 'routerParamsChange':
        const { params, source, data: formData } = data;
        const { address } = params || {};
        if (address) {
          this.setState({
            addressData: address,
          });
        }
        if (source === 'goods') {
          this.onChange('info', { data: formData, nextData: formData });
        }
        break;
      case 'scanCoupon':
        const { dak_id: dakId } = data;
        if (Taro.kbUpdateRelationInfo) {
          Taro.kbUpdateRelationInfo({
            dakId,
          });
        }
        this.setState({
          scanCoupon: data,
        });
        break;
      case 'refresh_cabinet':
        break;
    }
  };
  // 更新收件人类表
  updateReceiveList = (list = null) => {
    this.receiveList = list;
    const total = isArray(list) ? list.length || 1 : 1;
    this.setState({
      total,
    });
  };

  // 检查某属性是否存在
  checkHasOwnProperty = (data, key) => data.hasOwnProperty(key);

  // 地址信息输入变化
  onChange = (key, e) => {
    const { data, nextData, formEvent, eventType, receiveList } = e;
    switch (key) {
      case 'address':
        const { receive_province, send_province } = nextData;
        receive_province && send_province && (data.disabled = false);
        this.formIns.update(data);
        this.updateReceiveList(receiveList);
        if (
          (formEvent === 'input' || eventType === 'ready' || eventType === 'outter') &&
          this.checkHasOwnProperty(data, 'send_mobile')
        ) {
          this.checkRealname();
        }
        break;
      case 'info':
        // 物品信息变化
        const { extraInfo } = this.state;
        data &&
          this.setState(
            {
              extraInfo: { ...extraInfo, ...data },
            },
            () => {
              const { service } = data;
              const isProPriceChange = service && this.checkHasOwnProperty(service, 'cost_value');
              const isBrandChange = this.checkHasOwnProperty(data, 'brand');
              const isWeightChange = this.checkHasOwnProperty(data, 'goods_weight');
              this.triggerGetReckonFee(
                isWeightChange || isBrandChange || isProPriceChange ? null : data,
              );
            },
          );
        return;
        break;
    }

    this.triggerGetReckonFee(data);
  };

  // 切换是否同意
  onSwitchAgree = (agree) => {
    this.setState({ agree });
  };

  // 检测实名状态
  checkRealname = () => {
    if (!this.logined) return;
    const {
      form: {
        data: { send_mobile: mobile },
      },
    } = this.state;
    getRealnameStatus(mobile)
      .then(({ realnamed }) => {
        this.setState({
          realnameTips: !realnamed,
        });
      })
      .catch(() => {
        this.setState({
          realnameTips: false,
        });
      });
  };

  // 跳转实名认证页面
  onRealname = () => {
    const {
      form: {
        data: { send_mobile: phone },
      },
    } = this.state;
    Taro.navigator({
      url: `realname?${qs.stringify({ phone })}`,
    });
  };

  // 登录状态更新
  onUpdate = (data) => {
    const { logined } = data;
    this.logined = logined;
    this.checkRealname();
    if (logined) {
      // 扫码进入的逻辑
      const page = getPage(-1);
      scanAction({ scanType: 'QR_CODE' }, page)
        .then((res) => {
          // 20231122 全局保留快递柜状态
          const { dispatch } = this.props;
          dispatch(save(res));
        })
        .catch((err) => console.log(err));
      if (process.env.MODE_ENV === 'yz') {
        if (process.env.PLATFORM_ENV === 'alipay') {
          // 统计
          const { params } = this.$router;
          reportAnalytics({
            key: 'order.edit.new',
            action: 'load',
            ...params,
          });
        }
      }
    }
  };

  // 切换权益次卡
  handleChangeCard = (cardInfo) => {
    this.setState(
      {
        cardInfo,
      },
      this.countFee,
    );
  };

  // 店铺信息变更
  handleChangeStore = (data) => {
    const { relation_id = '', dakId, courier_id: courierId, placeOrderConfig = null } = data || {};
    relation_id && this.getSupportPay(relation_id);
    this.updateRelationInfo({
      ...data,
      courierId,
      dakId,
      relation_id: relation_id,
      placeOrderConfig,
    });
  };

  onSwitchFee = (isOpenedFee) =>
    this.setState({
      isOpenedFee,
    });

  // 获取积分数据
  handleIntegralChange = (e) => {
    this.setState(
      {
        feeData: {
          ...this.state.feeData,
          integral: e.points,
        },
      },
      () => {
        console.log('重新输出计算明细');
        this.countFee();
      },
    );
  };

  // 验证
  handleCheck = () => {
    return this.formIns.check();
  };
  // 获取权益卡id
  getCardId = (cardInfo = this.state.cardInfo) => {
    const { isOpenedPay, total } = this.state;
    const { card_id = '0', desc } = (isOpenedPay && total === 1 && cardInfo) || {};
    return {
      card_id,
      used: card_id > 0,
      desc,
    };
  };

  // 清除未支付订单信息
  handleInterceptModalClose = () => {
    this.setState({
      unPayOrders: [],
    });
  };

  // 快递柜真正入柜后，清除收件信息
  cleanKdgAddressInfo = () => {
    this.clearAddress();
  };
  // 是否显示快递员声明
  checkIsShowDeclaration = () => {
    const { serviceConfig } = this.props;
    const { declaration, isDeclared } = serviceConfig || {};
    return isDeclared && !!declaration;
  };
  handleDeclarationSwitchAgree = (agree) => {
    this.setState({
      declarationAgree: agree,
    });
  };

  render() {
    const {
      cardInfo,
      total,
      isComplete,
      feeList,
      isOpened,
      isOpenedFee,
      isOpenedPay,
      order_id,
      realnameTips,
      reckonFee,
      getting,
      agree,
      agreeModal,
      addressData,
      extraInfo,
      relationInfo,
      checkedCash,
      dynamicForms,
      extraPropData,
      closedPay,
      reckonPrice,
      unPayOrders,
      scanCoupon,
      declarationAgree = false,
      isOpenDeclaration = false,
      ...rest
    } = this.state;
    const { isKdg = false, isKdg_tmp } = relationInfo || {};
    const { serviceConfig } = this.props;
    const { declaration } = serviceConfig || {};
    const { used: cardUsed } = this.getCardId(cardInfo);
    const mode = checkIsBrand(relationInfo, 'sto') ? 'brand' : 'order';
    const showDetail = reckonFee > 0 || cardUsed;
    const { goods_weight } = extraInfo || {};
    const sendLocked = isObject(relationInfo) && isObject(relationInfo.placeOrderConfig);

    return (
      <KbPage cover={false} {...rest} onUpdate={this.onUpdate}>
        <KbScrollView
          full
          renderHeader={
            <Fragment>
              {!getUserStateByComplete() && (
                <View className='kb-login__auth--box'>
                  <KbLoginAuthAndBind className='kb-button__middle' />
                </View>
              )}
            </Fragment>
          }
          renderFooter={
            <Fragment>
              {mode == 'brand' && process.env.TARO_ENV == 'alipay' && (
                <KbBrandCashCheckbox value={checkedCash} onChange={this.handleCashCheckBoxChange} />
              )}
              <View className='at-row at-row__align--center kb-send__fee--row at-row__justify--end'>
                {total == 1 && (
                  <View className='at-col kb-send__fee'>
                    <View className='at-row at-row__align--center at-row__justify--between kb-spacing-md-tb'>
                      <View>
                        <View className='at-row at-row__align--center'>
                          {reckonFee > 0 && (
                            <View className='kb-spacing-md-r'>
                              <Text className='kb-color__black kb-size__lg'>预估费用：</Text>
                              {getting ? (
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='loading'
                                  className='kb-icon-size__base kb-color__grey'
                                />
                              ) : (
                                <Text className='kb-color__red'>{reckonFee}</Text>
                              )}
                            </View>
                          )}

                          {isOpenedPay && closedPay && isComplete && this.showEquityCard && (
                            <View>
                              <KbCardBar
                                data={relationInfo}
                                onChange={this.handleChangeCard}
                                weight={goods_weight}
                              />
                            </View>
                          )}
                        </View>

                        <View className='kb-color__grey kb-size__sm kb-spacing-xs-t'>
                          {!isComplete
                            ? '填完地址可预估'
                            : cardInfo
                            ? `${
                                (cardInfo.desc || '') +
                                (goods_weight
                                  ? ''
                                  : reckonPrice
                                  ? `${cardInfo.desc ? ',' : ''}按1kg预估`
                                  : '')
                              }`
                            : goods_weight
                            ? ''
                            : reckonPrice
                            ? '按1kg预估'
                            : ''}
                        </View>
                      </View>
                      {showDetail && (
                        <View
                          className='kb-color__grey kb-send__fee--detail kb-size__base kb-margin-sm-r'
                          hoverClass='kb-hover-opacity'
                          onClick={this.onSwitchFee.bind(this, true)}
                        >
                          明细
                        </View>
                      )}
                    </View>
                  </View>
                )}
                <View className='kb-spacing-md'>
                  <KbSubmitBar
                    className='kb-size__lg'
                    onClick={this.handleSubmitForm}
                    onCheck={this.handleCheck}
                    onChange={this.changeSubmitType}
                    total={total}
                    relation={relationInfo}
                    btnText={isKdg ? (isKdg_tmp ? '下一步' : '预约下单') : ''}
                  />
                </View>
              </View>
              <KbCurtainActivity />
              <KbScanCoupon data={scanCoupon} />
            </Fragment>
          }
        >
          <View className='kb-send'>
            <View className='kb-border-lg kb-send__item kb-box-shadow'>
              <KbStoreCardSelector
                onChange={this.handleChangeStore}
                disabled={!!order_id || isKdg}
              />
            </View>
            <View className='kb-send__item '>
              <KbAddressEdit
                locked
                pageName='edit'
                relationInfo={relationInfo}
                useDefault={!order_id}
                data={addressData}
                sendLocked={sendLocked}
                onChange={this.onChange.bind(this, 'address')}
                supportBatch={sendLocked || isKdg ? false : true}
                ref={this.addressEditRef}
              />
            </View>
            <View className='kb-send__item'>
              <KbExtraEnter
                relationInfo={relationInfo}
                reckonFee={reckonFee}
                data={extraInfo}
                dynamicForms={dynamicForms}
                serviceConfig={serviceConfig}
                onChange={this.handleExtraInfoUpdate}
                total={total}
              />
              <KbBrandInfo
                type='brand'
                reckonFee={reckonFee}
                data={extraPropData}
                dynamicForms={dynamicForms}
                onChange={this.onChange.bind(this, 'info')}
                serviceConfig={serviceConfig}
                relationInfo={relationInfo}
              />
            </View>
            <View className='at-row at-row__align--center'>
              <View className='at-col'>
                <ServiceAgreement
                  agreeType='serviceAgreement'
                  updateData={this.setState.bind(this)}
                  agree={agree}
                  agreeModal={agreeModal}
                />
              </View>
              <View>
                {isOpenedPay && total === 1 && this.showEquityCard && !isKdg && (
                  <KbCardBar data={relationInfo} action='buy' />
                )}
                <KbIntegralBar
                  mode='edit'
                  onChange={this.handleIntegralChange}
                  dakId={relationInfo.dakId}
                />
              </View>
            </View>
            {this.checkIsShowDeclaration() && (
              <View>
                <KbDeclarationAgreement
                  declaration={declaration}
                  open={isOpenDeclaration}
                  agree={declarationAgree}
                  onSwitchAgree={this.handleDeclarationSwitchAgree}
                  onConfirm={() => this.handleSubmitForm('agreeDeclaration')}
                  relationType={relationInfo.type}
                />
              </View>
            )}
          </View>
        </KbScrollView>
        <AtFloatLayout
          isOpened={isOpenedFee}
          onClose={this.onSwitchFee.bind(this, false)}
          title='金额明细'
        >
          <KbFeeList list={feeList} reckonFee={reckonFee} />
        </AtFloatLayout>
        <KbPrintMore isOpened={isOpened} onPrintted={(res) => this.onPrintted(res)} />
        <Fragment>
          {order_id && (
            <View className='kb-back'>
              <View
                hoverClass='kb-hover'
                className='kb-button__link'
                onClick={this.navigatorToOrder}
              >
                返回
              </View>
            </View>
          )}
        </Fragment>
        <KbUnPayOrders order_ids={unPayOrders} onClose={this.handleInterceptModalClose} />
      </KbPage>
    );
  }
}

export default Index;
