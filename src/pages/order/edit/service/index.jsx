/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbServiceTips from '@/components/_pages/order/extra-info/service-tip';
import {
  calculate,
  getServiceList,
  servicesConfig,
  servicesKeyMap,
  serviceTips,
} from '@/components/_pages/order/extra-info/_utils';
import { getFormItem, serviceKeys } from '@/components/_pages/order/_utils';
import KbButton from '@base/components/button';
import KbCheckbox from '@base/components/checkbox';
import KbNoticeBar from '@base/components/notice-bar';
import KbPage from '@base/components/page';
import Form from '@base/utils/form';
import { debounce, decimalLen, getStorageSync, setStorage } from '@base/utils/utils';
import { ScrollView, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isNumber from 'lodash/isNumber';
import { AtButton, AtIcon, AtInput, AtSwitch, AtTabs, AtTabsPane } from 'taro-ui';
import { getProPriceDebounce } from '@/components/_pages/order/_utils/order.edit.dh';
import isUndefined from 'lodash/isUndefined';
import './index.scss';
import { defaultToPayAmount } from '@/components/_pages/order/_utils/order.service';

@connect(({ global }) => ({ loginData: global.loginData }))
class Index extends Component {
  static options = {
    addGlobalClass: true,
  };
  static config = {
    navigationBarTitleText: '增值服务',
  };
  constructor(props) {
    super(props);
    this.state = {
      form: { data: {}, disabled: true },
      agree: false,
      current: 0,
      serviceList: [],
      dynamicDisabled: {},
      servicesConfig,
      relationInfo: {},
      extraInfo: {},
      hideBar: false,
      total: 0, //收件人数量
    };
    this.handleInputChange = debounce(this.handleInputChange, 500, {
      leading: false,
      trailing: true,
    });
  }

  componentDidMount() {
    this.agreeStorage('check');
  }

  agreeStorage = (type, value) => {
    const storageKey = 'service-agree';
    if (type === 'set') {
      setStorage({ key: storageKey, data: value });
    } else {
      const { data: val } = getStorageSync(storageKey) || {};
      this.setState({
        agree: val === 1,
      });
    }
  };

  // 数据传入
  onPostMessage = (key, e) => {
    const { params } = e;
    if (params && params.title) {
      Taro.setNavigationBarTitle({
        title: params.title,
      });
    }
    switch (key) {
      case 'routerParamsChange':
        this.createForm(() => {
          const {
            serviceConfig,
            dynamicForms,
            reckonFee,
            data,
            relationInfo,
            total,
            pageSource,
            address,
          } = params || {};
          const { service } = data || {};
          this.setState({
            relationInfo,
            extraInfo: data,
            total,
            pageSource,
            address,
          });
          serviceConfig &&
            this.updateServicesConfig(serviceConfig).then(() => {
              const { cost_value } = service || {};
              if (service) {
                cost_value && (service.rate_checked = true);
                this.formIns.update(service);
              }
              let fee = reckonFee === '--' ? 0 : reckonFee;
              fee &&
                dynamicForms &&
                dynamicForms.service &&
                (dynamicForms.service.arrive_pay = {
                  value: fee,
                  disabled: true,
                });
              dynamicForms && this.triggerWhetherFormLocked(dynamicForms);
            });
        });
        break;
    }
  };
  // 生成增值服务配置项
  updateServicesConfig(data) {
    return new Promise((resolve) => {
      const serviceList = getServiceList(
        Object.keys(data)
          .filter((i) => !!data[i])
          .filter((val, _, arr) => {
            // 如果同时存在保价和声明价值，只需填写声明价值
            if (!(val == 'isProPrice' && arr.includes('isDecVal'))) {
              return val;
            }
          })
          .map((i) => servicesKeyMap[i]),
        // 如果该品牌有特定的增值服务
      );
      this.setState(
        {
          servicesConfig: data,
          serviceList,
        },
        resolve,
      );
    });
  }
  // 是否触发表单锁定为自定义内容
  triggerWhetherFormLocked = (dynamicForms) => {
    const { service: dynamicService } = dynamicForms || {};
    const { serviceList } = this.state;
    let dynamicDisabled = {};
    let dynamicUpdateData = {};
    const keys = Object.keys(dynamicService || {});
    const dynamicList = getServiceList(
      keys.map((key) => {
        dynamicService[key].disabled && (dynamicDisabled[key] = true);
        dynamicUpdateData[key] = dynamicService[key].value;
        return key;
      }),
    );
    const copyList = [...dynamicList];
    serviceList.forEach((item) => {
      if (!dynamicList.find((iitem) => iitem.key === item.key)) {
        copyList.push(item);
      }
    });
    this.setState(
      {
        serviceList: copyList,
        dynamicDisabled,
      },
      () => {
        this.formIns.update(dynamicUpdateData);
      },
    );
    return keys.length > 0;
  };

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (cb) => {
    const form = getFormItem({
      keys: [...serviceKeys],
      data: {},
    });
    this.formIns = new Form(
      {
        form,
        onChange: ({ value, name }) => {
          let val = value;
          const {
            servicesConfig: { isProPrice, proPriceEnd },
          } = this.state;
          val = 1 * val || 0;
          if (decimalLen(val) > 2) {
            val = val.toFixed(2);
          }
          if (['pro_price', 'keep_account'].includes(name) && isProPrice) {
            const max = proPriceEnd || *********;
            if (val > parseFloat(max)) {
              Taro.kbToast({
                text: `物品价值超过最大限制${max}元，请重新录入`,
              });
              val = value;
            }
          }
          return val * 1;
        },
        onUpdate: async (data) => {
          const { nextData: updateData } = data;
          let proPriceKey = Object.keys(updateData).find((key) =>
            ['pro_price', 'keep_account'].includes(key),
          );
          const {
            servicesConfig: { isProPrice, proPriceStart, proPriceEnd, cost, rate_checked },
            pageSource,
            address,
            relationInfo = {},
            extraInfo = {},
          } = this.state;
          // 计算保价相关逻辑
          console.log(updateData, proPriceKey, isProPrice,  'updateData')
          if (isNumber(updateData[proPriceKey]) && isProPrice) {
            let value = parseFloat(updateData[proPriceKey]) || 0;
            let checked = rate_checked;
            const max = proPriceEnd || *********;
            if (value > parseFloat(proPriceStart || 0) && value <= parseFloat(max)) {
              checked = true;
            } else {
              if (value >= proPriceEnd) {
                checked = true;
                value = max;
                // 同步更新输入框中的值，防止当超出范围后，没有显示最大值
                this.formIns.update({
                  keep_account: max * 1,
                });
                value > proPriceEnd &&
                  Taro.kbToast({
                    text: `物品价值超过最大限制${max}元，请重新录入`,
                  });
              }
            }
            let cost_value;
            console.log(value, 'value', this.curValue, checked, 'checked')
            console.log('cost_value111', cost_value)
            if (value && value * 1 > 0 && this.curValue != value) {
              this.curValue = value;
              if (checked) {
                if (process.env.MODE_ENV == 'wkd') {
                  if (pageSource == 'dh') {
                    cost_value = await getProPriceDebounce({
                      address: address,
                      weight: extraInfo.goods_weight,
                      brand: relationInfo.brand,
                      decVal: value,
                    });
                  }
                  cost_value = this.calculateProPrice(value, cost);
                } else {
                  cost_value = calculate(value, cost);
                }
              } else {
                cost_value = 0;
              }
            }
            if (value && value * 1 > 0) {
              this.curValue2 = value;
            }
            console.log('cost_value222', cost_value, this.curValue2)
            if (!isUndefined(cost_value) && updateData.cost_value !== cost_value) {
              this.formIns.update({
                cost_value,
                rate_checked: value === 0 ? false : checked,
              });
            } else if (isUndefined(cost_value) && value === 0 && this.curValue2 != value) {
              this.curValue2 = value;
              this.formIns.update({
                cost_value: 0,
                rate_checked: false,
              });
            }
          }
        },
        onReady: () => {
          cb && cb();
        },
      },
      this,
    );
  };

  // 是否同意协议
  handleSwitchAgree = () => {
    this.setState((pre) => {
      const agree = !pre.agree;
      this.agreeStorage('set', agree ? 1 : 0);
      return { agree };
    });
  };
  // 切换tab
  handleSelectService = (current) => {
    this.setState({
      current,
    });
  };
  // 到付开关
  handleSwitchChange = (formName, check) => {
    this.formIns.update({
      [formName]: check ? defaultToPayAmount : 0,
    });
  };

  // 处理输入框
  handleInputChange = (key, value) => {
    console.log(key, value, 'handleInputChange')
    this.onChange_form(key, { target: { value }, type: 'change' });
  };
  handleBar(value, ev) {
    // 处理支付宝版本键盘弹起时，底部按钮会上推
    if (process.env.PLATFORM_ENV == 'alipay') {
      if (ev.type == 'focus') {
        this.setState({
          hideBar: true,
        });
      } else if (ev.type == 'blur') {
        this.setState({
          hideBar: false,
        });
      }
    }
  }

  // 取消
  handleNavBack = () => {
    Taro.navigator();
  };
  // 确定
  handleSubmitForm = (action = 'init') => {
    const {
      servicesConfig: { isProPrice, isDecVal, cost },
      form: { data },
      agree,
      relationInfo,
      pageSource,
    } = this.state;
    const { type: relationType, dynamicForms = {}, brand } = relationInfo || {};

    if (brand == 'cngg') {
      Taro.navigator();
      return;
    }

    const { keep_account, collection, arrive_pay, rate_checked } = data;
    if (isDecVal && !keep_account) {
      Taro.kbToast({ text: '请声明物品价值' });
      return;
    }
    if (!agree) {
      Taro.kbToast({ text: '请同意免责声明' });
      return;
    }
    if (collection && arrive_pay) {
      Taro.kbToast({ text: '到付和代收只能填写其中一项' });
      return;
    }
    if (arrive_pay > 100000) {
      Taro.kbToast({ text: '到付金额不能大于10万' });
      return;
    }
    if (collection > 100000) {
      Taro.kbToast({ text: '代收金额不能大于10万' });
      return;
    }
    if (process.env.MODE_ENV == 'wkd') {
      const isHasService = dynamicForms.service && dynamicForms.service.isShow;
      if (relationType == 'brand' && !isHasService && action != 'confirm' && pageSource != 'dh') {
        Taro.kbModal({
          content: [
            '若需要使用增值服务-保价，将匹配京东快递为您服务',
            '注：此方式不可使用优寄优惠券，由京东快递员线下收取快递费',
          ],
          cancelText: '取消',
          onConfirm: () => {
            Taro.kbSetGlobalData('disabledServiceList', true);
            Taro.kbUpdateRelationInfo({
              brand: 'jd',
            });
            this.handleSubmitForm('confirm');
          },
          onCancel: () => {
            Taro.navigator();
          },
        });
        return;
      }
    }
    Taro.navigator({
      post: {
        type: 'routerParamsChange',
        data: {
          source: 'goods',
          data: {
            service: { ...data, rate: isProPrice && rate_checked ? cost : '' },
          },
        },
      },
    });
  };

  //微快递存在特有计算保价方式
  calculateProPrice(value, cost) {
    //计算保价费用
    let params = { costMoney: 0, InputMoney: value * 1 };
    const { relationInfo, extraInfo } = this.state;
    const { type: relationType, brand } = relationInfo || {};
    const { product_code } = extraInfo || {};
    let brandArr = ['sfky'];
    if (relationType == 'brand') {
      params.costMoney = this.getCostMoney(params.InputMoney, cost, brand);
      if (brandArr.includes(brand)) {
        if (brand == 'sfky' && product_code == 'SE0100' && params.costMoney < 1) {
          //顺丰-重货包裹
          params.costMoney = 1;
        } else if (brand == 'sfky' && product_code == 'SE0101' && params.costMoney < 10) {
          //顺丰-小票零担
          params.costMoney = 10;
        }
      }
    } else if (relationType == 'tcjs' && brand == 'dada') {
      params.costMoney = this.getDadaMoney(params.InputMoney, cost);
    } else {
      params.costMoney = calculate(params.InputMoney, cost);
      if (params.costMoney > 0 && params.costMoney < 1) {
        //保价费用小于1元，按1元处理;
        params.costMoney = 1;
      }
    }
    return params.costMoney;
  }
  getDadaMoney = (InputMoney, cost) => {
    let money = InputMoney;
    /**
     * 计算达达保价费
     */
    if (money < 100) {
      money = 100;
    } else {
      money = money % 100 > 0 ? Math.floor(money / 100) * 100 + 100 : money;
    }
    let costMoney = Math.ceil(calculate(money, cost));
    return costMoney;
  };
  getCostMoney = (InputMoney, cost, brand) => {
    if (brand == 'dp') {
      if (InputMoney > 0 && InputMoney <= 300) {
        return 1;
      }
    } else {
      if (InputMoney > 0 && InputMoney <= 500) {
        return 1;
      } else if (InputMoney > 500 && InputMoney <= 1000) {
        return 2;
      }
    }
    let costMoney = calculate(InputMoney, cost);
    return Math.ceil(costMoney);
  };

  render() {
    const {
      servicesConfig,
      serviceList,
      current,
      form: { data },
      agree,
      dynamicDisabled,
      total,
      relationInfo,
      hideBar,
      pageSource,
      ...rest
    } = this.state;
    const { isProPrice, cost, isDeclared, declaration, proPriceStart, proPriceEnd } =
      servicesConfig;
    const { type: relationType, brand } = relationInfo || {};
    const hasServiceList = serviceList && serviceList.length > 0;
    const hasProPrice = (key) => isProPrice && ['keep_account', 'pro_price'].includes(key);
    const getDeclarationList = (showProPriceDec) => {
      if (pageSource === 'dh') {
        return [];
      }
      let list = showProPriceDec
        ? [
            `根据驿站/快递员/网点声明每笔订单交寄物品价值在 ${parseFloat(
              proPriceStart || 0,
            )}元~${parseFloat(proPriceEnd || 0)}元必须保价，低于 ${parseFloat(
              proPriceStart || 0,
            )}元非必须保价（价值区间是或字段，根据快递员/驿站/网点设置的）`,
            '请按物品实际价值来填写物品价值，我方页面只做下单参考，具体业务请咨询驿站/快递员/驿站/网点',
            '物品是否属于可保价范围请咨询上门驿站/快递员/网点服务人员，我方与驿站/快递员/网点没有从属关系，不对其服务做任何担保和背书',
            '请仔细与驿站/快递员/网点明确服务内容，并当面与驿站/快递员/网点签订增值服务协议且索要服务依据',
          ]
        : [];
      if (isDeclared && declaration) {
        list = [declaration, ...list];
      }
      return list;
    };

    const rootCls = classNames('kb-service', {
      'kb-service-one': pageSource === 'dh' && serviceList && serviceList.length == 1,
    });

    const isCn = brand == 'cngg';
    console.log(data && data.cost_value, 'data.cost_value')

    return (
      <KbPage
        {...rest}
        renderHeader={
          <Fragment>
            {total > 1 && (
              <KbNoticeBar>收件人中未填写增值服务金额的订单，可在此统一补全</KbNoticeBar>
            )}
          </Fragment>
        }
        renderFooter={
          <Fragment>
            {!hideBar && (
              <View className='kb-spacing-md kb-service-footer'>
                <View className='at-row at-row__align--center kb-spacing-md-lr kb-spacing-md-b'>
                  <KbCheckbox
                    label='我已阅读并同意'
                    checked={agree}
                    onChange={this.handleSwitchAgree}
                    className='kb-color__black'
                  />
                  <View>
                    <AtButton
                      className='kb-button__link'
                      size='small'
                      onClick={Taro.navigateToDocument.bind(this, 10)}
                    >
                      《免责声明》
                    </AtButton>
                  </View>
                </View>
                <View className='kb-spacing-sm-t  at-row at-row__align--center kb-service-options'>
                  {pageSource != 'dh' && (
                    <View className='kb-margin-sm-lr kb-service-options-item'>
                      <KbButton
                        onClick={this.handleNavBack}
                        className='kb-button'
                        circle
                        type='secondary'
                      >
                        {hasServiceList ? '取消' : '返回'}
                      </KbButton>
                    </View>
                  )}
                  {hasServiceList && (
                    <View className='kb-margin-sm-lr kb-service-options-item'>
                      <KbButton
                        onClick={this.handleSubmitForm}
                        className='kb-button'
                        circle
                        type='primary'
                      >
                        确定
                      </KbButton>
                    </View>
                  )}
                </View>
              </View>
            )}
          </Fragment>
        }
      >
        {hasServiceList ? (
          <View className='kb-service-wrap'>
            <View className={rootCls}>
              <AtTabs
                className={`kb-tabs-button ${isCn ? 'kb-hideTab' : ''}`}
                tabList={serviceList}
                current={current}
                onClick={this.handleSelectService}
                swipeable={!!(serviceList && serviceList.length > 1)}
              >
                {serviceList.map((item, index) => {
                  const { key, inputConfig, customKey, required } = item;
                  const serviceTip =
                    relationType == 'brand' && key == 'pro_price'
                      ? serviceTips(brand, { pageSource, item })['brandProPrice']
                      : serviceTips(brand, { pageSource, item })[key];
                  const showProPriceDec = hasProPrice(key);
                  console.log(showProPriceDec, key, data, 'showProPriceDec')
                  const declarationList = getDeclarationList(showProPriceDec);
                  const labelCls = classNames('kb-service-form-label kb-spacing-lg-l', {
                    'kb-service-form-required': !!required,
                  });
                  const formName = customKey || key;
                  return (
                    <AtTabsPane current={current} index={index} key={key}>
                      <View className='kb-service-pane'>
                        {!isCn && (
                          <Fragment>
                            <View className='kb-form kb-service-form kb-service-form__active'>
                              <View className='kb-service-form-item'>
                                <View className={labelCls}>{inputConfig.label}</View>
                                {key !== 'arrive_pay' ? (
                                  <Fragment>
                                    <AtInput
                                      className='kb-service-form-item__input'
                                      placeholder='填写金额'
                                      value={data[formName]}
                                      type='digit'
                                      cursor={-1}
                                      disabled={dynamicDisabled[key]}
                                      onChange={this.handleInputChange.bind(this, formName)}
                                      adjustPosition={false}
                                      enableNative
                                      onFocus={this.handleBar.bind(this)}
                                      onBlur={this.handleBar.bind(this)}
                                    />
                                    <View className='kb-spacing-lg-lr'>元</View>
                                  </Fragment>
                                ) : (
                                  <View className='at-col'>
                                    <AtSwitch
                                      onChange={this.handleSwitchChange.bind(this, formName)}
                                      border={false}
                                      size='small'
                                      color={process.env.MODE_ENV == 'wkd' ? '#099dff' : '#00a173'}
                                      checked={data[formName] > 0}
                                    />
                                  </View>
                                )}
                              </View>
                              {showProPriceDec && data.cost_value && (
                                <View className='kb-spacing-md-t'>
                                  <View className='kb-service-form-item'>
                                    <View className='kb-service-form-label kb-spacing-lg-l'>
                                      保价费
                                    </View>
                                    <View className='at-col at-input kb-service-form-item__input'>
                                      <View className='at-input__container'>
                                        <View className='at-input__input'>{data.cost_value}</View>
                                      </View>
                                    </View>
                                    <View className='kb-spacing-lg-lr'>元</View>
                                  </View>
                                </View>
                              )}
                            </View>
                            {showProPriceDec && (
                              <View className='kb-color__orange kb-spacing-md-tb kb-size__base'>
                                注：保价费率{cost}%
                              </View>
                            )}
                          </Fragment>
                        )}
                        <View className='kb-tips-scroll'>
                          <View className='kb-tips-scroll__inner'>
                            <ScrollView scrollY className='kb-scrollview'>
                              <KbServiceTips
                                serviceTip={serviceTip}
                                declarationList={declarationList}
                                relationType={relationType}
                                current={key}
                              />
                            </ScrollView>
                          </View>
                        </View>
                      </View>
                    </AtTabsPane>
                  );
                })}
              </AtTabs>
            </View>
          </View>
        ) : (
          <View className='kb-empty-service'>
            <AtIcon
              prefixClass='kb-icon'
              value='no-package'
              className='kb-icon-size__xxl kb-color__grey'
            />
            <View className='kb-spacing-lg-t kb-color__grey kb-size__lg'>
              还没有开启增值服务功能哦～
            </View>
          </View>
        )}
      </KbPage>
    );
  }
}

export default Index;
