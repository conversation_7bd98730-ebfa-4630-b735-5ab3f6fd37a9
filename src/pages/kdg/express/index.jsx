/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  cabinetSyncOrderPlace,
  formatGridInfo,
  CABINET_GRID_LIST,
} from '@/components/_pages/kdg/express/utils';
import KbModal from '@base/components/modal';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import request from '@base/utils/request';
import WebSocket from '@base/utils/request/webSocket';
import { debounce, getPage } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import MD5 from 'crypto-js/md5';
import isFunction from 'lodash/isFunction';
import { AtButton, AtIcon } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  };
  constructor() {
    // console.log('页面参数', this.$router.params);
    this.state = {
      activeId: '1',
      gridInfo: {}, // 打开的快递柜格口信息
      timer: 0, // 倒计时
    };
    this.navProps = {
      title: '选择格口',
      hideIcon: true,
    };
  }

  componentWillUnmount() {
    this.clear();
    this.ws.destroy();
  }

  handleUpdate = (data) => {
    if (!data.logined) return;
    // console.log('handleUpdate', data);
    this.connect();
  };

  connect = () => {
    const { loginData } = this.props;
    const { userInfo } = loginData || {};
    const { sessionid, openid } = userInfo || {};
    this.ws = new WebSocket({
      url: 'wss://cabinet.kuaidihelp.com:19501',
      pathname: '/web',
      heart: {
        action: '/v1/Cabinet/heartbeat',
        data: { timestamp: Date.now() / 1000, id: `${MD5(openid)}`, sessionid },
      },
      onOpen: () => {
        // console.log('this.ws', this.ws);
        this.showLoading('正在连接 ...');
        this.sendMessage({ type: 'get_cabinet_count' });
      },
      onMessage: (res) => {
        this.getMessage(res);
      },
      onStatusChange: (status) => {
        console.log('链接快递柜状态', status);
      },
    });
  };

  getMessage(res) {
    // console.log('res', res);
    const { code, msg, data: messageData } = res || {};
    const { type, data } = messageData || {};
    const { activeId } = this.state;
    if (code != 0) {
      Taro.kbToast({
        text: msg,
      });
      this.hideLoading();
      return;
    }
    const keys = [
      'get_cabinet_count',
      'customer_open_window_receipt',
      'customer_post_goods_exception',
      'customer_cancel_post_success',
      'customer_cancel_post_fail',
    ];
    // 统一处理loading方法
    if (keys.includes(type)) {
      this.hideLoading();
    }
    switch (type) {
      // 获取格口数量
      case 'get_cabinet_count':
        this.loading.close();
        const count = {};
        CABINET_GRID_LIST.forEach((item) => {
          count[item.key] = data[item.index] || 0;
        });
        this.setState({
          count,
        });
        break;
      // 开门成功
      case 'customer_open_window_receipt':
        Taro.kbToast({ text: '开门成功' });
        this.setState({
          gridInfo: formatGridInfo({ ...data, size: activeId }),
        });
        this.startCountDown();
        break;
      // 开门异常
      case 'customer_post_goods_exception':
        Taro.kbToast({
          text: data.message,
        });
        break;
      // 取消成功
      case 'customer_cancel_post_success':
        Taro.kbToast({
          text: '取消投递成功',
          onClose: () => {
            this.setGridInfo({
              gridId: '',
            });
          },
        });
        break;
      // 取消异常
      case 'customer_cancel_post_fail':
        Taro.kbToast({
          text: data.message,
        });
        break;
      default:
        break;
    }
  }

  sendMessage(params) {
    const { type, data = {} } = params;
    const { order_id, dak_id, device_id, cabinet_id, brand, source } = this.$router.params;
    const { loginData } = this.props;
    const { userInfo } = loginData || {};
    const { openid, mobile, nickname } = userInfo || {};
    const { activeId } = this.state;
    const activeData = (activeId && CABINET_GRID_LIST.find((i) => i.index == activeId)) || {};
    const message = {
      action: 'forward',
      data: {
        id: `${MD5(openid)}`,
        device_id: device_id,
        form: 'customer',
        courier_id: openid,
        timestamp: Math.ceil(Date.now() / 1000),
        type,
        data: {
          courier_id: dak_id, // 驿站ID
          waybill_no: order_id, // order_id
          brand: brand,
          sid: `${MD5(openid)}`,
          ecId: cabinet_id, // 快递柜id
          phone: mobile,
          name: nickname,
          ...(data || {}),
        },
      },
    };
    this.ws.sendMessage(message);
    // 个别信息单独处理
    switch (type) {
      case 'post_deliver_reopen':
        // 重新开门没有回传，直接展示成功
        Taro.kbToast({
          text: '重新开门成功',
        });
        this.startCountDown();
        break;
      case 'post_deliver_complete': // 确认投递
        const prePage = getPage(-2);
        if (prePage && prePage.cleanKdgAddressInfo) {
          prePage.cleanKdgAddressInfo();
        }
        if (source == 'order') {
          Taro.kbToast({
            text: '投递成功',
            onClose: () => {
              Taro.navigator({
                post: {
                  type: 'refresh_cabinet',
                },
              });
            },
          });
          return;
        }
        // 确认投递没有回传，发送消息后直接往下走
        cabinetSyncOrderPlace({ order_id });
        Taro.kbToast({
          text: '投递成功',
          onClose: () => {
            Taro.navigator({
              url: 'order/result',
              target: 'self',
              options: {
                ...this.$router.params,
                isKdg: 1,
                kdgSize: activeData.short,
              },
            });
          },
        });
        break;
      default:
        break;
    }
  }

  setGridInfo(data = {}) {
    const { gridInfo } = this.state;
    this.setState({
      gridInfo: {
        ...gridInfo,
        ...data,
      },
    });
  }

  showLoading(text) {
    this.loading = Taro.kbToast({
      status: 'loading',
      text: text,
    });
  }

  hideLoading() {
    if (this.loading && isFunction(this.loading.close)) {
      this.loading.close();
    }
  }

  startCountDown() {
    // 开启倒计时
    const maxTimer = 60;
    let n = maxTimer;
    const changeTimer = (_n) => {
      this.setState({
        timer: _n,
      });
    };
    this.clear();
    changeTimer(n);
    this.countDownTimer = setInterval(() => {
      n--;
      if (n < 0) {
        this.clear();
        return;
      }
      changeTimer(n);
    }, 1000);
  }

  clear() {
    this.countDownTimer && clearInterval(this.countDownTimer);
  }

  handleSelect(item) {
    const { count } = this.state;
    const isCanUse = !!count[item.key];
    if (!isCanUse) {
      Taro.kbToast({
        text: '暂无可用格口！',
      });
      return;
    }
    this.setState({
      activeId: item.index,
    });
  }

  handleOpenKdg = debounce(() => {
    const { activeId, count } = this.state;
    const activeData = (activeId && CABINET_GRID_LIST.find((i) => i.index == activeId)) || {};
    const canUse = count[activeData.key] > 0;
    if (!canUse) {
      Taro.kbToast({
        text: '无可用格口,请选择其他格口',
      });
      return;
    }
    this.showLoading('正在开门 ...');
    this.sendMessage({
      type: 'post_deliver_open',
      data: {
        size: activeId,
      },
    });
  });

  // 重新打开
  reOpen = debounce(() => {
    const { gridInfo } = this.state;
    this.sendMessage({
      type: 'post_deliver_reopen',
      data: {
        gridId: gridInfo.gridId,
      },
    });
  });

  // 取消投递
  cancelCabinet = debounce(() => {
    const { gridInfo } = this.state;
    const { gridId, size } = gridInfo;
    const sendMsg = (have_package) => {
      this.showLoading('正在取消 ...');
      this.sendMessage({
        type: 'post_deliver_cancel',
        data: {
          gridId,
          size,
          have_package,
        },
      });
    };
    sendMsg('1');
    return;
    Taro.KbModal({
      title: '取消投递',
      content: '您是否有包裹取回?',
      top: false,
      closeOnClickOverlay: false,
      closable: false,
      cancelButtonProps: { type: 'secondary' },
      cancelText: '无包裹',
      confirmText: '有包裹',
      onCancel: () => {
        sendMsg('0');
      },
      onConfirm: () => {
        sendMsg('1');
      },
    });
  });

  // 投递完成
  finishCabinet = debounce(() => {
    const { gridInfo } = this.state;
    const { gridId, size } = gridInfo;
    this.sendMessage({
      type: 'post_deliver_complete',
      data: {
        gridId,
        size,
      },
    });
  });

  navigatorBackModal() {
    const { order_id } = this.$router.params;
    Taro.kbModal({
      title: '提示',
      content: [
        '您还没有选择格口开柜，将包裹放入柜中',
        { text: '是否确认取消寄件？', className: 'kb-text__center' },
      ],
      cancelText: '取消寄件',
      confirmText: '继续寄件',
      top: false,
      closeOnClickOverlay: false,
      closable: false,
      onCancel: () => {
        request({
          url: '/api/weixin/mini/minpost/order/cancel',
          data: {
            order_id,
            refund_source: 'user_cancel',
          },
          toastSuccess: '取消成功',
          toastError: true,
          onThen: ({ code }) => {
            if (`${code}` === '0') {
              Taro.navigator();
            }
          },
        });
      },
    });
  }

  render() {
    const { activeId, isOpened, count = {}, gridInfo = {}, timer, ...rest } = this.state;
    const activeData = (activeId && CABINET_GRID_LIST.find((i) => i.index == activeId)) || {};
    return (
      <KbPage
        {...rest}
        onUpdate={this.handleUpdate}
        navProps={this.navProps}
        renderNavLeft={
          <View hoverClass='kb-hover' onClick={this.navigatorBackModal.bind(this)}>
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-color__white kb-icon-size__md kb-icon__direction-left'
            />
          </View>
        }
      >
        <KbScrollView
          full
          renderFooter={
            <View className='kb-kdgExpress-footer' onClick={this.handleOpenKdg.bind(this)}>
              <AtButton type='primary' circle>
                一键开柜（{activeData.short}）
              </AtButton>
            </View>
          }
        >
          <View className='kb-kdgExpress-list'>
            {CABINET_GRID_LIST.map((item) => {
              const isActive = activeId == item.index;
              return (
                <View
                  className={`kb-kdgExpress-list-item ${
                    isActive ? 'kb-kdgExpress-list-item--active' : ''
                  }`}
                  onClick={this.handleSelect.bind(this, item)}
                  hoverClass='kb-hover-opacity'
                  key={item.key}
                >
                  <View className='icon' />
                  <View className='grid'>
                    <View className={`grid-inner grid-inner-${item.index}`} />
                  </View>
                  <View className='title'>{item.title}</View>
                  <View className='desc'>{item.desc}</View>
                  <View className='num'>剩余：{count[item.key] || 0}个</View>
                </View>
              );
            })}
          </View>
        </KbScrollView>
        <KbModal
          isOpened={!!gridInfo && gridInfo.gridId}
          top={false}
          closeOnClickOverlay={false}
          closable={false}
          onCancel={this.cancelCabinet.bind(this)}
          onConfirm={this.finishCabinet.bind(this)}
          cancelButtonProps={{ type: 'secondary' }}
          cancelText='取消投递'
          confirmText='投递完成'
          className='kb-kdgExpress-gridModal'
        >
          <View className='gridModal'>
            <View className='gridModal-extra'>
              {timer > 0 ? (
                <View className='kb-color__brand'>{timer}s</View>
              ) : (
                <View onClick={this.reOpen.bind(this)}>重新打开</View>
              )}
            </View>
            <View className='gridModal-title'>
              <Text className='kb-color__brand kb-size__xxl'>
                {gridInfo.grid_number == 1 && gridInfo.number
                  ? `${gridInfo.number}格口`
                  : gridInfo.num}
              </Text>
              {timer <= 0 ? '柜门已关闭' : '柜门已开'}
            </View>
            {/* <View className='gridModal-list'>
              {CABINET_GRID_LIST.map((item) => {
                const active = activeId == item.index;
                const itemCls = classNames(
                  'gridModal-list-item',
                  `gridModal-list-item-${item.index}`,
                  {
                    'gridModal-list-item--active': active,
                  },
                );
                return (
                  <View className={itemCls} key={item.key}>
                    <View className='cell'>{active ? `${gridInfo.num}` : ''}</View>
                    <View className='cell' />
                  </View>
                );
              })}
            </View> */}
            <View className='gridModal-desc'>请放置包裹后及时关门</View>
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
