/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-kdgExpress {
  &-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-bottom: 30px;
    &-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      width: 340px;
      margin-top: $spacing-v-md;
      padding: $spacing-h-md * 2;
      background: $color-white;
      border: $border-lightest;
      border-color: $color-white;
      border-radius: $border-radius-md;
      .icon {
        position: absolute;
        top: -$width-base;
        right: -$width-base;
        width: 50px;
        height: 50px;
        background: #e6e6e6;
        border-radius: 0px $border-radius-md 0px 40px;
        &::after {
          position: absolute;
          top: 10px;
          right: 10px;
          width: 20px;
          height: 12px;
          border: $border-lightest;
          border-color: $color-white;
          border-top: none;
          border-right: none;
          transform: rotate(-45deg);
          content: '';
        }
      }
      .grid {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 130px;
        height: 130px;
        background: #fff4e1;
        border-radius: 50%;
        &-inner {
          position: relative;
          width: 68px;
          height: 30px;
          background: #f5be6c;
          &::after {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 5px;
            height: 5px;
            background: $color-white;
            border-radius: 50%;
            content: '';
          }
        }
        &-inner-0 {
          height: 20px;
        }
        &-inner-1 {
          height: 30px;
        }
        &-inner-2 {
          height: 40px;
        }
        &-inner-3 {
          height: 50px;
        }
        &-inner-4 {
          height: 60px;
        }
        &-inner-5 {
          height: 60px;
        }
        &-inner-6 {
          height: 60px;
        }
      }
      .title {
        margin-top: $spacing-v-xl;
        font-weight: bold;
        font-size: $font-size-xl;
      }
      .desc {
        margin-top: $spacing-v-sm;
        color: $color-grey-2;
        font-size: $font-size-base;
      }
      .num {
        margin-top: $spacing-v-xl;
        padding: 5px $spacing-h-md;
        color: $color-grey-1;
        font-size: $font-size-base;
        background: #f3f3f3;
        border-radius: 20px;
      }
      &--active {
        border-color: $color-brand;
        .icon {
          background: $color-brand;
        }
      }
    }
  }
  &-gridModal {
    padding: 0;
    .gridModal {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: $spacing-h-xl + 20px;
      &-extra {
        position: absolute;
        top: 10px;
        right: $spacing-v-md;
        font-size: $font-size-base;
      }
      &-title {
        margin: $spacing-v-xl 0;
        color: #000;
        font-weight: bold;
        font-size: $font-size-xl;
      }
      &-list {
        padding: $spacing-h-md;
        background: $color-grey-5;
        &-item {
          display: flex;
          justify-content: space-around;
          box-sizing: border-box;
          width: 470px;
          margin-bottom: $spacing-h-md;
          .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 220px;
            height: 100%;
            font-size: $font-size-base;
            background: $color-white;
          }
          &:last-child {
            margin-bottom: 0;
          }
          &-1 {
            height: 60px;
          }
          &-2 {
            height: 80px;
          }
          &-3 {
            height: 100px;
          }
          &-4 {
            height: 120px;
          }
          &-5 {
            height: 120px;
          }
          &-6 {
            height: 120px;
          }
          &--active {
            .cell {
              &:first-child {
                color: $color-white;
                background: $color-brand;
              }
            }
          }
        }
      }
      &-desc {
        margin-top: $spacing-v-lg;
        color: $color-grey-2;
        font-size: $font-size-lg;
      }
    }
  }
  &-footer {
    padding: $spacing-h-md;
    background: $color-white;
  }
}
