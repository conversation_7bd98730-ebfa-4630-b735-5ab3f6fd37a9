/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.lotteryDetails {
  position: relative;
  .head {
    position: relative;
    width: 100%;
    margin-bottom: 30px;
    .headColor {
      width: 100%;
      height: 200px;
      margin-bottom: -50px;
      background: #ed663f;
      border-radius: 0 0 250px 250px;
    }
    .cardBox {
      position: relative;
      width: 94%;
      margin: -180px auto 0;
      padding: 20px 0;
      background: #fff;
      border-radius: 20px;
      .information {
        flex: 1;
        align-items: flex-end;
        padding-bottom: 20px 0;
        .allName {
          margin: 20px 20px 40px;
          color: #995850;
          font-weight: 600;
          font-size: 30px;
        }
        .description {
          display: flex;
          align-items: center;
          color: #f4d096;
          font-size: 26px;
        }
      }
      .ml-20 {
        margin-left: 20px;
      }
    }
  }
  .winningModal {
    .btn {
      position: absolute;
      top: 74%;
      left: 50%;
      width: 80%;
      height: 80px;
      color: red;
      font-size: 40px;
      line-height: 80px;
      text-align: center;
      transform: translateX(-50%);
    }
    .share {
      position: absolute;
      width: 100%;
      height: 100%;
      opacity: 0;
    }
    .popupTitle {
      position: absolute;
      top: 70px;
      left: 50%;
      width: 100%;
      color: red;
      font-weight: 600;
      font-size: 50px;
      text-align: center;
      transform: translateX(-50%);
    }
    .lotteryTxt {
      position: absolute;
      top: 170px;
      left: 50%;
      width: 70%;
      padding: 20px 0;
      color: #7a400e;
      font-size: 25px;
      letter-spacing: 1px;
      text-align: center;
      background: #ffd6b4;
      border-radius: 50px;
      transform: translateX(-50%);
    }
    .memberPopupTitle {
      position: absolute;
      top: 360px;
      left: 50%;
      width: 100%;
      color: #f62c13;
      font-weight: 600;
      font-size: 50px;
      text-align: center;
      transform: translateX(-50%);
    }
    .memberPopupDesc {
      position: absolute;
      top: 410px;
      left: 50%;
      width: 70%;
      padding: 20px 0;
      color: #f62c13;
      text-align: center;
      transform: translateX(-50%);
    }
    .openMemberPopupTitle {
      position: absolute;
      top: 318px;
      left: 50%;
      width: 100%;
      color: #ee8039;
      font-weight: 600;
      font-size: 56px;
      text-align: center;
      transform: translateX(-45%);
    }
    .openMemberPopupBtn {
      position: absolute;
      top: 590px;
      left: 50%;
      width: 70%;
      padding: 20px 0;
      color: #f62c13;
      font-weight: bold;
      font-size: 50px;
      text-align: center;
      transform: translateX(-50%);
      &__after {
        position: absolute;
        top: 0;
        right: -14px;
        padding: $spacing-h-xs $spacing-v-sm;
        color: $color-white;
        font-size: $font-size-base;
        background: rgba($color: $color-black-1, $alpha: 0.5);
        border-radius: 10px 10px 10px 0;
      }
      &__money {
        font-size: 45px;
      }
      &__month {
        font-size: 30px;
      }
      &__text {
        font-size: 40px;
      }
    }
    .memberOpenTime {
      top: 490px;
      width: unset;
      padding: 10px;
      font-size: 18px;
      background: #fff0dc;
    }
    .lotterys {
      position: absolute;
      top: 375px;
      left: 50%;
      width: 75%;
      color: #ffcc00;
      font-weight: 600;
      font-size: 28px;
      text-align: center;
      transform: translateX(-50%);
    }
    .regret {
      position: absolute;
      top: 120px;
      left: 50%;
      color: #7a400e;
      text-align: center;
      transform: translateX(-50%);
    }
    .agreement {
      position: absolute;
      bottom: 170px;
      left: 66%;
      display: flex;
      align-items: center;
      width: 100%;
      transform: translateX(-50%);

      .at-button__wxbutton {
        color: #f30;
      }
    }
    .save_button {
      position: absolute;
      bottom: 40px;
      left: 50%;
      width: 80%;
      height: 100px;
      transform: translateX(-50%);
      opacity: 0;
    }
  }
  .modal {
    .content {
      height: 500px;
    }
  }

  //集分宝
  @keyframes mymoves {
    0% {
      transform: scale(0.9);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(0.9);
    }
  }
  .Jifenbao_Box {
    position: absolute;
    top: 430px;
    right: 10px;
    width: 100px;
    height: 100px;
    transform: scale(1);
    animation: mymoves 2.5s infinite;
    .img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .wkd_lotterys {
    height: auto;
    padding: 0;
    border: none;
  }

  .kb-scrollview {
    height: 600px;
  }
  .boostList {
    width: 94%;
    margin: 10px auto;
    &__title {
      font-size: $font-size-lg;
    }
    &__desc {
      margin: 10px auto;
      font-size: $font-size-xs;
    }
    &__btn {
      font-size: $font-size-lg;
    }
    &__receiveBtn {
      font-size: $font-size-lg;
      background: #fdf7f3;
      border: 1px solid #edd5cf;
    }
    .kb-hover {
      color: #ccc;
      border: 1px solid #ccc;
    }
  }

  .fc-red {
    color: red;
  }
}
.hg-93 {
  position: relative;
  height: 93vh;
  overflow: auto;
}

.adStyle {
  width: 92% !important;
  margin: 3% auto 0 !important;
}
.wImg {
  display: block;
  width: 100%;
}
.avatar_url {
  position: absolute;
  top: 230px;
  left: 50%;
  width: 90px;
  height: 90px;
  border: 6px solid #fcb430;
  border-radius: 50%;
  transform: translateX(-50%);
}

// Description

.descriptionBox {
  padding: 20px;
  .title {
    margin: 0 0 15px 0;
    font-weight: 900;
    font-size: 32px;
  }
  .between {
    justify-content: space-between;
  }
}

.ml-10 {
  margin-left: 10px;
}
.ml-5 {
  margin-left: 5px;
}
.mr-20 {
  margin-right: 20px;
}
.fc-red {
  color: red;
}

//WinningStatus

.noprize {
  line-height: 50px;
  text-align: center;
  .noprizeTitle {
    color: black;
    font-weight: 600;
    font-size: 30px;
  }
  .other {
    font-size: 24px;
  }
}
//Invitation
.InvitationBox {
  margin: 20px 0;
  padding: 20px;
  color: #6c6c6c;
  font-weight: 600;
  font-size: 23px;
  font-size: 28px;
  line-height: 37px;
  background-color: #ffffff;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    color: black;
    font-weight: 500;
    font-size: 28px;
  }
  .friendsBar {
    display: flex;
    justify-content: space-between;
    .friendBox {
      width: 80px;
      height: 112px;
      margin-top: 20px;
      .portrait {
        display: block;
        width: 80px;
        height: 80px;
      }
      .portraitbtn {
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        width: 80px;
        height: 80px;
        opacity: 0;
      }
      .name {
        width: 100%;
        height: 32px;
        font-size: 19px;
        line-height: 32px;
        text-align: center;
      }
    }
  }

  .fc-red {
    color: red;
  }
  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .relative {
    position: relative;
  }
}

//Rule
.RuleBox {
  margin: 0px 20px 40px;
  padding: 20px;
  color: #6c6c6c;
  font-size: 23px;
  line-height: 37px;
  background-color: #ffffff;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    color: black;
    font-weight: 500;
    font-size: 28px;
    .more {
      color: #6c6c6c;
      font-weight: 100;
      font-size: 24px;
    }
  }
}

//Participation
.ParticipationBox {
  margin: 20px 0;
  padding: 20px;
  color: #6c6c6c;
  font-weight: 600;
  font-size: 23px;
  font-size: 28px;
  line-height: 37px;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    color: black;
    font-weight: 500;
    font-size: 28px;
    .more {
      margin: 0 auto;
    }
  }
  .people {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .friendBox {
      width: 65px;
      height: 65px;
      margin: 2px 10px;
      .portrait {
        display: block;
        width: 65px;
        height: 65px;
      }
    }
  }

  .fc-blue {
    color: #3794f3;
  }
  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
