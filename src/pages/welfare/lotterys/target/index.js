/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbActivityCard from '@/components/_pages/welfare/lotterys/card/index';
import Fotter from '@/components/_pages/welfare/lotterys/fotter';
import Winning from '@/components/_pages/welfare/lotterys/winning';
import KbWinningStatus from '@/components/_pages/welfare/lotterys/winningStatus';
import KbWinningList from '@/components/_pages/welfare/lotterys/winningList';
import KbLotteryModal from '@/components/_pages/welfare/lotterys/lotteryModal/index';
import KbPage from '@base/components/page';
import request from '@base/utils/request';
import { debounce, reportAnalytics } from '@base/utils/utils';
import { Block, Button, Image, ScrollView, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { AtFloatLayout } from 'taro-ui';
import KbPrizeModal from '@/components/_pages/welfare/lotterys/prizeModal';
import methods from '../methods/details';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
export default class Index extends Component {
  config = {
    navigationBarTitleText: '抽奖详情',
    enablePullDownRefresh: true,
    navigationBarBackgroundColor: '#e56d43',
  };
  constructor(props) {
    super(props);
    this.state = {
      activityId: '',
      activityData: {
        shareList: [],
        ParticipationList: [],
        winningList: [],
        is_winning: false,
      },
      cardActivity: {},
      isWinOpened: false,
      whetherHasActivity: false,
      lList: [],
      isOpened: false,
      modalName: '',
      lotteryStatus: false,
      loginStatus: false, //是否授权个人信息
      agreementStatus: true,
      boostSwitch: false,
      boostList: [],
      lottery_history: [],
      me: {},
      vipInfo: {
        is_first: '0',
        status: '0',
        first_price: '1.6',
      },
      prizeRanking: {},
      prizeOpened: false,
    };
    for (var i in methods) {
      this[i] = methods[i].bind(this);
    }
    this.getVipInfos = debounce(this.getVipInfo, 300);
    this.getInit = debounce(this.getInit, 300);
  }

  componentDidMount() {
    reportAnalytics({
      key: 'welfare.lotterys',
      title: '每日抽奖详情',
    });
    this.getInit();
  }

  componentDidShow() {
    this.intervalometer();
    this.getInit();
  }

  componentWillUnmount() {
    clearInterval(this.timer);
  }

  onPullDownRefresh = () => {
    this.setState(
      {
        lList: [],
      },
      () => this.getDetails(),
    );
  };

  onPayMoney = () => {
    const { vipInfo: { status = 0 } = {} } = this.state;
    if (status) {
      reportAnalytics({
        key: 'welfare.lotterys',
        title: '会员抽奖',
      });
    }
    reportAnalytics({
      key: 'welfare.lotterys',
      title: '点击抽奖',
    });
    const { activityShareId, activityId } = this.state;
    request({
      url: '/g_wkd/v2/activity/VxLottery/join',
      toastLoading: true,
      data: {
        share_kb_id: activityShareId,
        lid: activityId,
      },
    }).then((res) => {
      const { code, data, msg } = res;
      this.getDetails(this.timely ? {} : { modalName: '参与成功' });
      if (code == 0) {
        reportAnalytics({
          key: 'welfare.lotterys',
          title: '参与成功',
        });
        if (data.share_kb_id) {
          reportAnalytics({
            key: 'welfare.lotterys',
            title: '成功助力人数',
          });
        }
        if (this.timely) {
          this.onOpenWin();
        }
      } else if (this.timely && [99015, 99016].includes(code)) {
        this.setState({
          isOpened: true,
          modalName: '未中奖',
        });
      } else {
        Taro.showToast({ title: msg });
      }
    });
  };

  onUpdate = () => {
    const { activityId } = this.state;
    this.getVipInfos();
    this.getDetails({ id: activityId });
  };

  handleCard = () => {};

  render() {
    const { navigationBarTitleText } = this.config;
    const {
      me,
      activityData,
      whetherHasActivity,
      lList,
      isOpened,
      modalName,
      winningList,
      ranking,
      lotteryStatus,
      cardActivity,
      loginStatus,
      shareList,
      agreementStatus,
      vipInfo,
      boostSwitch,
      boostList,
      lottery_history,
      isWinOpened,
      prizeOpened,
      prizeRanking,
      ...rest
    } = this.state;
    return (
      <KbPage
        {...rest}
        title={navigationBarTitleText}
        unbackHome
        className='kb-main-clockIn'
        onUpdate={this.onUpdate}
      >
        <View className={`lotteryDetails ${whetherHasActivity ? 'hg-93' : ''}`}>
          <View className='head'>
            <View className='headColor' />
            <View className='cardBox'>
              {cardActivity.id ? (
                <KbActivityCard
                  Size={300}
                  params={cardActivity}
                  handleCard={this.handleCard}
                  showAttend={false}
                />
              ) : (
                <KbActivityCard showAttend={false} />
              )}
            </View>
          </View>
          <View style={{ padding: '0 30rpx' }}>说明</View>
          <View className='RuleBox'>
            <Text>{activityData.desc}</Text>
          </View>

          <KbWinningStatus
            ranking={ranking}
            lottery_history={lottery_history}
            activityData={activityData}
            handleBoost={this.handleBoost}
            onPayMoney={this.onPayMoney}
            onOpenWin={this.onOpenWin}
            onCallBack={this.getDetails}
            lotteryHistory={lottery_history}
          />

          <Block>
            {lotteryStatus ? (
              ''
            ) : !this.timely && activityData.is_lottery == 0 && whetherHasActivity ? (
              <View className='InvitationBox'>
                <View className='title'>
                  <View>
                    {`${activityData.times}` === '0' ? (
                      <View>
                        已获得<Text className='fc-red'>0</Text>位会员助力,也许大奖就是你
                      </View>
                    ) : (
                      <View>
                        中奖几率+
                        <Text className='fc-red'>{activityData.times}</Text>
                        倍，已超过
                        <Text className='fc-red'>{activityData.rank}</Text>
                        位参与者。
                      </View>
                    )}
                  </View>
                </View>
                <View className='friendsBar'>
                  {shareList.map((item) => {
                    return (
                      <View className='friendBox relative' key={item.id}>
                        <Image className='portrait' src={item.avatar_url}></Image>
                        {item.share ? (
                          <Button className='portraitbtn' onClick={this.handleBoost}></Button>
                        ) : null}
                        <View className='name ellipsis'>{item.nickname}</View>
                      </View>
                    );
                  })}
                </View>
              </View>
            ) : (
              ''
            )}
          </Block>
          <View className='ParticipationBox'>
            <View className='title'>
              <View className='more'>
                已有<Text>{Number(activityData.lCount) || 0}</Text>人参与
              </View>
            </View>
            <View className='people'>
              {lList.map((item) => {
                return (
                  <View className='friendBox' key={item.nickname}>
                    <Image
                      className='portrait'
                      src={
                        item.avatar_url ||
                        '//osscdn-kbad.kuaidihelp.com/admin/ad/2022/04/24/6264efc099394/icon_my.png'
                      }
                    />
                  </View>
                );
              })}
            </View>
          </View>
          <Winning datas={activityData} lists={winningList} />

          <KbLotteryModal
            isOpened={isOpened}
            modalName={modalName}
            vipInfo={vipInfo}
            activityData={activityData}
            agreementStatus={agreementStatus}
            onPay={this.onPay}
            onClose={this.onCloseCurtain}
            onSwitchAgree={this.onSwitchAgree}
            handleBoost={this.handleBoost}
          />

          <KbPrizeModal isOpened={prizeOpened} ranking={prizeRanking} onClose={this.onClosePrize} />

          <AtFloatLayout
            isOpened={boostSwitch}
            onClose={this.handleBoost}
            title={this.timely ? '完成任务增加抽奖次数' : '助力增加中奖率'}
          >
            <ScrollView scrollY className='kb-scrollview'>
              {boostList.map((item) => {
                const is_receive = item.is_receive == '1';
                return (
                  <View className='boostList'>
                    <View className='boostList__title'>{item.title}</View>
                    <View className='boostList__desc'>{item.desc}</View>
                    <Button
                      className={is_receive ? 'boostList__btn' : 'boostList__receiveBtn'}
                      hoverClass='kb-hover'
                      disabled={is_receive}
                      onClick={this.handleBoostTask.bind(this, item)}
                    >
                      {item.button_text}
                    </Button>
                  </View>
                );
              })}
              <View className=' kb-spacing-md-b'></View>
            </ScrollView>
          </AtFloatLayout>

          <KbWinningList
            isOpened={isWinOpened}
            onCallBack={this.getDetails}
            onClose={this.onCloseWin}
            lottery_history={lottery_history}
            onOpenPrize={this.onOpenPrize}
          />

          <View className='adStyle'>
            <ad-custom unit-id='adunit-e35a2d0abbde0b1d' />
          </View>
        </View>
        {whetherHasActivity ? <Fotter /> : null}
      </KbPage>
    );
  }
}
