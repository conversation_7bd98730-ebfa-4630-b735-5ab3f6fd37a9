/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import { myFormat } from '@/components/_pages/welfare/_utils/welfare.lotterys';

const methods = {
  getEveryDayList(nextPage) {
    let { allTab } = this.state;
    const { everyday } = allTab;
    request({
      url: '/g_wkd/v2/activity/VxLottery/mailList',
      data: {
        page: everyday.page || 1,
        page_size: 7,
        is_all: '1',
        show_num: 1,
        show_8day: 1,
        order_by: 'lottery_time_asc',
      },
    }).then((res) => {
      const { data, code } = res;
      if (code == 0) {
        const hasNextPage = !!data.list.length;
        const activityList =
          everyday.page === 1 ? data.list : [...everyday.activityList, ...data.list];
        const page = hasNextPage && nextPage ? everyday.page + 1 : everyday.page;
        this.setState({
          allTab: {
            ...allTab,
            everyday: {
              ...everyday,
              page,
              activityList,
              hasNextPage,
            },
          },
        });
      }
    });
  },

  getMyList(nextPage) {
    const { allTab, currentTab } = this.state;
    const { page: triggerPage, activityList: currentList } = allTab[currentTab];
    request({
      url: '/g_wkd/v2/activity/VxLottery/myList',
      toastLoading: false,
      data: {
        lottery_status: currentTab,
        page: triggerPage,
        page_zise: '7',
      },
    }).then((res) => {
      const { data, code } = res;
      if (code == 0) {
        const hasNextPage = !!data.lList.length;
        const activityList = triggerPage === 1 ? data.lList : [...currentList, ...data.lList];
        const page = hasNextPage && nextPage ? triggerPage + 1 : triggerPage;
        const currentData = {
          ...allTab,
          [currentTab]: {
            ...allTab[currentTab],
            page,
            activityList,
            hasNextPage,
          },
        };
        this.setState({
          allTab: {
            ...currentData,
          },
        });
      }
    });
  },

  handleCard(activityId, isLottery) {
    if (activityId) {
      const { allTab, currentTab } = this.state;
      this.setState({
        allTab: {
          ...allTab,
          [currentTab]: {
            ...allTab[currentTab],
            activityList: [],
            page: 1,
          },
        },
      });
      Taro.navigateTo({
        url: `/pages-0/pages/welfare/lotterys/details?activityId=${activityId}&isLottery=${isLottery}`,
      });
    }
  },

  lotteryStatus(details) {
    let now_time = +new Date();
    var remaining = myFormat(details.lottery_time) - now_time;
    if (remaining < 0) {
      if (+details.is_lottery == '0') {
        return true;
      }
    } else {
      return false;
    }
  },

  switchTab(triggerTab) {
    const { currentTab, allTab } = this.state;
    this.setLoadStatus();
    if (currentTab !== triggerTab) {
      this.setState(
        {
          currentTab: triggerTab,
          allTab: {
            ...allTab,
            [triggerTab]: {
              ...allTab[triggerTab],
              activityList: [],
              page: 1,
            },
          },
        },
        () => {
          if (triggerTab !== 'everyday') {
            this.getMyList(triggerTab);
          } else {
            this.getEveryDayList();
          }
        },
      );
    }
  },

  goEveryDay() {
    this.setState({
      currentTab: 'everyday',
      allList: this.state.everyDayList,
    });
  },

  setLoadStatus() {
    this.setState({
      status: 'loading',
    });
    let timer = null;
    clearTimeout(timer);
    timer = setTimeout(() => {
      this.setState({
        status: '',
      });
    }, 500);
  },
};
export default methods;
