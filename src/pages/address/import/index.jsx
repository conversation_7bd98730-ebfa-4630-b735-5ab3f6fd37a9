/* eslint-disable no-unused-vars */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbNoticeBar from '@base/components/notice-bar';
import KbPage from '@base/components/page';
import KbTable from '@base/components/table';
import KbImportMode from '@/components/_pages/address/import-mode';
import { execFileDataApis, getUploadFileApis } from '@/components/_pages/address/_utils';
import request from '@base/utils/request';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro, { Component, Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtButton } from 'taro-ui';
import KbCheckbox from '@base/components/checkbox';
import {
  createExcelColumn,
  formatExcelAddress,
  formatList,
} from '@/components/_pages/address/_utils/import';
import { PLATFORM_NAME } from '@/utils/config';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '文件导入',
  };

  constructor(props) {
    super(props);
    this.state = {
      path: '',
      name: '',
      count: 1,
      type: 'file',
      extension: ['xls', 'xlsx', 'txt'],
      list: [],
      importMode: 'normal',
      isFileHasSender: false, // 文件中是否包含发件人信息
      range: createExcelColumn(),
    };
    this.allType = '全部信息';
    this.columns = [
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        hoverClass: 'kb-hover',
        className: 'kb-excel__table--bar',
        render: (value) => {
          const rItem = this.state.range.find((i) => i.value == value) || {};
          return {
            value: rItem.text || '不导入',
            className: `kb-color__${value ? 'red' : 'grey'}`,
          };
        },
      },
      {
        title: '导入数据1',
        dataIndex: 'd1',
        key: 'd1',
        className: 'kb-excel__table--grey',
      },
      {
        title: '导入数据2',
        dataIndex: 'd2',
        key: 'd2',
        className: 'kb-excel__table--grey',
      },
    ];
  }

  componentDidUpdate(_prevProps, prevState) {
    const { importMode: pre_importMode, isFileHasSender: pre_isFileHasSender } = prevState || {};
    const { importMode, isFileHasSender } = this.state;
    // 更新可选配置项
    if (pre_importMode != importMode || pre_isFileHasSender != isFileHasSender) {
      this.setState({
        range: createExcelColumn({
          importMode: importMode,
          isFileHasSender,
        }),
      });
    }
  }

  // 清空
  clearImportted = (then) => {
    this.setState(
      {
        path: '',
        file: '',
        name: '',
        list: [],
      },
      then,
    );
  };

  // 返回上一页，或重新选择文件
  onCancel = () => {
    const { list } = this.state;
    if (list.length) {
      this.onSelect();
    } else {
      Taro.navigator();
    }
  };

  //   触发上传
  onConfirm = () => {
    const { path: filePath, name, list, file, size, isFileHasSender } = this.state;
    const { platform } = Taro.systemInfo || {};
    if (platform === 'windows') {
      Taro.kbModal({
        content: 'pc端可能暂时不支持导入excel文档，请打开使用手机端小程序操作！',
        confirmText: '我知道了',
      });
      return;
    }
    if (list.length) {
      let hasToPay = list.find((item) => item.type == '到付');
      let hasCollection = list.find((item) => item.type == '代收货款');
      if (hasToPay && hasCollection) {
        Taro.kbModal({
          content: '到付和代收货款不能同时存在，请重新选择！',
        });
        return;
      }
      // 立即下单
      const toastLoading =
        size > 400
          ? ['正在尝试导入中', '系统检测您的文件较大，如导入失败，请尝试分文件导入']
          : true;
      request({
        ...execFileDataApis({ allType: this.allType, list, file }),
        toastLoading,
        toastError: true,
        onThen: ({ data }) => {
          if (isArray(data) && data.length > 0) {
            console.log('解析数据', data);
            if (isFileHasSender) {
              Taro.navigator({
                key: 'importAddress',
                url: 'address/confirm',
                options: {
                  list: formatExcelAddress(data),
                },
              });
            } else {
              Taro.navigator({
                post: {
                  type: 'importAddress',
                  data: {
                    list: formatList(data),
                  },
                },
              });
            }
            this.clearImportted();
          }
        },
      });
    } else {
      if (!filePath) return;
      // 确认上传文件
      request({
        ...getUploadFileApis({ filePath, name }),
        requestDataType: 'file',
        toastError: true,
        onThen: (res) => {
          const {
            data: { path: file, data: list },
          } = res;
          if (file && isArray(list)) {
            const newList = [];
            list[0].map((item, index) => {
              newList.push({
                type: item,
                d1: list[1][index],
                d2: list[2][index],
              });
            });
            this.setState({
              file,
              list: newList,
            });
          }
        },
      });
    }
  };

  //   选择excel
  onSelect = () => {
    const { count, type, extension } = this.state;
    const { platform } = Taro.systemInfo || {};
    if (platform == 'mac' || platform == 'windows') {
      Taro.kbModal({
        content: 'PC端小程序暂不支持文件导入！,如有需要，请使用手机端小程序导入!',
      });
      return;
    }
    if (process.env.PLATFORM_ENV === 'alipay') {
      Taro.kbModal({
        content: '支付宝端暂不支持导入功能',
      });
      return;
    }
    Taro.chooseMessageFile({
      count,
      extension,
      type,
    })
      .then(({ tempFiles }) => {
        if (isArray(tempFiles)) {
          const [{ path, name, size }] = tempFiles;
          this.setState({
            list: [],
            path,
            size: size / 1024,
            name,
          });
        }
      })
      .catch(() => {});
  };

  // 表格点击
  onTableClick = (_, { col, row }) => {
    if (col === 0) {
      Taro.kbActionSheet({
        items: [...this.state.range],
        onClick: (_, actionItem) => {
          const list = [...this.state.list];
          const type = actionItem.value;
          const isExclude = ['备注'].includes(type);
          if (type && list.find((item) => item.type === type) && !isExclude) {
            Taro.kbToast({
              text: `请勿重复选择${type}`,
            });
            return;
          }
          list.splice(row, 1, {
            ...list[row],
            type,
          });
          this.setState({
            list,
          });
        },
      });
    }
  };

  handleChange(key, data) {
    switch (key) {
      case 'importMode':
        this.setState({
          importMode: data.value,
        });
        break;
      case 'isFileHasSender':
        this.setState({
          isFileHasSender: data,
        });
        break;
    }
  }

  render() {
    const { list, path, name, importMode, isFileHasSender, ...rest } = this.state;
    const { canToPay = '1', canCollection = '1' } = this.$router.params;

    const hasList = !!list.length;
    return (
      <KbPage
        {...rest}
        renderHeader={
          <View>
            <KbNoticeBar onClick={Taro.navigateToDocument.bind(this, 7, 'blank')}>
              点此了解Wps/Excel表格内下单打单 {'>'}
            </KbNoticeBar>
            {hasList ? (
              <Fragment>
                <View className='kb-import-fileContent at-row'>
                  <View
                    className='at-row at-row__align--center'
                    onClick={this.handleChange.bind(this, 'isFileHasSender', false)}
                    hoverClass='kb-hover'
                  >
                    <KbCheckbox checked={!isFileHasSender} />
                    <Text className='kb-margin-sm-l kb-size__base2'>文件中无发件人信息</Text>
                  </View>
                  <View
                    className='at-row at-row__align--center'
                    onClick={this.handleChange.bind(this, 'isFileHasSender', true)}
                    hoverClass='kb-hover'
                  >
                    <KbCheckbox checked={isFileHasSender} />
                    <Text className='kb-margin-sm-l kb-size__base2'>文件中有发件人信息</Text>
                  </View>
                </View>
                <KbImportMode
                  canToPay={canToPay == '1'}
                  canCollection={canCollection == '1'}
                  data={importMode}
                  onChange={this.handleChange.bind(this, 'importMode')}
                />
              </Fragment>
            ) : null}
          </View>
        }
        renderFooter={
          <View className='kb-import__footer at-row at-row__align--center'>
            <View>
              <AtButton circle type='secondary' onClick={this.onCancel}>
                {hasList ? '重新选择' : '取消'}
              </AtButton>
            </View>
            <View className='at-col kb-spacing-lg-l'>
              <AtButton circle type='primary' onClick={this.onConfirm} disabled={!path && !hasList}>
                导入
              </AtButton>
            </View>
          </View>
        }
      >
        {hasList ? (
          <View className='kb-import__list'>
            <KbTable columns={this.columns} dataSource={list} onClick={this.onTableClick} />
          </View>
        ) : (
          <ScrollView scrollY className='kb-scrollview'>
            <View className='kb-import__select'>
              <AtButton circle type='secondary' onClick={this.onSelect}>
                选择Excel
              </AtButton>
              {name && <View className='kb-import__select--name'>已选：{name}</View>}
            </View>
            <View className='kb-tips'>
              <View className='kb-tips__item'>1、支持Excel（.xls,.xlsx）文档导入</View>
              <View className='kb-tips__item'>
                2、Excel文件从{PLATFORM_NAME}聊天中选择，请先发送到{PLATFORM_NAME}
                好友，再从好友中选择
              </View>
              <View className='kb-tips__item'>3、确保一条收件信息一行表格数据</View>
              <View className='kb-tips__item'>4、导入数据请放在第一个工作薄中</View>
              <View className='kb-tips__item'>
                5、导入有误，可尝试将表格内容复制粘贴进文本文档(.txt)后，新建Excel表格，将其粘贴保存再导入
              </View>
            </View>
          </ScrollView>
        )}
      </KbPage>
    );
  }
}

export default Index;
