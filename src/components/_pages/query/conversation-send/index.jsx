/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtIcon, AtInput } from 'taro-ui';
import { uploadImage } from '@/utils/qy';
import KbSubscribe from '@base/components/subscribe';
import './index.scss';

const KbSendMessage = (props) => {
  const { onSend, disabled } = props;

  const [text, setText] = useState();

  const handelSend = () => {
    if (disabled) return;
    if (!text) {
      Taro.kbToast({
        text: '请输入回复内容',
      });
      return;
    }
    setText('');
    onSend({ comment: text });
  };

  const handleChooseImg = () => {
    if (disabled) return;
    Taro.chooseMedia({
      count: 3,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const { tempFiles } = res;
        const fileList = tempFiles.map((v) => v.tempFilePath);
        uploadImage({
          filePath: fileList,
          type: 'feedback',
        }).then((res) => {
          const pic_list = res.data.map((v) => {
            const { code, data } = v || {};
            if (code == 0 && data.file_path) {
              return data.file_path;
            }
          });
          if (pic_list.length > 0) {
            onSend({
              pic_list,
            });
          }
        });
      },
    });
  };

  const handelCheck = () => {
    if (!text) {
      return {
        code: 1000,
        msg: '请输入回复内容',
      };
    }
    return {
      code: 0,
    };
  };

  return (
    <View className='kb-conversation__send kb-spacing-md'>
      <View className='at-row at-row__align--center'>
        <View className='kb-spacing-sm' onClick={handleChooseImg}>
          <AtIcon className='kb-color__grey' prefixClass='kb-icon' value='pic' size='16' />
        </View>
        <View className='flex-1 kb-conversation__send--input'>
          <AtInput
            disabled={disabled}
            placeholderClass='placeholder'
            cursor={-1}
            border={false}
            value={text}
            cursorSpacing={100}
            placeholder='请输入回复内容'
            onChange={(value) => setText(value)}
            alwaysEmbed
          />
        </View>
        <View hoverClass='kb-hover' className='kb-spacing-sm kb-size__lg'>
          <KbSubscribe
            className='kb-button__middle'
            type='custom'
            onSubscribe={handelSend}
            onCheck={handelCheck}
            action='feedback'
            circle
          >
            <View>发送</View>
          </KbSubscribe>
        </View>
      </View>
    </View>
  );
};

KbSendMessage.options = {
  addGlobalClass: true,
};

export default KbSendMessage;
