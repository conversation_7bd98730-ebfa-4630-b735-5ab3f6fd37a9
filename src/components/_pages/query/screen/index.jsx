/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Block, View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import './index.scss';
import useQueryScreen from './utils';

const QueryScreen = (props) => {
  const { onHandleOpen, showSendButton, showPickButton } = useQueryScreen(props);

  return showSendButton || showPickButton ? (
    <Block>
      <View className='kb-queryscreen'>
        {showPickButton && (
          <View className='kb-queryscreen__item'>
            <AtButton
              type='secondary'
              circle
              onClick={onHandleOpen.bind(null, { operate_type: 'pickup_open' })}
            >
              取件开门
            </AtButton>
          </View>
        )}
        {showSendButton && (
          <View className='kb-queryscreen__item'>
            <AtButton
              type='secondary'
              circle
              onClick={onHandleOpen.bind(null, { operate_type: 'send_open' })}
            >
              寄件开门
            </AtButton>
          </View>
        )}
      </View>
    </Block>
  ) : null;
};

QueryScreen.options = {
  addGlobalClass: true,
};

export default QueryScreen;
