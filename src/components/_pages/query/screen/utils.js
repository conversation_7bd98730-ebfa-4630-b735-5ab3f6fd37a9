/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import request from '@base/utils/request';
import Taro, { useCallback, useEffect, useMemo, useState } from '@tarojs/taro';
import { useLocationUpdate } from './location';

// listReady ==> 自动开门的动作需要在列表请求完成之后做

export default function useQueryScreen(props) {
  const { dakInfo, onShowStamptime, hasList, listReady, source = '', gateInfo = {} } = props;

  const { gate_id } = gateInfo || {};
  const { dakId } = dakInfo || {};
  const { onLocationStart, onLocationClose, locationData } = useLocationUpdate();
  const [autoOpen, setAutoOpen] = useState(true);
  const { pickup_open, mail_open } = dakInfo || {};
  const { latitude, longitude } = locationData || {};

  /**
   * 检查是否自动开门
   * 1.获取到位置信息
   * 2.获取到驿站ID
   * 3.autoOpen 用户点击开门按钮之后，不再触发自动开门。
   *    ===> 可能触发场景，自动开门时定位权限拒绝，到按钮开门时引导开始权限后，获取到了位置信息，马上就出发自动开门。
   * 4.listReady 列表请求完成之后再触发。 hasList初始值就是false,没法判断是否完成列表请求
   * 5.source  idCode 从身份码页面进入之后，不再触发自动开门。
   *
   */
  const onLoadOpen = useMemo(
    () => !!(!!locationData && dakId && autoOpen && listReady && source != 'idCode'),
    [locationData, dakId, autoOpen, listReady, source],
  );

  // 检查实名认证信息
  const checkRealnameStatus = () => {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/weixin/mini/user/Bind/getUserRealnameStatus',
        onThen: ({ code, data = {}, msg }) => {
          if (code == 0) {
            if (data.realname_status == '1') {
              resolve();
            } else {
              Taro.kbModal({
                content: '请先完成实名认证',
                cancelText: '取消',
                confirmText: '去实名',
                onConfirm: () => {
                  Taro.navigator({
                    url: 'realname',
                    options: {
                      phone: data.mobile,
                    },
                  });
                },
                onCancel: () => {
                  reject();
                },
              });
            }
          } else {
            Taro.kbToast({
              text: msg,
            });
            reject();
          }
        },
      });
    });
  };
  // 输入取件码模式
  const inputPickupCode = (params) => {
    return new Promise((resolve) => {
      Taro.kbModal({
        top: false,
        template: [
          {
            className: 'kb-color__brand kb-size__xl  kb-spacing-xl-b kb-text__center',
            value: '取件开门',
          },
          {
            tag: 'at-input',
            placeholder: '填写取件码',
            value: params.pickup_code || '',
            circle: true,
            border: false,
            name: 'pickup_code',
          },
          {
            className: 'kb-color__red kb-size__base kb-spacing-md-t',
            value: params.msg || '',
          },
        ],
        cancelText: '取消',
        confirmText: '开门',
        onConfirm: (e) => {
          const { data: { pickup_code } = {} } = e;
          if (pickup_code) {
            resolve(pickup_code);
            return false;
          }
          Taro.kbToast({
            text: '请填写取件码',
          });
          return true;
        },
      });
    });
  };

  // 按钮方法 取件码模式｜ 寄件开门模式
  const onHandleOpen = async (params) => {
    // 需要点击开门的时候就关闭自动开门的触发逻辑
    setAutoOpen(false);
    // 验证实名制信息
    await checkRealnameStatus();
    // 验证location信息
    if (!latitude || !longitude) {
      onLocationStart();
      return;
    }

    // 取件码
    if (params.operate_type == 'pickup_open') {
      pickupCodeMode(params);
    } else {
      onOpenDoor({
        ...params,
      });
    }
  };

  // 取件码模式
  const pickupCodeMode = async (params = {}) => {
    let pickup_code = params.pickup_code || '';
    pickup_code = await inputPickupCode({ pickup_code, msg: params.msg });
    onOpenDoor({
      ...params,
      pickup_code,
    });
  };

  // 开门方法
  const onOpenDoor = useCallback(
    async (params = {}) => {
      const { operate_type = '' } = params;
      request({
        url: '/api/weixin/mini/waybill/record/gateOpen',
        data: {
          ...params,
          dak_id: dakId,
          latitude,
          longitude,
          gate_id,
        },
        onThen: ({ code, msg }) => {
          if (code == 0) {
            if (operate_type == 'direct_open' && !hasList) return;
            if (msg.indexOf('#') > -1) {
              const colorMap = {
                红色: '#ff003c',
                紫色: '#e861ff',
                黄色: '#fbc02d',
                绿色: '#00ff1e',
                蓝色: '#0026ff',
                青色: '#5dedff',
                白色: '#757575',
              };
              const template = msg.split('#').map((item) => {
                const dom = {
                  tag: 'view',
                  className: 'kb-display__inline-block',
                  value: item,
                };
                if (colorMap[item]) {
                  return {
                    ...dom,
                    style: `color: ${colorMap[item]}`,
                  };
                }
                return dom;
              });
              Taro.kbModal({
                top: false,
                centered: true,
                closable: false,
                template,
                confirmText: '我知道了',
              });
            } else {
              setTimeout(() => {
                Taro.kbToast({
                  text: msg,
                  duration: 5000,
                });
              }, 3000);
            }
          } else if (code == '5000' && operate_type == 'pickup_open') {
            // 取件码开门模式 重新唤起弹窗
            pickupCodeMode({
              ...params,
              msg,
            });
          } else {
            if (code == 23021402) {
              Taro.kbModal({
                content: msg,
                confirmText: '我知道了',
              });
            } else {
              setTimeout(() => {
                Taro.kbToast({
                  text: msg,
                  duration: 5000,
                });
              }, 3000);
            }
          }
        },
      });
    },
    [dakId, latitude, longitude, hasList, gate_id],
  );

  // 初始化
  const init = () => {
    setAutoOpen(true);
    onLocationStart();
  };

  useEffect(() => {
    // 触发自动开门
    if (onLoadOpen) {
      onOpenDoor({ operate_type: 'direct_open' });
    }
  }, [onLoadOpen]);

  // onshow时检查标识，是否重新init
  useEffect(() => {
    const authSymbol = Taro.kbGetGlobalDataOnce('AuthSystemSymbol');
    if (onShowStamptime && authSymbol == '1') {
      init();
    }
  }, [onShowStamptime]);

  // 首次加载时候开始位置监听
  useEffect(() => {
    init();
    return () => {
      onLocationClose();
    };
  }, []);

  return {
    onOpenDoor,
    onHandleOpen,
    showSendButton: mail_open == '1' && listReady && !hasList,
    showPickButton: pickup_open == '1' && listReady && !hasList,
    locationData,
  };
}
