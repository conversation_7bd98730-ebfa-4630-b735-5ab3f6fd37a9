/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro, { useState } from '@tarojs/taro';

export function useLocationUpdate() {
  const [locationData, setLocationData] = useState();

  const checkSystemAuth = () => {
    Taro.kbModal({
      content: '需开启微信的定位权限后重新进入',
      cancelText: '取消',
      confirmText: '开启',
      onConfirm: () => {
        wx.openAppAuthorizeSetting({
          success: () => {
            // 跳转系统设置页面标识 在onshow时候凭此标识重新触发定位
            Taro.kbSetGlobalData('AuthSystemSymbol', '1');
          },
        });
      },
    });
  };

  const checkLocationAuth = () => {
    return new Promise(async (resolve) => {
      Taro.getSetting({
        success: (res) => {
          const status = !!res.authSetting['scope.userLocation'];
          if (status) {
            resolve(true);
          } else {
            Taro.authorize({
              scope: 'scope.userLocation',
              success: () => {
                resolve(true);
              },
              fail: () => {
                Taro.kbModal({
                  content: '开门失败，请开启手机定位后重试',
                  cancelText: '取消',
                  confirmText: '开启',
                  onConfirm: () => {
                    Taro.openSetting()
                      .then(({ authSetting }) => {
                        const status = authSetting['scope.userLocation'];
                        Taro.kbToast({
                          text: `开启定位权限${status ? '成功' : '失败'}`,
                        });
                        resolve(status);
                      })
                      .catch(() => {});
                  },
                });
              },
            });
          }
        },
      });
    });
  };

  const onLocationChange = (res) => {
    // latitude	number	纬度，范围为 -90~90，负数表示南纬。使用 gcj02 国测局坐标系
    // longitude	number	经度，范围为 -180~180，负数表示西经。使用 gcj02 国测局坐标系
    // speed	number	速度，单位 m/s
    // accuracy	number	位置的精确度
    // altitude	number	高度，单位 m	1.2.0
    // verticalAccuracy	number	垂直精度，单位 m（Android 无法获取，返回 0）	1.2.0
    // horizontalAccuracy	number	水平精度，单位 m	1.2.0
    console.info('onLocationChange', res);
    setLocationData(res);
  };

  // 开始获取位置
  const onLocationStart = async () => {
    console.info('开始监听======>');
    Taro.startLocationUpdate({
      success: () => {
        Taro.onLocationChange(onLocationChange);
      },
      fail: async ({ errMsg }) => {
        console.info('startLocationUpdate===>', errMsg);
        const errmsg = errMsg.split(':')[1];
        // 微信未开启定位
        if (errmsg == 'fail system permission denied') {
          checkSystemAuth();
          return;
        }
        // 小程序未获取定位
        if (errmsg == 'fail auth deny') {
          const status = await checkLocationAuth();
          if (status) {
            onLocationStart();
          }
          return;
        }
        Taro.kbModal({
          title: '获取位置信息失败',
          content: '请确认手机打开定位功能并已授权微信使用定位权限',
        });
      },
    });
  };

  // 组件销毁或离开时，需关闭所有监听
  const onLocationClose = () => {
    Taro.offLocationChange();
    Taro.stopLocationUpdate();
  };

  return {
    onLocationStart,
    onLocationClose,
    locationData,
  };
}
