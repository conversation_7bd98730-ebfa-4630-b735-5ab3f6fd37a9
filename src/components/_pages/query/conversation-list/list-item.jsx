/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { Image, Text, View } from '@tarojs/components';
import KbAvatar from '@/components/_pages/user/avatar';
import classNames from 'classnames';
import { check } from '@base/utils/rules';
import { setClipboardData } from '@/utils/qy';
import './list-item.scss';

const KbConversationListItem = (props) => {
  const { data = {} } = props;

  const picHost = 'https://upload.kuaidihelp.com';

  const userTypeMap = {
    1: '管理员',
    2: '业务员',
    3: '驿站',
  };

  const operateButtonMap = {
    0: '',
    1: '标记已处理',
    2: '打回工单',
    3: '关闭工单',
    4: '重启工单',
    closedBySystem: '系统自动关闭',
  };

  let {
    create_time,
    create_user_avatar_img,
    avatar_img,
    pic_list = [],
    comment,
    answer_user_name,
    answer_user_id,
    userType,
    id,
    privilege,
    annex,
    have_annex,
    operate,
    des,
  } = data || {};

  const onPreviewImg = (url) => {
    let urls = [url];
    Taro.previewImage({
      current: urls[0],
      urls,
    });
  };

  const getPicUrl = (url) => {
    url = decodeURIComponent(url);
    return check('url', url).code === 0 ? url : picHost + (/^\//.test(url) ? '' : '/') + url;
  };

  const onDownload = (url) => {
    setClipboardData(url, '下载链接已复制，请前往浏览器中下载');
  };

  const isSelf = answer_user_id == 0 || !userType;

  const isService = privilege == '1';

  const hasFile = have_annex == '1' && annex.length > 0;

  const isClosedBySystem = operate == '3' && answer_user_id == '-1'; // 是否被系统关闭

  if (isClosedBySystem) {
    operate = 'closedBySystem';
  }

  const wrapperCls = classNames('kb-conversation-list__item--wrapper kb-margin-md-lr', {
    'kb-conversation-list__item--wrapper--reverse': isSelf,
  });

  const textCls = classNames(
    'kb-conversation-list__item--content--text kb-margin-md-lr kb-spacing-md',
    {
      'kb-conversation-list__item--content--text-left': !isSelf,
      'kb-conversation-list__item--content--text-right': isSelf,
    },
  );

  return (
    <View key={id} className='kb-conversation-list__item'>
      <View>
        <View className='kb-size__sm kb-text__center kb-margin-md-tb kb-color__grey'>
          {create_time}
        </View>
      </View>
      {!isSelf && (
        <View className='kb-margin-md kb-size__sm kb-color__grey kb-conversation-list__item--role'>
          <Text className='kb-color__brand kb-size__md kb-size__bold kb-margin-sm-r'>
            {operateButtonMap[operate]}
          </Text>
          {answer_user_name} {isService ? '客服' : userTypeMap[userType]}
        </View>
      )}
      <View className={wrapperCls}>
        <View>
          <KbAvatar size='small' circle src={create_user_avatar_img || avatar_img} />
        </View>
        <View className='kb-conversation-list__item--content'>
          <View className={textCls}>
            <View>{comment || des}</View>
            {pic_list &&
              pic_list.length > 0 &&
              pic_list.map((img) => {
                let url = getPicUrl(img);
                return (
                  <View
                    className='kb-conversation-list__item--content-img kb-margin-md-b'
                    key={`${img}`}
                  >
                    <Image
                      mode='widthFix'
                      src={url}
                      style={{ width: '100%' }}
                      onClick={onPreviewImg.bind(null, url)}
                    />
                  </View>
                );
              })}
            {hasFile &&
              annex.map((v) => {
                const { id, name, annex_url } = v;
                return (
                  <View key={id}>
                    <View style={{ display: 'flex', alignItems: 'center' }}>
                      {name}
                      {annex_url && (
                        <Text
                          className='kb-color__brand'
                          onClick={onDownload.bind(null, annex_url)}
                        >
                          下载
                        </Text>
                      )}
                    </View>
                  </View>
                );
              })}
          </View>
        </View>
      </View>
    </View>
  );
};

KbConversationListItem.options = {
  addGlobalClass: true,
};

export default KbConversationListItem;
