/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, Text, View } from '@tarojs/components';
import { useEffect, useRef, useState } from '@tarojs/taro';
import { useUpdate } from '@base/hooks/page';
import KbLoader from '@base/components/loader';
import KbConversationListItem from './list-item';
import { getAnswerList } from '../_utils/query.feedback';
import './index.scss';

/**
 * 回复对话列表
 * @params insertData 需要插入的数据
 *  */
const KbConversationList = (props) => {
  const { reqParams = {}, insertData = [], active } = props;

  const [list, setList] = useState(null);
  const [scrollIntoView, setScrollIntoView] = useState('');
  const [loading, setLoading] = useState(false);
  const [ready, setReady] = useState(false);
  const timerRef = useRef();
  const listRef = useRef([]);

  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchAnswerList = async (data = {}) => {
    setLoading(true);
    const { list: answerList, page, pageSize, total } = await getAnswerList(data);
    setLoading(false);
    setList((prevList) => {
      let arr = [];
      arr = [...(answerList || [])];
      // 最后一页才添加需要插入的信息
      if (page * pageSize >= total && insertData.length > 0) {
        arr.unshift(...insertData);
      }
      // 防止更新导致列表不断累加
      if (page == 1) {
        listRef.current = arr;
        return arr;
      }
      listRef.current = [...arr, ...prevList];
      return [...arr, ...prevList];
    });

    if (page == 1) {
      const { id } = answerList[answerList.length - 1] || {};
      setTimeout(() => {
        setScrollIntoView(`c_${id}`);
      }, 500);
    }

    setPagination({
      page,
      pageSize,
      total,
    });
  };

  /**
   * 拉取所有聊天记录，实时更新数据
   *  */
  const pullAnswerList = async (params = {}) => {
    const { list: answerList } = await getAnswerList({ ...params, page: 1 });
    const listInRef = listRef.current;
    const { id: answerListLastId } = answerList[answerList.length - 1] || {};
    const { id: answerListRefLastId } = listInRef[listInRef.length - 1] || {};
    const searchIds = listInRef.map((val) => val.id);
    // 最新的消息列表
    const newList = answerList.filter((val) => !searchIds.includes(val.id));
    if (answerListLastId != answerListRefLastId) {
      setList((prevList) => {
        listRef.current = [...prevList, ...newList];
        // 新消息滚动到底部
        setScrollIntoView(`c_${answerListLastId}`);
        return [...prevList, ...newList];
      });
    }
  };

  useUpdate(
    (loginData) => {
      const { logined } = loginData || {};
      if (!logined) return;
      if (active) {
        fetchAnswerList(reqParams);
        setReady(true);
      }
    },
    [reqParams, active],
  );

  useEffect(() => {
    if (ready) {
      timerRef.current = setInterval(() => {
        pullAnswerList({ ...pagination, ...reqParams });
      }, 3000);
    }
    return () => {
      clearInterval(timerRef.current);
    };
  }, [ready]);

  return (
    <ScrollView
      className='kb-scrollview'
      scrollY
      scrollIntoView={scrollIntoView}
      scrollWithAnimation
    >
      <View className='kb-spacing-md-b' id='kb-conversation-wrapper'>
        {pagination.total > pagination.page * pagination.pageSize && (
          <View className='kb-text__center kb-margin-md-tb'>
            {loading ? (
              <KbLoader size='small' />
            ) : (
              <Text
                className='kb-color__brand'
                onClick={fetchAnswerList.bind(null, { page: pagination.page + 1, ...reqParams })}
              >
                查看更多
              </Text>
            )}
          </View>
        )}
        {list &&
          list.map((item) => {
            return (
              <View id={`c_${item.id}`} key={item.id}>
                <KbConversationListItem data={item} />
              </View>
            );
          })}
      </View>
    </ScrollView>
  );
};

KbConversationList.options = {
  addGlobalClass: true,
};

KbConversationList.defaultProps = {
  reqParams: {},
  insertData: [],
};

export default KbConversationList;
