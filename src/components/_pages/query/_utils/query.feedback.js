/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import isArray from 'lodash/isArray';

/**
 * 回复工单消息
 *  */
export const answer = (params) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/waybill/logistic/answer',
      toastError: true,
      toastSuccess: false,
      toastLoading: false,
      data: params,
    }).then((res) => {
      if (res.code == 0) {
        resolve();
      }
    });
  });
};
/**
 * 工单详情
 *  */
export const getAnswerDetail = (problemId) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/waybill/logistic/answerDetail',
      toastError: true,
      toastSuccess: false,
      data: {
        problemId,
      },
    }).then((res) => {
      if (res.code == 0) {
        resolve(res.data);
      }
    });
  });
};
/**
 * 工单回复列表
 *  */
export const getAnswerList = ({ page = 1, pageSize = 10, problemId } = {}) => {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/waybill/logistic/answerList',
      toastError: true,
      toastLoading: false,
      toastSuccess: false,
      data: {
        page,
        size: pageSize,
        problemId,
      },
      onThen: (res) => {
        const { data } = res || {};
        const { answerList = [], total = 0, page = 1, pageSize = 10 } = data || {};
        resolve({
          list: isArray(answerList) ? answerList : null,
          total: total * 1,
          page: page * 1,
          pageSize: pageSize * 1,
        });
      },
    });
  });
};
