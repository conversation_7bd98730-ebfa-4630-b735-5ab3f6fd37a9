/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 *
 * @description 创建bars
 * @param serviceStatus 支付分状态
 * @returns
 */
export const createBars = () => {
  const bars = [
    {
      key: 'bind',
      value: '绑定手机号',
      url: 'user/relation',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'mobile',
      },
    },
    {
      key: 'help',
      value: '帮助中心',
      force: true,
      url: 'help',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'help',
      },
    },
  ];

  if (process.env.PLATFORM_ENV === 'weapp') {
    if (process.env.MODE_ENV !== 'third.post') {
      if (process.env.MODE_ENV !== 'third') {
        if (process.env.MODE_ENV !== 'third.pro') {
          bars.push({
            key: 'join',
            value: '申请加盟',
            force: true,
            id: 9,
            iconInfo: {
              prefixClass: 'kb-color__brand kb-icon',
              value: 'team',
            },
          });
        }
      }
    }
    bars.push({
      key: 'relation',
      value: '关联小程序',
      url: 'help/relation',
      force: true,
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'relation',
      },
    });
  } else {
    if (process.env.MODE_ENV !== 'third.pro') {
      bars.push({
        key: 'wallet',
        value: '我的钱包',
        url: 'user/wallet',
        iconInfo: {
          prefixClass: 'kb-color__brand kb-icon',
          value: 'wallet',
        },
      });
    }
  }
  // 定制普通版没有优惠券与联系客服
  if (process.env.MODE_ENV !== 'third') {
    if (
      process.env.MODE_ENV === 'yz' ||
      (process.env.PLATFORM_ENV === 'weapp' && process.env.MODE_ENV === 'third.pro') ||
      process.env.MODE_ENV === 'third.post'
    ) {
      bars.push({
        key: 'integral',
        value: '我的积分',
        url: 'order/integral',
        iconInfo: {
          prefixClass: 'kb-color__brand kb-icon',
          value: 'integral',
        },
      });
    }
    if (!(process.env.PLATFORM_ENV === 'alipay' && process.env.MODE_ENV === 'third.pro')) {
      bars.splice(1, 0, {
        key: 'card',
        value: '寄件优惠券',
        url: 'order/card?action=normal',
        iconInfo: {
          prefixClass: 'kb-color__brand kb-icon',
          value: 'rights-card',
        },
      });
    }
  }
  if (process.env.PLATFORM_ENV === 'weapp' && process.env.MODE_ENV !== 'third.post') {
    bars.push({
      key: 'service',
      value: '联系客服',
      url: 'user/qrcode',
      force: true,
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'service-2',
      },
    });
  }
  // 驿站支付宝版本和微信版均支持停发区查询
  if (process.env.MODE_ENV == 'yz') {
    bars.push({
      key: 'stopArea',
      value: '停发区查询',
      url: 'closedArea',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'tfqy',
      },
    });
  }

  // 微信端驿站所有版本增加好印莱引导
  if (process.env.PLATFORM_ENV === 'weapp') {
    if (
      process.env.MODE_ENV === 'yz' ||
      process.env.MODE_ENV === 'third' ||
      process.env.MODE_ENV === 'third.pro' ||
      process.env.MODE_ENV === 'third.post'
    ) {
      bars.push({
        key: 'service',
        value: '我要打印',
        url: 'hyl',
        force: true,
        iconInfo: {
          prefixClass: 'kb-color__brand kb-icon',
          value: 'printer',
        },
      });
    }
  }

  bars.push({
    key: 'realName',
    value: '实名认证',
    url: 'realnameList',
    iconInfo: {
      prefixClass: 'kb-color__brand kb-icon',
      value: 'realname1',
    },
  });

  bars.push({
    key: 'addrCode',
    value: '地址码',
    url: 'address/addrCode',
    iconInfo: {
      prefixClass: 'kb-color__brand kb-icon',
      value: 'address_code',
    },
  });

  return bars;
};

/**
 * @description 我的订单
 * @returns
 */
export const createOrderBars = () => {
  return [
    {
      key: 'o-send',
      value: '我寄出的',
      url: 'order?type=send',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'o-send',
      },
    },
    {
      key: 'o-receive',
      value: '我收到的',
      url: 'order?type=receive',
      iconInfo: {
        prefixClass: 'kb-color__brand kb-icon',
        value: 'o-receive',
      },
    },
  ];
};
