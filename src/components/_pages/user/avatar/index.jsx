/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import avatar from '@/assets/images/avatar.png';
import { check } from '@base/utils/rules';
import { AtAvatar } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { src, size } = props;
  return !src ? (
    <AtAvatar
      size={size}
      image={
        process.env.MODE_ENV === 'wkd'
          ? 'https://cdn-img.kuaidihelp.com/wkd/miniApp/login_wkd.png'
          : avatar
      }
      circle
    />
  ) : (
    <AtAvatar
      size={size}
      image={check('url', src).code === 0 ? src : `https://upload.kuaidihelp.com/avatar/${src}`}
      circle
    />
  );
};

Index.defaultProps = {
  size: 'normal',
  src: '',
};

export default Index;
