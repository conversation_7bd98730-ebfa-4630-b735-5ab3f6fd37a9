/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import isObject from 'lodash/isObject';

export const servicesKeyMap = {
  isDelivery: 'collection',
  isDesPay: 'arrive_pay',
  isDecVal: 'keep_account',
  isProPrice: 'pro_price',
};
export const serviceMap = {
  cost_value: {
    key: 'cost_value',
    title: '保价费用',
  },
  keep_account: {
    key: 'keep_account',
    title: '声明价值',
    confirmText: '声明价值',
    inputConfig: {
      label: '声明物品价值',
    },
    switch: {
      label: '保价费率',
      key: 'rate_checked',
    },
    required: true,
  },
  pro_price: {
    key: 'pro_price',
    title: '保价服务',
    confirmText: '保价费',
    inputConfig: {
      label: '声明物品价值',
    },
    switch: {
      label: '保价费率',
      key: 'rate_checked',
    },
    required: false,
    customKey: 'keep_account',
  },
  collection: {
    key: 'collection',
    title: '代收货款',
    confirmText: '代收金额',
    inputConfig: {
      label: '代收货款',
    },
    required: false,
  },
  arrive_pay: {
    key: 'arrive_pay',
    title: '到付运费',
    confirmText: '到付金额',
    inputConfig: {
      label: '到付运费',
    },
    switch: {
      cInput: true,
      label: '是否到付金额',
      key: 'is_arrive_pay',
    },
    required: false,
  },
};
export const getServiceList = (keys = []) => {
  return Object.keys(serviceMap)
    .filter((key) => keys.includes(key))
    .map((i) => serviceMap[i]);
};
export const getServiceListActivity = (data) => {
  const otherConfig = {
    is_collection: {
      title: '代收金额',
    },
    is_arrive_pay: {
      title: '到收货款',
    },
  };
  const activityService = {
    ...serviceMap,
    ...otherConfig,
  };
  const valid = Object.keys(data).filter((key) => {
    return (
      (otherConfig[key] ? !data[key.replace('is_', '')] && !!data[key] : data[key]) &&
      data[key] !== 0 &&
      activityService[key]
    );
  });

  return valid.map((key) => {
    return {
      ...activityService[key],
      value: otherConfig[key] ? '' : data[key],
    };
  });
};

export const serviceTips = (brand, opt = {}) => {
  const { pageSource } = opt || {};
  if (pageSource === 'dh') {
    return {
      brandProPrice: [
        '1、保价金额2000以内免保价费，超过2000的按0.2%费率计保价费，上限3万。',
        '2、物品价值超3万，按3万保价',
        // '3、若未保价，货运赔付不超过2000元，快递赔付不超过500元。',
      ],
    };
  }
  return {
    collection: [
      '1.我方页面只做下单参考，具体业务请咨询快递员/网点',
      '2.我方与快递员/网点没有从属关系，不对其服务做任何担保和背书',
      '3.请仔细与快递员/网点明确服务内容，并当面与快递员/网点签订增值服务协议且索要服务依据',
    ],
    arrive_pay: [
      '1.我方页面只做下单参考，具体业务请咨询快递员/网点',
      '2.我方与快递员/网点没有从属关系，不对其服务做任何担保和背书',
      '3.请仔细与快递员/网点明确服务内容，并当面与快递员/网点签订增值服务协议且索要服务依据',
    ],
    keep_account: [
      '1.请按物品实际价值来声明物品价值，我方页面只做下单参考，具体业务请咨询快递员/网点',
      '2.物品是否属于可保价范围请咨询上门快递员，我方与快递员/网点没有从属关系，不对其服务做任何担保和背书',
      '3.请仔细与快递员/网点明确服务内容，并当面与快递员/网点签订增值服务协议且索要服务依据',
    ],
    pro_price: [
      '1.请按物品实际价值来声明物品价值，我方页面只做下单参考，具体业务请咨询快递员/网点',
      '2.物品是否属于可保价范围请咨询上门快递员，我方与快递员/网点没有从属关系，不对其服务做任何担保和背书',
      '3.请仔细与快递员/网点明确服务内容，并当面与快递员/网点签订增值服务协议且索要服务依据',
    ],
    brandProPrice:
      brand == 'cngg'
        ? [
            {
              label:
                '货物破损：未按照约定为下单用户的寄递物品提供存储、保管的服务，从而导致用户利益受损，包括但不限于因包裹破损导致货品破损、物流包装破损',
              list: [
                '（1）、如能提供有效货值证明的，按有效货值的20%赔偿，最高不超过300元；无法提供有效货值证明的，按10倍运费赔付，最高不超过300元；',
                '（2）、如货品外包装破损且货品本身破损，同时影响商品主要功能的，按照货品丢失条款进行赔偿。',
              ],
            },
            {
              label:
                '货物丢失：未按服务承诺保管包裹，使包裹出现丢失的情况，包括但不限于因包裹破损导致货品少件、包裹整体丢失。',
              list: [
                '（1）、如能提供有效货值证明的，按有效货值赔付，最高不超过600元；无法提供有效货值证明的，按10倍运费赔付，最高不超过600元；',
                '（2）、货品少件的物品损失，菜鸟或菜鸟指定的物流服务商按最多10倍物流服务费赔偿，最高不超过600元。',
              ],
            },
          ]
        : [
            {
              label: '保价费用计算规则:',
              list: [
                brand == 'dp'
                  ? '物品价值300元以内收费1元，300元以上按照4‰计费，向上取整，声明价值超过20000元不予保价！'
                  : '物品价值在0-500元的，保价费用为1元，501-1000元，保价费用为2元，大于1000元时保价费用为物品价值的5‰，保价费向上取整精确到元，最低1元。声明价值超过20000不予保价!',
              ],
            },
            {
              label: '以下物品不提供保价服务:',
              list: [
                '1)价值不易核实的物品，如古玩字画、纪念币、翡翠原石、观赏石等;',
                '2)易损或不易包装的物品，如玉雕、工艺品等',
                '3)其他运输风险较大的物品，以快递员与您核实确认的信息为准。',
              ],
            },
            {
              label: '理赔规则:',
              list: [
                '1)已保价的货物，由快递公司原因造成货物丢失或全部损毁、灭失(包装材料破损除外)的，按照保价的声明价值进行赔付;内件短少或部分损毁的，按照货物的声明价值和损失的比例赔付。',
                '2)未保价的货物，由于快递公司原因造成货物丢失或破损(包装材料破损除外)的，按照货物实际损失赔偿，但每单最高额不超过该单货物运费的5倍。',
                '3)因下列原因造成的托寄物毁损灭失、延误派送、无法派送的，快递公司不承担赔偿责任:',
                '3.1因不可抗力因素造成的。不可抗力是指无法预测、无法控制或无法避免的客观因素或意外事件，包括但不限于地震、火灾、雪灾、暴风雨、大雾等恶劣天气;罢工、恐怖事件、意外交通事故、法规政策的修改、政府、司法机关的行为、决定或命令;抢劫、飞车抢劫等暴力犯罪。',
                '3.2因托寄物的自然性质、内在缺陷或合理损耗造成的。',
                '3.3非快递公司过错造成的，包括但不限于您没有如实申报托寄物资料，您交付的托寄物属于法律法规明文定及本合同约定禁止运输或限制运输的，收货人迟延受领托寄物等情形。',
                '4)如果您就托寄物购买了保险，因发生保险事故导致托寄物毁损灭失的，保险公司已经向您承担或许诺承担保险理赔责任后，在此范围内不再承担赔偿责任。',
              ],
            },
          ],
  };
};

export const remarkMaps = [
  { label: '带文件夹', key: '1' },
  { label: '带防水袋', key: '2' },
  { label: '带纸箱', key: '3' },
  { label: '要爬楼', key: '4' },
  { label: '上门请联系', key: '5' },
];
export const sendOptions = [
  { title: '去驿站寄', key: 'ship' },
  { title: '上门取件', key: 'take' },
];
// 驿站增值服务配置
export const servicesConfig = {
  courierId: null,
  cost: null, // 保价价费率
  proPriceStart: null, // 保价起点
  proPriceEnd: null, // 保价上限
  isDelivery: null, // 是否开启代收货款0.关闭，1.开启
  isDesPay: null, // 是否开启到付，0.关闭1.开启
  isDecVal: null, // 是否声明价值：0.关闭，1.开启
  isProPrice: null, // 是否开启保价：0.关闭,1.开启
  isToDoor: null, // 是否支持上门取件0.关闭,1.开启
  allowReserveTime: '', //是否支持上门取件预约时间
  isFresh: null, // 是否支持生鲜：0.关闭,1.开启,
  isDeclared: null, //是否开启自定义的声明
  reserve_start_time: '', //营业开始时间
  reserve_end_time: '', //营业结束时间
};
// 拉取增值服务配置项
export const getServicesConfig = (relationInfo) => {
  const {
    dakId: dak_id,
    relation_id,
    agent_guid,
    type: relationType,
    courier_id,
    brand,
    platform,
    courier = {},
  } = relationInfo || {};
  if (process.env.MODE_ENV !== 'wkd') {
    if (!dak_id && !relation_id && !agent_guid) return Promise.reject();
  }
  return new Promise((resolve, reject) => {
    request({
      url: () => {
        let url = dak_id
          ? '/api/weixin/mini/minpost/order/getDakValueaddConfig'
          : '/api/weixin/mini/minpost/relation/checkRelation';
        if (process.env.MODE_ENV == 'wkd') {
          url =
            relationType == 'tcjs' ? '/g_wkd/v1/rush/Rush/getAddValue' : '/v1/GrabVas/getGrabVas';
        }
        return url;
      },
      toastLoading: false,
      nonceKey: process.env.MODE_ENV === 'yz' ? (dak_id ? 'dak_id' : 'relation_id') : '',
      formatRequest: (req) => {
        if (process.env.MODE_ENV == 'wkd') {
          let reqData = {};
          switch (relationType) {
            case 'courier':
              reqData = {
                courier_id,
              };
              break;
            case 'brand':
              if (platform == 'yjkd_courier') {
                reqData = {
                  courier_id: courier.courier_id,
                };
              } else if (['sf', 'sfky'].includes(brand)) {
                reqData = {
                  brand: 'sf',
                };
              } else if (platform == 'yjkd_brand') {
                //优寄快递除优寄快递员外，均拉取默认京东增值服务
                reqData = {
                  brand: 'jd',
                };
                // 德邦配置单独拉取
                if (brand == 'dp') {
                  reqData.brand = brand;
                }
              }
              break;
            case 'tcjs':
              reqData = {
                brand,
              };
              break;
          }
          req = reqData;
        } else {
          req = {
            dak_id,
            relation_id,
            agent_guid,
          };
        }
        return req;
      },
      onThen: ({ data, code, ...rest }) => {
        const switchKeys = ['allowReserveTime'];
        data &&
          Object.keys(data).forEach((key) => {
            if (key.includes('is') || switchKeys.includes(key)) {
              data[key] == '1' ? (data[key] = true) : (data[key] = false);
            }
            if (key == 'proPriceEnd' && data.proPriceEnd <= 0) {
              data.proPriceEnd = 20000;
            }
          });
        if (data) {
          resolve({ data, relation_id: relation_id || dak_id, ...rest });
        } else {
          reject(data);
        }
      },
    });
  });
};

// 是否触发表单锁定为自定义内容
export const isFormLocked = (dynamicForms, update) => {
  let updateData = {};
  if (!dynamicForms) return;
  Object.keys(dynamicForms).forEach((i) => {
    if (dynamicForms[i].value) {
      updateData[i] = dynamicForms[i].value;
    }
  });
  if (Object.keys(updateData).length) {
    update({ ...updateData });
    return updateData;
  }
  return false;
};

export const calculate = (num, cost) => parseFloat(((num * 1 * (cost * 1)) / 100).toFixed(2));
// 获取自定义表单配置中的有效值
export const getValidData = (formData) => {
  const data = {};
  formData &&
    Object.keys(formData).forEach((key) => {
      const value = formData[key].value;
      if (value) {
        data[key] = value;
      }
    });
  return data;
};
const verifyTipMaps = {
  'collection.required': '代收货款为必填项',
  'arrive_pay.required': '到付金额为必填项',
};
const verifyKeys = ['required'];
export const getDynamicVerifyOpt = (data) => {
  let verify = {};
  // 取出为验证字段的内容，并填充验证未通过时的提示语
  data &&
    Object.keys(data).map((key) => {
      const item = data[key];
      verifyKeys.forEach((verifyKey) => {
        if (item.hasOwnProperty(verifyKey)) {
          !verify[key] && (verify[key] = {});
          verify[key][verifyKey] = item[verifyKey];
          verify[key][`${key}.${verifyKey}`] = verifyTipMaps[`${key}.${verifyKey}`];
        }
      });
    });
  return verify;
};
export const verifyDynamicForm = (data, verifyOpt = {}) => {
  let res = { code: 0, msg: '' };
  verifyOpt &&
    Object.keys(verifyOpt).forEach((key) => {
      if (verifyOpt[key].required && !data[key]) {
        res.code = '1001';
        res.msg = verifyOpt[key][`${key}.required`];
      }
    });

  return res;
};
// 组装form值的展示
const fixFromValueView = (key, value) => {
  let node = {
    type: 'text',
    text: '',
    attrs: {
      style: 'margin-right:3px',
    },
  };
  switch (key) {
    case 'goods_weight':
      node.text = value + 'kg ';
      break;
    case 'cost_value':
      node.text = `保价费${value}元 `;
      break;
    case 'keep_account':
      node.text = `声明价值${value}元 `;
      break;
    case 'collection':
      node.text = `代收货款${value}元 `;
      break;
    case 'arrive_pay':
      node.text = `到付金额${value}元 `;
      break;
    case 'is_arrive_pay':
      node.text = '到付金额';
      break;
    case 'package_images':
      value[0] &&
        (node = {
          type: 'node',
          name: 'img',
          attrs: {
            style: 'width:20px;height:15px;margin-left:3px;',
            src: value[0],
          },
        });
      break;
    default:
      node.text = value + ' ';
      break;
  }

  return node;
};
// 获取值表单值展示
export const getFormValueView = (data) => {
  const showKeys = [
    'goods_weight',
    'goods_name',
    'cost_value',
    'keep_account',
    'collection',
    'arrive_pay',
    'goods_remark',
    'is_arrive_pay',
    'package_images',
  ];
  if (isObject(data)) {
    let keys = Object.keys(data);
    const exclusion = {
      is_arrive_pay: 'arrive_pay',
    };
    const customVerify = (key) => {
      const verify = { package_images: (data) => data && data.length };
      return verify[key] ? verify[key](data[key]) : true;
    };
    const packageImageIndex = keys.findIndex((key) => key === 'package_images');
    if (packageImageIndex) {
      const [packageImages] = keys.splice(packageImageIndex, 1);
      packageImages && keys.unshift(packageImages);
    }

    const children = keys
      .filter((key) => {
        if (!(data[exclusion[key]] && data[key]) && customVerify(key)) {
          return key;
        }
      })
      .filter((key) => showKeys.includes(key) && !!data[key])
      .map((key) => fixFromValueView(key, data[key]));
    return children.length
      ? [
          {
            name: 'div',
            attrs: {
              style: `display: flex;width:100%;
              ${children.length <= 2 ? 'justify-content: center;' : ''}
          align-items: center;overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;`,
            },
            children,
          },
        ]
      : [];
  }
  return [];
};

export const memoObject = (data = {}, change = {}) => {
  let modify = null;
  Object.keys(data).forEach((key) => {
    if (data[key] !== change[key] && key in change) {
      modify = {
        ...modify,
        [key]: change[key],
      };
    }
  });
  return modify;
};

export const findProductTypeEn = (brand, type) => {
  const extraInfo_brandInfo = Taro.kbGetGlobalData('extraInfo_brandInfo') || [];
  const product_types = extraInfo_brandInfo.find((item) => item.key == brand) || {};
  if (product_types.product_type && product_types.product_type.length > 0) {
    const item = product_types.product_type.find((item) => item.label == type) || {};
    return item.key;
  } else {
    return '';
  }
};

export const findProductTypeName = (list = [], type) => {
  const item = list && list.length > 0 ? list.find((item) => item.key == type) : {};
  return (item && item.label) || '';
};
