/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { dateCalendar, extractData } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import isNumber from 'lodash/isNumber';
import numeral from 'numeral';

// 格式化数字
export const formatNumeral = (val, unit = '元', defaultValue) => {
  if ((!val || val == 0) && defaultValue) {
    val = defaultValue;
  } else {
    val = numeral(val || 0).format('0.00');
  }
  return `${val}${unit}`;
};

/**
 * 时间格式化
 *  */
export const formatSectionTimer = (start, end) => {
  if (!start || !end) return [];
  const formatStart = dateCalendar(start, { timer: true });
  const formatEnd = dateCalendar(end, { timer: true });
  let data = '',
    startTime = '',
    endTime;
  data = formatStart.split(' ')[0];
  startTime = formatStart.split(' ')[1].split(':')[0];
  endTime = formatEnd.split(' ')[1].split(':')[0];

  return `${data} ${startTime}:00 ~ ${endTime}:00`;
};

/**
 *
 * @description 兼容订单详情
 * @param {*} item
 * @returns
 */
export const formatOrderDetail = (item) => {
  const transFormKey = [
    'order_number',
    'user_id',
    'brand',
    'waybill',
    'delivery_type',
    'order_status',
    'pay_status',
    'logistic_status',
    'logistic_status_txt',
    'package_weight',
    'package_info',
    'package_note',
    'charging_weight',
    'place_volume',
    'settlement_volume',
    'f_fee',
    'f_kg',
    's_fee',
    'regiment_estimate_price',
    'regiment_wait_pay_price',
    'other_money',
    'pay_price',
    'wait_pay_price',
    'claiming_value',
    'warrant_price',
    'real_claiming_value',
    'real_warrant_price',
    'calculate_price_type',
    'collect_courier_mobile',
    'collect_courier_name',
    'reserve_start_time',
    'reserve_end_time',
    'create_at',
    'regiment_remark',
    'freight',
    'can_push_pay',
    'regiment_pay_price',
    'change_shipper_address_order',
    'order_package_pics',
    'regiment_profit',
    'arrive_pay',
    'settlement_price_details',
    'third_create_at',
    'pay_method',
    ['send_name', 'shipper_name'],
    ['send_tel', 'shipper_tel'],
    ['send_mobile', 'shipper_mobile'],
    ['send_province', 'shipper_province'],
    ['send_city', 'shipper_city'],
    ['send_county', 'shipper_county'],
    ['send_address', 'shipper_address'],
    ['receive_name', 'shipping_name'],
    ['receive_tel', 'shipping_tel'],
    ['receive_mobile', 'shipping_mobile'],
    ['receive_province', 'shipping_province'],
    ['receive_city', 'shipping_city'],
    ['receive_county', 'shipping_county'],
    ['receive_address', 'shipping_address'],
  ];

  return extractData(item, transFormKey);
};

/**
 *
 * @description 支付状态
 */
export const formatStatus = (status) => {
  const statusMap = {
    0: {
      text: '待结算',
      color: 'yellow',
      key: 0,
    },
    1: {
      text: '待补款',
      color: 'red',
      key: 1,
    },
    2: {
      text: '已结清',
      color: 'green',
      key: 2,
    },
  };

  return statusMap[status];
};

/**
 * 格式化订单详情数据
 * @returns
 */
export const formatResponseOrderDetail = (data = {}, isAdmin) => {
  const payMethodMap = {
    0: '寄付月结',
    1: '到付',
    2: '寄付现结',
  };

  const formatData = formatOrderDetail(data);
  if (!data.order_number) {
    return { data: void 0 };
  }

  const { order_number, pay_status } = formatData;

  const statusInfos = {
    logistic_status_txt: formatData.logistic_status_txt,
    logistic_status: formatData.logistic_status,
    order_status: formatData.order_status,
    pickup: formatSectionTimer(formatData.reserve_start_time, formatData.reserve_end_time),
    create_at: dateCalendar(formatData.create_at, { timer: true }),
    remark: formatData.regiment_remark,
    collect_courier_name: formatData.collect_courier_name,
    collect_courier_mobile: formatData.collect_courier_mobile,
    user_id: isAdmin == 1 ? formatData.user_id : '',
  };

  const brandInfo = {
    delivery_type: formatData.delivery_type,
    brand: formatData.brand,
    waybill: formatData.waybill,
    order_number: formatData.order_number,
  };

  const addressInfo = {
    send_name: formatData.send_name,
    send_tel: formatData.send_tel,
    send_mobile: formatData.send_mobile,
    send_province: formatData.send_province,
    send_city: formatData.send_city,
    send_county: formatData.send_county,
    send_address: formatData.send_address,
    receive_name: formatData.receive_name,
    receive_tel: formatData.receive_tel,
    receive_mobile: formatData.receive_mobile,
    receive_province: formatData.receive_province,
    receive_city: formatData.receive_city,
    receive_county: formatData.receive_county,
    receive_address: formatData.receive_address,
    change_shipper_address_order: formatData.change_shipper_address_order,
  };

  const goodsInfo = {
    list: [
      {
        icon: 'wallet',
        text: payMethodMap[formatData.arrive_pay],
      },
      {
        icon: 'riyongpin',
        text: formatData.package_info,
      },
      {
        icon: 'weight',
        text: formatNumeral(formatData.package_weight, 'kg', '--'),
      },
      {
        icon: 'volume',
        text: formatNumeral(formatData.place_volume, 'cm³', '--'),
      },
      {
        icon: 'baojiafeiyong',
        text: formatNumeral(formatData.warrant_price, '元', '--'),
      },
    ],
    package_note: formatData.package_note,
    package_pics: formatData.order_package_pics || [],
    create_at: dateCalendar(formatData.create_at, { timer: true }),
    order_id: formatData.order_number,
    third_create_at: formatData.third_create_at,
  };

  const transformNum = (num) => {
    isNumber(num * 1000) ? num * 1000 : 0;
  };

  const floatAdd = (num, num2) => {
    return (transformNum(num) + transformNum(num2)) / 1000;
  };

  const getTotalFee = () => {
    if (pay_status == 2) {
      if (isAdmin) {
        return formatNumeral(formatData.regiment_pay_price, '元', '--');
      } else {
        return formatNumeral(formatData.pay_price, '元', '--');
      }
    } else {
      if (isAdmin) {
        return floatAdd(formatData.regiment_estimate_price, formatData.regiment_wait_pay_price);
      } else {
        return formatData.wait_pay_price;
      }
    }
  };

  const formatFeeDetail = (feeDetail = []) => {
    const hasPackageFee = feeDetail.some((i) => i.name.includes('包装'));
    if (hasPackageFee && feeDetail.length > 0) {
      feeDetail[0].showPackageFee = true;
    }
    return feeDetail;
  };

  const payInfo = {
    order_status: formatData.order_status,
    pay_status,
    logistic_status: formatData.logistic_status,
    can_push_pay: formatData.can_push_pay,
    pay_status_info: formatStatus(pay_status),
    charging_weight: formatNumeral(formatData.charging_weight, 'kg', '--'),
    settlement_volume: formatNumeral(formatData.settlement_volume, 'cm³', '--'),
    real_claiming_value: formatNumeral(formatData.real_claiming_value, '', '--'),
    real_warrant_price: formatNumeral(formatData.real_warrant_price, '', '--'),
    other_money: formatNumeral(formatData.other_money, '', '--'), // 包装费
    pay_price: formatNumeral(formatData.pay_price, '', '--'), // 已支付
    wait_pay_price: formatData.wait_pay_price, // 待支付
    freight: formatNumeral(formatData.freight, '', '--'),
    totalFee: getTotalFee(),
    f_fee: formatData.f_fee,
    f_kg: formatData.f_kg,
    s_fee: formatData.s_fee,
    regiment_profit: formatData.regiment_profit,
    calculate_price_type: formatData.calculate_price_type,
    settlement_price_details: isArray(formatData.settlement_price_details)
      ? formatFeeDetail(formatData.settlement_price_details)
      : [],
    pay_method: formatData.pay_method,
  };

  return {
    data: {
      statusInfos,
      order_number,
      brandInfo,
      addressInfo,
      goodsInfo,
      payInfo,
    },
  };
};

/**
 * 判断地址信息是否加密
 *  */
export const isEncryptionData = (data = {}) => {
  const str = Object.values(data).join('');
  return str.includes('*');
};
/**
 * 拼接用户信息
 *  */
export const combineData = (data = {}) => {
  return Object.values(data).join('');
};
