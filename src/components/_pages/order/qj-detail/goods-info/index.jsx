/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { data = {} } = props;
  const { list = [], package_note, create_at, third_create_at } = data;

  return (
    <View className='kb-goods kb-margin-md kb-spacing-md kb-background__white'>
      <View className='kb-goods__title kb-size__lg kb-spacing-md-tb at-row at-row__align--center at-row__justify--between'>
        <Text>包裹信息</Text>
      </View>
      <View className='at-row at-row__align--center at-row__justify--between'>
        {list.map((val) => {
          return (
            <View className='kb-text__center kb-goods__item kb-spacing-md-tb' key={val.icon}>
              <View
                className={classNames([
                  'kb-spacing-md-b',
                  `kb-color__${val.text ? 'brand-dark' : 'grey'}`,
                ])}
              >
                <AtIcon prefixClass='kb-icon' value={val.icon} className='kb-icon-size__lg' />
              </View>
              <View className='kb-size__base'>{val.text}</View>
            </View>
          );
        })}
      </View>
      {package_note && (
        <View className='kb-size__base kb-spacing-md-b'>
          <Text className='kb-color__grey'>包裹备注：</Text>
          <Text>{package_note}</Text>
        </View>
      )}
      <View className='kb-size__base kb-spacing-md-b'>
        <Text className='kb-color__grey'>下单时间：</Text>
        <Text>{create_at}</Text>
      </View>
      {third_create_at && (
        <View className='kb-size__base kb-spacing-md-b'>
          <Text className='kb-color__grey'>推送快递公司时间：</Text>
          <Text>{third_create_at}</Text>
        </View>
      )}
    </View>
  );
};

Index.defaultProps = {
  data: {},
};
Index.options = { addGlobalClass: true };

export default Index;
