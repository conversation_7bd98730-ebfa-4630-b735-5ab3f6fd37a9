/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { data = {}, statusInfos } = props;
  const { pay_status_info = {}, pay_method, settlement_price_details = [], f_kg } = data;
  const { order_status } = statusInfos || {};

  const handleNavigator = (key) => {
    Taro.navigateToDocument(key);
  };
  const toPackingFee = () => {
    Taro.navigator({
      url: 'https://m.kuaidihelp.com/f/packingFee',
      target: 'webview',
    });
  };

  // 支付分，月结
  const onlinePay = ['1', '2'].includes(`${pay_method}`);

  return (
    <View className='kb-payInfo kb-margin-md-lr kb-spacing-md kb-background-color__white'>
      <View className='at-row at-row__justify--between at-row__align--center kb-payInfo__title kb-border-b'>
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View className='kb-size__lg'>费用信息</View>
          {onlinePay && order_status != 0 && (
            <View className='kb-payInfo__pay-status'>
              <View
                className={classNames([
                  'kb-payInfo__checkbox',
                  `kb-color__${pay_status_info.color}`,
                  `kb-background-color__${pay_status_info.color}`,
                ])}
              >
                <AtIcon
                  prefixClass='kb-icon'
                  value='right'
                  className='kb-icon-size__sm kb-color__white'
                />
              </View>
              <Text className={classNames(['kb-size__lg kb-spacing-sm-lr'])}>
                {pay_status_info.text}
              </Text>
            </View>
          )}
        </View>
      </View>
      {pay_status_info.key != 0 && (
        <Fragment>
          <View className='at-row at-row__align--center kb-payInfo__commonlist'>
            <Text>结算重量：{data.charging_weight}</Text>
          </View>
          <View className='at-row at-row__align--center kb-payInfo__commonlist'>
            <Text>结算体积：{data.settlement_volume}</Text>
          </View>
          <View className='at-row at-row__justify--between at-row__align--center kb-payInfo__title kb-border-b'>
            <View className='at-row at-row__align--center at-row__justify--between'>
              <View className='at-row at-row__align--center'>
                <Text className='kb-margin-md-r kb-size__lg'>
                  计费方式：按{data.calculate_price_type == 2 ? '体积' : '重量'}计费
                </Text>
                <AtIcon
                  onClick={() => handleNavigator('price_rule')}
                  prefixClass='kb-icon'
                  value='bail'
                  className='kb-icon-size__base  kb-spacing-xs-b'
                />
              </View>
            </View>
          </View>
        </Fragment>
      )}
      {pay_status_info.key != 0 && (
        <Fragment>
          <View className='kb-payInfo__commonlist'>
            <Text>运费：{data.freight}</Text>
            {onlinePay && (
              <Text>
                首重{data.f_fee}元/{f_kg > 1 ? f_kg : ''}KG，续重{data.s_fee}元/KG
              </Text>
            )}
          </View>
          {settlement_price_details.map((val) => {
            return (
              <View className='kb-payInfo__commonlist' key={val.id}>
                <Text>
                  {val.name}：{val.fee}
                </Text>
                {val.showPackageFee && <Text onClick={toPackingFee}>{'收费标准 >'}</Text>}
              </View>
            );
          })}
          <View className='kb-payInfo__commonlist'>
            <View>
              <Text className={order_status == 0 ? 'kb-payInfo__readyPrice' : ''}>
                已支付：{data.pay_price}
              </Text>
              {order_status == 0 && <Text className='kb-payInfo__priceSource'>资金已原路退回</Text>}
            </View>
          </View>
          <View className='kb-payInfo__commonlist'>
            <View className='at-row at-row__align--center'>
              <View className='at-row at-row__align--baseline kb-color__black'>
                <Text>总费用：¥</Text>
                <Text className='kb-payInfo__totalPrice kb-margin-sm-l'>{data.wait_pay_price}</Text>
              </View>
            </View>
          </View>
        </Fragment>
      )}
    </View>
  );
};

Index.defaultProps = {
  data: {},
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
