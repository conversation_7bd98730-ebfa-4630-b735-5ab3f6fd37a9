/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$border-radius-xxl: 26px;
$p-v-md: 36px;

.kb-payInfo {
  border-radius: $border-radius-xxl;

  &__money {
    white-space: nowrap;
  }

  &__title {
    padding-top: 37px;
    padding-bottom: 37px;
  }

  &__commonlist {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 104px;
    color: $color-grey-1;
    font-size: $font-size-lg;
  }

  &__checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 20px;
  }

  &__pay-status {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__totalPrice {
    font-size: 64px;
  }

  &__profit {
    width: 34px;
    height: 47px;

    &__price {
      color: #3cdb7c;
    }
  }

  &__readyPrice {
    color: $color-grey-2;
    text-decoration: line-through;
  }

  &__priceSource {
    margin-left: $spacing-v-md;
    color: #ffb5b5;
  }
}

.kb-background-color {
  &__red {
    background-color: $color-red-1;
  }

  &__yellow {
    background-color: #ffc34a;
  }

  &__green {
    background-color: #3cdb7c;
  }

  &__white {
    background-color: $color_white;
  }
}

.kb-border-b {
  border-bottom: $border-lighter;
}

.kb-total {
  text-align: right;
  border-top: $border-lightest;
}

.kb-direction {
  &__down {
    transform: rotate(0);
  }

  &__up {
    transform: rotate(-180deg);
  }
}

.kb-margin-md {
  padding: $p-v-md;
  padding-top: 0;
  padding-bottom: 0;
}

.kb-spacing-md-tb {
  padding-top: $p-v-md;
  padding-bottom: $p-v-md;
}
