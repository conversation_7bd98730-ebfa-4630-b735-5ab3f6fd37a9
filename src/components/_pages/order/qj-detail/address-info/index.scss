/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$border-radius-xxl: 26px;
$font-size-17: 34px;
$p-v-xs: 16px;
$p-v-md: 36px;
$color-green-2: #e9f7ee;

.kb-detail {
  &__brand {
    border-bottom: $border-lighter;
    &--type {
      color: $color-white;
      background-color: $color-brand;
      border-top-left-radius: $border-radius-xl;
      border-bottom-right-radius: $border-radius-xl;
    }
  }

  &__address {
    padding: $spacing-v-xl $p-v-md $spacing-v-xl $p-v-md;
    border-radius: $border-radius-xxl;
    &--item {
      padding-bottom: $spacing-v-xl;
    }

    &--border &--item {
      &:first-child {
        position: relative;
        padding-bottom: $spacing-v-xl * 2;

        &::after {
          position: absolute;
          right: 0;
          bottom: $spacing-v-xl;
          left: 80px;
          border-bottom: $border-lightest;
          content: '';
        }
      }
    }
  }
}
.kb-background__green {
  background-color: $color-green-2;
}
.kb-address__item {
  &:last-child {
    padding-left: 0;
  }
}
.item-info__text-mobile {
  display: inline-block;
  min-width: 240px;
}

.kb-background__white {
  background-color: $color_white;
}
.kb-background__greener {
  background-color: #3cdb7c;
}
.kb-background__brand {
  background-color: #1480ff;
}

$address-placeholder-height: 150px;
.kb-address {
  box-sizing: border-box;
  background-color: $color-white;
  &__item {
    .item {
      &-bar {
        &__item {
          &--lib::before,
          &:first-child::after {
            height: $address-placeholder-height/2.5;
            content: '';
          }
          &--lib {
            display: flex;
            align-items: center;
            margin-right: $p-v-md * 0.5;
            margin-bottom: $p-v-md * 0.5;
            padding-right: $p-v-md * 0.5;
            padding-left: $p-v-md * 0.5;
            font-size: $font-size-base;
          }
        }
      }

      &-info {
        align-items: stretch;
        word-break: break-all;
        &__text {
          margin-right: $spacing-h-md;
          font-size: $font-size-17 !important;

          &:last-child {
            margin-right: 0;
          }
        }

        &__placeholder {
          display: flex;
          flex: 1;
          flex-direction: column;
          justify-content: center;
          min-height: $address-placeholder-height;
          &-item {
            line-height: 1.8;
          }
        }

        &__value {
          display: flex;
          flex-direction: column;
          justify-content: center;
          box-sizing: border-box;
          min-height: $address-placeholder-height;
          padding-right: $spacing-v-md;
          &--desc {
            margin-top: $spacing-h-sm;
            color: $color-grey-3;
            font-size: $font-size-base;
          }
        }
      }
    }

    &:first-child {
      padding: $p-v-xs 0 0 $p-v-md;
      border-radius: $border-radius-lg $border-radius-lg 0 0;
    }

    &:last-child {
      margin-bottom: 0;
      // padding: 0 0 0 $p-v-md;
      border-radius: 0;
      .item {
        &-container {
          border-bottom: 0;
        }
      }
    }

    &--send {
      .item {
      }
    }
  }
  &-type {
    display: inline-block;
    width: 48px;
    height: 48px;
    margin-right: $p-v-md / 2;
    overflow: hidden;
    color: $color-white;
    font-size: $font-size-base !important;
    line-height: 48px;
    text-align: center;
    border-radius: $border-radius-lg + 6;
  }
}
