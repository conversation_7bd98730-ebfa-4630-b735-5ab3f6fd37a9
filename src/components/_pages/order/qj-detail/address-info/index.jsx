/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { setClipboardData } from '@/utils/qy';
import { makePhoneCall, mergeBySpace } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Fragment, useEffect } from '@tarojs/taro';
import classNames from 'classnames';
import { AtAvatar, AtIcon } from 'taro-ui';
import { combineData, isEncryptionData } from '../utils';
import './index.scss';

const Index = (props) => {
  const { data, border, get, brands } = props;
  const addressList = [
    {
      key: 'send',
      tag: '寄',
      color: 'brand',
    },
    {
      key: 'receive',
      tag: '收',
      color: 'greener',
    },
  ];
  const handleCopy = (e, text, type) => {
    e.stopPropagation();
    setClipboardData(text, `${type}已复制`);
  };

  const fixDeliveryType = (type) => {
    switch (type) {
      case 'offer':
        return '标快';
      case 'express':
        return '特快';
      default:
        return '';
    }
  };

  const fitBrandPhone = (brand) => {
    const { name, tel } = brands[brand] || {};
    return {
      brandName: name,
      phone: tel,
    };
  };

  const getLogics = ({ waybill, brand, order_no, status_txt }) => {
    Taro.navigator({
      url: 'query/detail',
      options: {
        waybill,
        brand,
        order_no,
        status_txt,
      },
    });
  };

  useEffect(() => {
    get();
  }, []);

  return (
    <View
      className={classNames('kb-detail__address kb-spacing-md kb-margin-md kb-background__white', {
        'kb-detail__address--border': border,
      })}
    >
      <View className='kb-detail__brand kb-spacing-md-b'>
        <View className='at-row at-row__align--center'>
          <View className='item-icon kb-margin-md-r'>
            <AtAvatar
              image={
                data.brand
                  ? `https://cdn-img.kuaidihelp.com/brand_logo/icon_${data.brand}.png?v=20230314`
                  : ''
              }
              circle
              size='normal'
              className='kb-avatar__middle'
            />
          </View>
          <View className='item-content'>
            <View className='at-row at-row__align--center at-row__justify--between'>
              <View className='at-row at-row__align--center kb-size__lg'>
                <View>
                  <Text>{fitBrandPhone(data.brand).brandName}</Text>{' '}
                  <Text
                    hoverClass='kb-hover-opacity'
                    onClick={() => makePhoneCall(fitBrandPhone(data.brand).phone)}
                  >
                    {fitBrandPhone(data.brand).phone || ''}
                  </Text>
                </View>
                <View
                  className='kb-spacing-md-lr kb-margin-xs-b'
                  onClick={() => makePhoneCall(fitBrandPhone(data.brand).phone)}
                >
                  <AtIcon
                    hoverClass='kb-hover-opacity'
                    prefixClass='kb-icon'
                    value='phone'
                    className='kb-icon-size__base kb-color__brand'
                  />
                </View>
                {fixDeliveryType(data.delivery_type) && (
                  <View className='kb-detail__brand--type kb-spacing-sm-lr kb-size__sm'>
                    {fixDeliveryType(data.delivery_type)}
                  </View>
                )}
              </View>
            </View>
            <View className='at-row at-row__align--center'>
              {data.waybill ? (
                <Fragment>
                  <View
                    className='kb-color__brand'
                    hoverStopPropagation
                    hoverClass='kb-hover-opacity'
                    onClick={() =>
                      getLogics({
                        waybill: data.waybill,
                        brand: data.brand,
                        order_no: data.order_number,
                        status_txt: data.logistic_status_txt,
                      })
                    }
                  >
                    <Text className='kb-icon__text--mr kb-size__sm'>{data.waybill}</Text>
                  </View>
                  <View onClick={(e) => handleCopy(e, data.waybill, '运单号')}>
                    <AtIcon
                      prefixClass='kb-icon'
                      value='copy-text'
                      className='kb-icon-size__sm kb-color__brand'
                    />
                  </View>
                </Fragment>
              ) : (
                <Text className='kb-color__grey'>暂无单号</Text>
              )}
            </View>
          </View>
        </View>
      </View>
      {addressList.map((item) => {
        const mobile = mergeBySpace(data[`${item.key}_mobile`], data[`${item.key}_tel`]);
        const itemCls = classNames('kb-address__item', `kb-address__item--${item.key}`);
        const tagCls = classNames(
          `kb-background__${item.color}`,
          'kb-margin-lg-r',
          'kb-address-type',
        );
        const addressInfo = {
          [`${item.key}_province`]: data[`${item.key}_province`],
          [`${item.key}_city`]: data[`${item.key}_city`],
          [`${item.key}_county`]: data[`${item.key}_county`],
          [`${item.key}_address`]: data[`${item.key}_address`],
          [`${item.key}_name`]: data[`${item.key}_name`],
          mobile,
        };
        const { userInfo: { is_admin } = {} } = Taro.kbLoginData || {};
        return (
          <View key={item.key} className={itemCls}>
            <View className='at-row at-row__align--center'>
              <View className={tagCls}>{item.tag}</View>
              <View className='item-container at-col'>
                <View className='item-info at-row at-row__align--center'>
                  <View className='item-info__value kb-color__grey'>
                    <View className='item-info__value--label kb-color__black kb-size__lg at-row at-row__align--center'>
                      <Text className='item-info__text'>{data[`${item.key}_name`]}</Text>
                      <Text className='item-info__text item-info__text-mobile'>{mobile}</Text>
                      {data.change_shipper_address_order == 1 &&
                        item.key === 'send' &&
                        is_admin == 1 && (
                          <Text className='kb-spacing-xs-lr kb-margin-md-r kb-size__sm kb-background__green kb-color__green'>
                            已替换
                          </Text>
                        )}
                      {!isEncryptionData(addressInfo) && (
                        <View
                          hoverClass='kb-hover-opacity'
                          className='kb-margin-xs-b kb-color__brand'
                          onClick={(e) =>
                            handleCopy(
                              e,
                              combineData(addressInfo),
                              item.key == 'send' ? '寄件人信息' : '收件人信息',
                            )
                          }
                        >
                          <AtIcon
                            prefixClass='kb-icon'
                            value='copy-text'
                            className='kb-icon-size__sm'
                          />
                        </View>
                      )}
                    </View>
                    <View className='kb-spacing-sm-t kb-size__sm item-info__value--desc'>
                      {data[`${item.key}_province`]}
                      {data[`${item.key}_city`]}
                      {data[`${item.key}_county`]}
                      {data[`${item.key}_address`]}
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        );
      })}
    </View>
  );
};

Index.defaultProps = {
  data: {},
  border: false,
};

Index.options = {
  addGlobalClass: true,
};

export default connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    get,
  },
)(Index);
