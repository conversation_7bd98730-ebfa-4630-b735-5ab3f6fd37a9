/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import courier from '@/assets/images/courier-pic.png';
import { makePhoneCall } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { AtAvatar, AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const {
    logistic_status_txt,
    logistic_status,
    pickup,
    remark,
    order_status,
    collect_courier_name,
    collect_courier_mobile,
  } = data;

  const waitPay = order_status == 1;

  const iconClassName = classNames([
    'kb-icon-size__24',
    `kb-color__${logistic_status == 3 ? 'greener' : 'brand'}`,
  ]);

  return (
    <View className='kb-status kb-spacing-md kb-margin-md kb-background__white'>
      <View className='at-row at-row__align--center at-row__justify--center kb-margin-md-tb kb-text__center'>
        <View>
          <AtIcon prefixClass='kb-icon' value='success' className={iconClassName} />
        </View>
        <View className='kb-size__xl kb-margin-md-l'>{logistic_status_txt}</View>
      </View>
      <View className='kb-text__center'>
        {!waitPay && <View className='kb-color__grey kb-size__sm'>预约取件：{pickup}</View>}
      </View>
      {remark && (
        <View className='kb-status__remark kb-text__center kb-margin-sm'>
          <Text className='kb-size__sm kb-spacing-sm-lr kb-status__remark--text'>
            {decodeURIComponent(remark)}
          </Text>
        </View>
      )}
      {collect_courier_name && collect_courier_mobile && (
        <View
          hoverClass='kb-hover-opacity'
          className='kb-border-t at-row at-row__align--center kb-spacing-md-t kb-margin-md-t'
          onClick={() => makePhoneCall(collect_courier_mobile)}
        >
          <AtAvatar image={courier} circle size='small' />
          <View className='kb-size__base kb-margin-md-l'>
            {collect_courier_name} {collect_courier_mobile}
          </View>
          <AtIcon
            hoverClass='kb-hover-opacity'
            prefixClass='kb-icon'
            value='phone'
            className='kb-icon-size__base kb-color__brand kb-margin-xs-b kb-margin-md-l'
          />
        </View>
      )}
    </View>
  );
};

Index.defaultProps = {
  data: {},
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
