/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$border-radius-xxl: 26px;
$p-v-md: 36px;

.kb-status {
  border-radius: $border-radius-xxl;
  &__remark--text {
    background-color: rgb(255, 245, 226);
  }
}
.kb-icon-size__24 {
  font-size: 48px;
}
.kb-border-t {
  border-top: $border-lighter;
}
.kb-spacing-md {
  padding: $p-v-md;
}
.kb-background__white {
  background-color: $color_white;
}
.kb-color__greener {
  color: #3cdb7c;
}
