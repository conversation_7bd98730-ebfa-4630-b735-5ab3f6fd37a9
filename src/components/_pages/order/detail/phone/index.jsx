/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, Fragment } from '@tarojs/taro';
import { phoneDesensitization } from '@/components/_pages/_utils';
import { View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';

const Index = (props) => {
  const { mobile } = props;

  const encryptionMobile = phoneDesensitization(mobile);

  const [front, end] = encryptionMobile.split('****');

  const [showReal, setShowReal] = useState(false);

  const handleClick = () => {
    setShowReal(true);
  };

  return (
    <View
      onClick={handleClick}
      hoverClass='kb-hover-opacity'
      className='at-row at-row__align--center'
    >
      {showReal ? (
        mobile
      ) : (
        <Fragment>
          {front}
          <AtIcon
            prefixClass='kb-icon'
            value='invisible'
            className='kb-icon-size__sm kb-color__grey kb-spacing__sm-l'
          />
          <AtIcon
            prefixClass='kb-icon'
            value='invisible'
            className='kb-icon-size__sm kb-color__grey kb-spacing__sm-r'
          />
          {end}
        </Fragment>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
