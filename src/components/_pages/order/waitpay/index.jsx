/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { useDidShowCom, useUpdate } from '@base/hooks/page';
import { debounce } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import './index.scss';
import { getWaitPayOrder, payForOrder } from './utils';

/**
 * mode float浮窗提示/modal弹窗提示
 */
const Index = (props) => {
  const { mode = 'float' } = props;
  const [order, setOrder] = useState({});
  const [isOpened, setIsOpened] = useState(false);

  const handleClick = () => {
    const { order_id, canLookDetail } = order;
    if (canLookDetail) {
      Taro.navigator({
        url: 'order/detail',
        options: {
          order_id: order_id,
        },
      });
    } else {
      payForOrder(order_id).then(() => {
        Taro.kbToast({
          text: '支付成功',
          onClose: () => {
            checkWaitPay();
          },
        });
      });
    }
  };

  const checkWaitPay = debounce(
    function () {
      getWaitPayOrder().then((data) => {
        setOrder(data);
      });
    },
    800,
    {
      leading: false,
      trailing: true,
    },
  );

  useEffect(() => {
    if (mode == 'modal') {
      setIsOpened(!!order.order_id);
    }
  }, [order.order_id]);

  useDidShowCom(() => {
    checkWaitPay();
  });

  useUpdate(({ logined }) => {
    if (!logined) return;
    checkWaitPay();
  });

  const handleModal = (key) => {
    switch (key) {
      case 'cancel':
        Taro.navigator({
          url: 'query',
        });
        break;
      case 'confirm':
        handleClick();
        break;
    }
  };

  return (
    <Fragment>
      {order.order_id ? (
        mode === 'float' ? (
          <View className='kb-waitPay-tips' onClick={handleClick}>
            您有一个待支付的订单，请尽快完成支付&gt;
          </View>
        ) : mode === 'modal' ? (
          <KbModal
            closeOnClickOverlay={false}
            isOpened={!!isOpened}
            onCancel={handleModal.bind(this, 'cancel')}
            onConfirm={handleModal.bind(this, 'confirm')}
            confirmText='立即支付'
          >
            <View className='kb-text__center'>
              <View className='kb-text__bold kb-margin-md-b'>您有一笔待支付订单</View>
              <View>为了不影响您的寄件体验，请先支付您的账单，谢谢您的配合。</View>
            </View>
          </KbModal>
        ) : null
      ) : null}
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
