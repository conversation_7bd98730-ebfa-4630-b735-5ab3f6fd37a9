/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import Taro from '@tarojs/taro';
import isString from 'lodash/isString';

// 获取未支付的订单
export function getWaitPayOrder() {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/PaymentStatus/getUserRebirthOrder',
      toastLoading: false,
      onThen: (res) => {
        const { data: { order_id, status } = {} } = res;
        resolve({
          order_id,
          canLookDetail: status !== 'payment', // 是否可以查看订单
        });
      },
    });
  });
}

// 支付订单
export function payForOrder(order_id) {
  return new Promise((resolve, reject) => {
    const createError = ({ code, msg }) => {
      return code > 0 ? { code, msg } : { msg: '获取签名失败' };
    };
    const handleFail = (error) => {
      const userCancel = error && isString(error) && error.includes('cancel');
      if (!userCancel) {
        Taro.kbToast({
          text: error.message,
        });
      }
      reject(error);
    };
    request({
      url: '/g_order_core/v2/PaymentStatus/rebirthOrderPayment',
      data: {
        order_id,
      },
      onThen: ({ code, data, msg }) => {
        if (code == 0 && data) {
          requestPayment(data)
            .then(resolve)
            .catch((e) => {
              handleFail(e);
            });
        } else {
          handleFail(createError({ code, msg }));
        }
      },
    });
  });
}
