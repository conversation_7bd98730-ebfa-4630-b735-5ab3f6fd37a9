/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { useEffect, useState } from '@tarojs/taro';

/**
 * 未支付订单提示
 */
const KbUnPayOrders = (props) => {
  const { order_ids = [], onClose = noop, courier_id } = props;

  const [isOpened, setIsOpened] = useState(false);

  const orderLength = order_ids.length || 0;

  const handleClick = () => {
    handleCancel();
    if (orderLength == 1) {
      Taro.navigator({
        url: 'order/detail',
        options: {
          order_id: order_ids[0],
        },
      });
    } else {
      Taro.navigator({
        url: 'order',
        target: process.env.MODE_ENV == 'yz' ? 'tab' : 'blank',
        key: 'routerParamsChange',
        options: {
          type: 'checkUnPayOrders',
          courier_id,
          extra_info: {
            wait_pay: 1,
          },
        },
      });
    }
  };

  const handleCancel = () => {
    setIsOpened(false);
    onClose();
  };

  useEffect(() => {
    if (orderLength > 0) {
      setIsOpened(true);
    }
  }, [orderLength]);

  return (
    <KbModal
      center
      top={false}
      title='温馨提示'
      closeOnClickOverlay={false}
      isOpened={!!isOpened}
      onCancel={handleCancel}
      onConfirm={handleClick}
      confirmText='立即支付 >'
    >
      <View className='kb-text__center'>
        <View className='kb-margin-md-b'>
          您有<Text className='kb-color__brand kb-margin-sm-lr'>{orderLength}</Text>
          笔待支付订单，请完成支付后再次尝试下单
        </View>
      </View>
    </KbModal>
  );
};

KbUnPayOrders.options = {
  addGlobalClass: true,
};

export default KbUnPayOrders;
