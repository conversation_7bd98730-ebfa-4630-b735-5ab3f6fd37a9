/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDidHideCom, useDidShowCom, useUpdate } from '@base/hooks/page';
import { noop } from '@base/utils/utils';
import Taro, { useEffect, useRef, useState } from '@tarojs/taro';
import KbInfo from '../info';
import { checkIsKdg, getLastUseRelation } from '../_utils';
import './index.scss';
import { createRelationByScanAction, useGetRelationInfo } from '~/lib/config/config.page';

const Index = (props) => {
  const { pageSource } = props;
  const [errorMsg, updateErrorMsg] = useState('');
  const [loading, updateLoading] = useState(false);
  const [data, updateInfo] = useState(null);

  const relationInfo = useGetRelationInfo();

  const ref = useRef({});

  useEffect(() => {
    ref.current.relationInfo = relationInfo;
  }, [relationInfo]);

  const triggerGetLastUseRelation = async (
    loginData = ref.current.loginData,
    needFromScanAction = false,
  ) => {
    const { logined } = loginData || {};
    if (!logined || loading) return;
    updateLoading(true);

    // 是否扫码进入并添加了下单对象
    let isFromScanAction = false;
    if (needFromScanAction) {
      // 扫码进入首页，带有驿站信息
      isFromScanAction = await createRelationByScanAction(ref.current.relationInfo);
    }
    // last_use: 表示拉取最后一次使用的，包含快递柜或驿站；否则拉取默认下单关系，不包含快递柜；
    getLastUseRelation(void 0, { last_use: 1 })
      .then((data) => {
        updateInfo(data);
        props.onRelationUpdate(data);
        if (isFromScanAction || !checkIsKdg(data)) {
          // 非快递柜或者是扫码进入并取到了下单对象，依然可设置为全局下单对象；
          Taro.kbUpdateRelationInfo({
            ...data,
            isFromDefault: true, // 避免下单关系是vip时，被再次触发添加关系
          });
        }
        updateLoading(false);
      })
      .catch((e) => {
        updateErrorMsg(e.message);
        updateLoading(false);
      });
  };

  const handleGetInfo = () => triggerGetLastUseRelation();

  useUpdate((loginData) => {
    ref.current.loginData = loginData;
    triggerGetLastUseRelation(loginData, true);
  });

  useDidShowCom(() => {
    if (!ref.current.hidden) return;
    triggerGetLastUseRelation();
  });

  useDidHideCom(() => {
    ref.current.hidden = true;
  });

  return (
    <KbInfo
      data={data}
      loading={loading}
      onGetInfo={handleGetInfo}
      description={errorMsg}
      mode='used'
      pageSource={pageSource} // 用于区分是否从取件页面来的
    />
  );
};

Index.defaultProps = {
  onRelationUpdate: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
