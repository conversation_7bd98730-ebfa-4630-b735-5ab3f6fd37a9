/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 *
 * @description 循环读取buffer
 * @param {*} buffer
 * @param {*} callback
 */
export function forEachBuffer(buffer, byteLength) {
  return (callback) => {
    const data = new Uint8Array(buffer);
    const clamped = new Uint8ClampedArray(data);
    const len = byteLength || clamped.byteLength;
    for (let i = 0; i < len; i++) {
      callback(clamped[i], i);
    }
  };
}
