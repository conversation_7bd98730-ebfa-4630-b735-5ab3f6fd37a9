/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isFunction from 'lodash/isFunction';
import isPromise from 'is-promise';
import { forEachBuffer } from './utils';

/**
 *
 * @description 相机扫描
 */
class CameraScan {
  constructor() {
    this._cameraData = null;
    this.init();
  }
  init() {
    const ctx = Taro.createCameraContext('scan_camera');
    this.ctx = ctx;
    this.listener = null;
    this._count = 0;
  }
  // 识别
  triggerOcr(callback) {
    const listenerCallback = this.listenerCallback;
    if (this._cameraData && isFunction(listenerCallback)) {
      const result = listenerCallback(
        { cameraData: this._cameraData, count: this._count },
        callback,
      );
      if (isPromise(result)) {
        result
          .then((res) => {
            if (res && res.errCode === -2) {
              // 停止监听数据
              this.stop();
            }
          })
          .catch((err) => {
            // 返回固定错误码，继续触发识别的关键
            if (err && err.errCode === -1) {
              callback();
            }
          });
      }
    }
  }
  // 停止监听数据
  stop() {
    if (!this.listener) return;
    this.listener.stop();
  }
  // 开始监听数据
  start(listenerCallback) {
    this._count = 1;
    const interval = 10; // 间隔帧数
    let start = interval;
    const ctx = this.ctx;
    this.listenerCallback = listenerCallback;
    const listener = ctx.onCameraFrame((frame) => {
      this._cameraData = frame;
      if (start >= 0) {
        start++;
      }
      if (start >= interval) {
        let total = 0;
        // 待验证逻辑：前10byte数据都是0先放弃；
        forEachBuffer(
          frame.data,
          10,
        )((item) => {
          total += item;
        });
        if (total === 0) {
          start = 0;
          return;
        }
        // 延缓识别，且保证上次识别结束；
        this.triggerOcr(() => {
          this._count++;
          start = 0;
        });
        start = -1;
      }
    });
    this.listener = listener;
    listener.start();
  }
  // 牌照
  takePhoto(callback, opts) {
    const ctx = this.ctx;
    ctx.takePhoto({
      quality: 'high',
      ...opts,
      success: ({ tempImagePath }) => callback({ tempImagePath }),
      fail: (err) => {
        console.log('takePhoto-fail', err);
      },
    });
  }
}
export default CameraScan;
