/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text, ScrollView } from '@tarojs/components';
import { AtFloatLayout } from 'taro-ui';
import { getRankLevel } from '../../_utils/welfare.lotterys';
import KbLottery from '../lottery';
import './index.scss';

const Index = (props) => {
  const { isOpened, lottery_history, onCallBack, onOpenPrize, onClose } = props;
  return (
    <AtFloatLayout isOpened={isOpened} onClose={onClose} title='中奖记录'>
      <ScrollView scrollY className='kb-scrollview'>
        {lottery_history.map((item) => {
          return (
            <View className='winningList'>
              <View>
                <View className='winningList__title'>
                  {getRankLevel(item)} {item.title} <Text className='kb-color__grey'>X 1份</Text>
                </View>
                <View className='winningList__desc kb-color__grey'>{item.update_time}</View>
              </View>
              <KbLottery
                type='list'
                ranking={item}
                onCallBack={onCallBack}
                onOpenPrize={onOpenPrize}
              />
            </View>
          );
        })}
        <View className='kb-spacing-md-b'></View>
      </ScrollView>
    </AtFloatLayout>
  );
};

Index.defaultProps = {
  isOpened: false,
  onClose: () => {},
  onCallBack: () => {},
  onOpenPrize: () => {},
  lottery_history: [],
};

Index.options = {
  addGlobalClass: true,
};
export default Index;
