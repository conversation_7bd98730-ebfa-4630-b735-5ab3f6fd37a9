/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Text, Block, Image, Button } from '@tarojs/components';
import KbAgreement from '@/components/_pages/agreement';
import { goPage, getActivityKey } from '@/components/_pages/welfare/_utils/welfare.lotterys';
import KbCheckbox from '@base/components/checkbox';
import { AtCurtain, AtButton } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const {
    isOpened,
    modalName,
    activityData,
    onClose,
    vipInfo,
    agreementStatus,
    onPay,
    onSwitchAgree,
    handleBoost,
  } = props;
  const { openTime, lottery_type, me } = activityData;
  const { name = '' } = getActivityKey(me.reason);
  const { status = '', is_first, first_price } = vipInfo;
  const timely = lottery_type == 0;
  const handleJump = () => {
    timely ? goPage.bind(null, '未中奖弹窗按钮') : handleBoost();
    onClose();
  };
  return (
    <AtCurtain isOpened={isOpened} onClose={onClose} className='winningModal'>
      {modalName === '参与成功' ? (
        status == '1' ? (
          <Block>
            <Image
              className='wImg'
              src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/09/20/6329517397f2d/202209201336516572932.png'
              mode='widthFix'
            />
            <Image className='avatar_url' src={me.avatar_url || ''} />
            <Text className='memberPopupTitle'>会员助力X1</Text>
            <Text className='memberPopupDesc'>中奖概率大幅度提升⬆</Text>
            <Text className='lotteryTxt memberOpenTime'>{'开奖时间：' + openTime}</Text>
            <AtButton className='save_button' onClick={handleBoost}></AtButton>
          </Block>
        ) : (
          <Block>
            <Image
              className='wImg'
              src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/09/19/63283d3a363a5/bck.png'
              mode='widthFix'
            />
            <Text className='openMemberPopupTitle'>{is_first == '0' ? '3' : '1'}</Text>
            <View className='openMemberPopupBtn'>
              <Text className='openMemberPopupBtn__money'>{first_price}</Text>
              <Text className='openMemberPopupBtn__text'>元</Text>
              <Text className='openMemberPopupBtn__month'>/月 </Text>
              <Text className='openMemberPopupBtn__text'>立即开通</Text>
              {is_first == '0' ? <Text className='openMemberPopupBtn__after'>首次</Text> : ''}
            </View>
            <View className='agreement'>
              <KbCheckbox
                label='我已阅读并同意'
                checked={agreementStatus}
                onChange={onSwitchAgree}
                className='kb-color__black'
              />
              <KbAgreement agreeType='serviceAgreement' />
            </View>
            <Button className='save_button' onClick={onPay}></Button>
          </Block>
        )
      ) : (
        <Block>
          <Text className='popupTitle'>{modalName == '中奖' ? '恭喜您,中奖了' : ''}</Text>
          {modalName == '未中奖' ? (
            <Text className='regret'>很遗憾，您本次未中奖去试试其他的抽奖吧</Text>
          ) : (
            <Text className='lotteryTxt'>{modalName == '中奖' ? name : ''}</Text>
          )}
          <Text className='lotterys'>
            {timely ? '每天参与抽奖可以获得丰厚奖品' : '每天完成任务可以获得更多抽奖次数'}
          </Text>
          <Image
            className='wImg'
            src='//osscdn-kbad.kuaidihelp.com/admin/ad/2022/03/10/622967eda71aa/popup.png'
            mode='widthFix'
          />
          {modalName === '中奖' ? (
            <View className='btn' onClick={onClose}>
              朕知道了
            </View>
          ) : (
            <View className='btn' onClick={handleJump}>
              {timely ? '其他抽奖' : '获得更多抽奖次数'}
            </View>
          )}
        </Block>
      )}
    </AtCurtain>
  );
};

Index.defaultProps = {
  vipInfo: {},
  isOpened: false,
  modalName: '',
  activityData: {},
  agreementStatus: false,
  onClose: () => {},
  onPay: () => {},
  onSwitchAgree: () => {},
  handleBoost: () => {},
};

Index.options = {
  addGlobalClass: true,
};
export default Index;
