/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.lotteryDetailsModal,
.winningModal {
  .btn {
    position: absolute;
    top: 74%;
    left: 50%;
    width: 80%;
    height: 80px;
    color: red;
    font-size: 40px;
    line-height: 80px;
    text-align: center;
    transform: translateX(-50%);
  }
  .more {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
  .popupTitle {
    position: absolute;
    top: 70px;
    left: 50%;
    width: 100%;
    color: red;
    font-weight: 600;
    font-size: 50px;
    text-align: center;
    transform: translateX(-50%);
  }
  .lotteryTxt {
    position: absolute;
    top: 170px;
    left: 50%;
    width: 75%;
    padding: 20px 0;
    color: #7a400e;
    font-size: 25px;
    letter-spacing: 1px;
    text-align: center;
    background: #ffd6b4;
    border-radius: 50px;
    transform: translateX(-50%);
  }
  .lotterys {
    position: absolute;
    top: 375px;
    left: 50%;
    width: 70%;
    color: #ffcc00;
    font-weight: 600;
    font-size: 28px;
    text-align: center;
    transform: translateX(-50%);
  }
  .regret {
    position: absolute;
    top: 120px;
    left: 50%;
    color: #7a400e;
    text-align: center;
    transform: translateX(-50%);
  }
}
