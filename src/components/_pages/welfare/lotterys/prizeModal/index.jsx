/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image } from '@tarojs/components';
import { AtCurtain } from 'taro-ui';
import { lotteryJump } from '../../_utils/welfare.lotterys';

const KbPrizeModal = (props) => {
  const { isOpened, ranking, onClose } = props;
  const { pic } = ranking;
  return (
    <AtCurtain isOpened={isOpened} onClose={onClose}>
      <Image
        src={pic}
        onClick={lotteryJump.bind(null, ranking)}
        mode='widthFix'
        showMenuByLongpress
      />
    </AtCurtain>
  );
};

KbPrizeModal.defaultProps = {
  isOpened: false,
  ranking: {
    pic: 'http://upload.kuaidihelp.com/inn/admin/wuliao/7479651540423117645856eb550e28295026932.jpeg',
  },
  onClose: () => {},
};

KbPrizeModal.options = {
  addGlobalClass: true,
};
export default KbPrizeModal;
