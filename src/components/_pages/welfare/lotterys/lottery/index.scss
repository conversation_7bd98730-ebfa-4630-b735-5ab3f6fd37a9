/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.WinningStatus {
  .lotteryAuthButton {
    width: 200px;
    height: 200px;
    margin: $spacing-h-lg auto;
    color: #d83e0c;
    font-size: $font-size-lg + 1;
    line-height: 200px;
    text-align: center;
    background-color: #fec804;
    border: none;
    border-radius: $border-radius-arc !important;
  }

  .lotteryButton {
    color: $color-red;
    background: unset;
    border: 1px solid $color-red;
  }

  .disabled {
    filter: grayscale(1);
  }
}
