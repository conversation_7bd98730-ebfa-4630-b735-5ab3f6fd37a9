/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import request from '@base/utils/request';
import isFunction from 'lodash/isFunction';
import { chooseAddress } from '@/components/_pages/address/_utils';
import KbLoginAuth from '@base/components/login/auth';
import classNames from 'classnames';
import { connect } from '@tarojs/redux';
import debounce from 'lodash/debounce';
import { lotteryJump } from '../../_utils/welfare.lotterys';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
export default class Index extends Component {
  constructor(props) {
    super(props);
    this.getPrizes = debounce(this.getPrize, 200);
  }

  onAuthComplete = (phone) => {
    if (phone) {
      this.getPrize(phone);
    } else {
      Taro.showToast({
        title: '领奖需在开奖后72小时内完成授权，过时无效',
        icon: 'none',
      });
    }
  };

  onSuccessToast = () => {
    const { ranking: { claim_status } = {}, onCallBack } = this.props;
    const isExceed = claim_status == '2';
    if (!isExceed && onCallBack && isFunction(onCallBack)) {
      onCallBack();
    }
    const title = isExceed
      ? '该奖品仅限开奖后72小时内可领取，请关注其他抽奖，谢谢！'
      : '奖品将在3个工作日内发出，敬请期待。';
    Taro.showToast({
      title,
      icon: 'none',
    });
  };

  onSubmitLotteryInfo = (data) => {
    const { ranking: { id = '', lid, record_id } = {} } = this.props;
    request({
      url: '/g_wkd/v2/activity/VxLottery/customAward',
      data: {
        lottery_id: lid || id,
        record_id,
        ...data,
      },
    }).then((res) => {
      const { code, msg } = res;
      if (code == 0) {
        this.onSuccessToast();
      } else {
        Taro.showToast({
          title: msg,
          icon: 'none',
        });
      }
    });
  };

  getPrize = (phone) => {
    const { ranking = {} } = this.props;
    const { prize_type = 'money' } = ranking;
    if (prize_type === 'money') {
      Taro.navigator({
        url: '/pages-3/pages/user/wallet/index',
      });
    } else if (prize_type == 'url') {
      lotteryJump(ranking);
    } else if (prize_type == 'address') {
      chooseAddress()
        .then((res) => {
          const { name, mobile, province, city, district, address: address_ } = res;
          const address = province + city + district + address_;
          this.onSubmitLotteryInfo({
            address: {
              name,
              mobile,
              address,
            },
          });
        })
        .catch((err) => {
          Taro.showToast({
            title: '领奖需在开奖后72小时内完成授权，过时无效',
            icon: 'none',
          });
          console.log(err);
        });
    } else if (prize_type == 'pic') {
      const { onOpenPrize } = this.props;
      onOpenPrize(ranking);
    } else if (prize_type == 'phone') {
      this.onSubmitLotteryInfo({ phone });
    }
  };

  render() {
    const { type, loginData: { userInfo = {} } = {}, ranking = {} } = this.props;
    const { claim_status } = ranking;
    const { mobile = '' } = userInfo;
    const isList = type == 'list';
    const Cls = classNames({
      lotteryAuthButton: !isList,
      lotteryButton: isList,
      disabled: claim_status != '0',
    });
    const btnSize = isList ? 'mini' : 'default';
    return (
      <View className='WinningStatus'>
        {claim_status != '0' ? (
          <Button className={Cls} size={btnSize} onClick={this.onSuccessToast}>
            立即领奖
          </Button>
        ) : mobile ? (
          <Button className={Cls} size={btnSize} onClick={this.getPrize.bind(this, mobile)}>
            立即领奖
          </Button>
        ) : (
          <KbLoginAuth
            className={Cls}
            scope='phoneNumber'
            useOpenType
            size={isList ? 'small' : 'normal'}
            circle={!isList}
            onAuthComplete={this.onAuthComplete}
            onAuthFail={this.onAuthComplete}
            text='立即领取'
          />
        )}
      </View>
    );
  }
}
