/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Image } from '@tarojs/components';
import './index.scss';

const Index = (props) => {
  let startPoint = 0;
  let slipFlag = false;

  const onTouchStart = (event) => {
    slipFlag = true;
    startPoint = event.touches[0];
  };

  const onTouchMove = (event) => {
    console.log(event, '-');
    if (startPoint.clientX - event.touches[event.touches.length - 1].clientX > 80 && slipFlag) {
      slipFlag = false;
      props.onCallback();
    }
  };

  return (
    <View className='kb-rouchView' onTouchStart={onTouchStart} onTouchMove={onTouchMove}>
      <View>左滑查看中奖记录</View>
      <Image
        src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2023/04/24/6445d81cabaa1/WechatIMG4069.png'
        mode='widthFix'
      />
    </View>
  );
};

Index.defaultProps = {
  onCallback: () => {},
};

Index.options = {
  addGlobalClass: true,
};
export default Index;
