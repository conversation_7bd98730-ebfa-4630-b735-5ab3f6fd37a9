/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useState } from '@tarojs/taro';
import { View, Button, Text } from '@tarojs/components';
import { getLotteryStatus, goPage } from '@/components/_pages/welfare/_utils/welfare.lotterys';
import KbSubscribe from '@base/components/subscribe';
import KbRouchView from '../rouchView';
import KbLottery from '../lottery';
import './index.scss';

// 0 未参与 1 已开奖 2 已开奖,未参与 3 已中奖 4 未中奖 5 已参与,未开奖
const Index = (props) => {
  const { lotteryHistory, ranking, activityData, handleBoost, onPayMoney, getDetails, onOpenWin } =
    props;
  const { lottery_type, lotteryNumber = 0, claim_status = 0, id } = activityData;
  const hasCount = lotteryNumber > 0;
  const timely = lottery_type == 1;
  const [subscribeLock, setLock] = useState(false);
  const [lotteryStatus, setLotteryStatus] = useState(0);
  const [level, setLevel] = useState('');

  const handleSubscribe = () => {
    setLock(true);
    hasCount ? onPayMoney() : handleBoost();
  };

  useEffect(() => {
    const [status, rankLevel] = getLotteryStatus({ ...activityData, lotteryHistory });
    setLevel(rankLevel);
    setLotteryStatus(status);
  }, [activityData]);

  return (
    <View className='kb-winning'>
      {lotteryStatus === 0 ? (
        <View className='kb-winning-join'>
          <KbSubscribe action='lotterys' onSubscribe={onPayMoney} className='wkd_lotterys'>
            <View className='Lottery' onClick={onPayMoney}>
              <View className='kb-winning-box'>
                <Text className='kb-size__lg'>点击抽奖</Text>
              </View>
            </View>
          </KbSubscribe>
        </View>
      ) : lotteryStatus === 1 ? (
        <View className='kb-winning-box'>
          <View className='kb-winning-title'>正在开奖中，预计等待6～10分钟。</View>
          <View>请稍等～</View>
        </View>
      ) : lotteryStatus === 2 ? (
        <View className='kb-winning-box kb-color__red'>
          <View className='kb-winning-title kb-color__red'>您来晚了,活动已经结束了~</View>
          <View>恭喜{activityData.winningCount || 0}位小伙伴中奖</View>
        </View>
      ) : lotteryStatus === 3 ? (
        <View className='kb-winning-box kb-size__lg kb-color__red'>
          <View className='kb-winning-title kb-color__red'>恭喜您，中奖了~</View>
          <View className='kb-size__xxl kb-size__blod'>{level}</View>
          <KbLottery ranking={{ ...ranking, claim_status, id }} onCallBack={getDetails} />
        </View>
      ) : lotteryStatus === 4 ? (
        <View className='kb-winning-box'>
          <View className='kb-winning-title'>很遗憾,本次{!timely ? '未中奖~' : '活动已结束'}</View>
          <View className='kb-color__blue' onClick={goPage}>
            去试试其他抽奖{'>'}
          </View>
        </View>
      ) : lotteryStatus === 5 ? (
        <View className='kb-winning-assist'>
          <View className='kb-winning-assist-box'>
            <Button className='Lottery' onClick={handleBoost}>
              <View className='text'>
                <View>点我助力</View>
                <View>增加中奖率</View>
              </View>
            </Button>
          </View>
          {timely && lotteryHistory.length > 0 && <KbRouchView onCallback={onOpenWin} />}
        </View>
      ) : lotteryStatus === 6 ? (
        <View className='kb-winning-join'>
          <KbSubscribe
            action={hasCount && !subscribeLock ? 'lotterys' : ''}
            onSubscribe={handleSubscribe}
            className='wkd_lotterys'
          >
            <View className='Lottery'>
              <View className='kb-winning-box'>
                <Text className='kb-size__lg'>{hasCount ? '点击抽奖' : '获取更多'}</Text>
                {hasCount ? (
                  <Text>剩余{lotteryNumber}次</Text>
                ) : (
                  <Text className='kb-size__lg'>抽奖次数</Text>
                )}
              </View>
            </View>
          </KbSubscribe>
          {timely && lotteryHistory.length > 0 && <KbRouchView onCallback={onOpenWin} />}
        </View>
      ) : lotteryStatus === 7 ? (
        <View className='kb-winning-assist'>
          <Button className='Lottery wait'>活动结束</Button>
          {timely && lotteryHistory.length > 0 && <KbRouchView onCallback={onOpenWin} />}
        </View>
      ) : null}
    </View>
  );
};

Index.defaultProps = {
  lotteryHistory: [],
  ranking: {},
  activityData: {},
  onOpenWin: () => {},
  handleBoost: () => {},
  onPayMoney: () => {},
  getDetails: () => {},
};

Index.options = {
  addGlobalClass: true,
};
export default Index;
