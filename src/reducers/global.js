/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { SAVE } from '@base/constants/global';

const INITIAL_STATE = {
  isVip: true,
  followAccount: {},
  isFollowAccount_FLS: null,
  followGZHStatus: {}, // 各种公众号的关注状态
  brands: {},
};

export default function util(state = INITIAL_STATE, action) {
  switch (action.type) {
    case SAVE:
      return {
        ...state,
        ...action.payload,
      };
    default:
      return state;
  }
}
