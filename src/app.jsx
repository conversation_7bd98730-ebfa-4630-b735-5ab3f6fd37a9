/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import '@/styles/icon.scss';
import { buriedPoint, checkAppUpdate, toggleTabBarRedDot } from '@/utils/qy';
import { fixSwanScanEntry } from '@base/components/_utils/';
import resolve404 from '@base/utils/404/';
import { getSystemInfoSync } from '@base/utils/utils';
import { Provider } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import './index';
import Index from './pages/index';
import configStore from './store';

/**
 * redux数据
 */
const store = configStore();

class App extends Component {
  config = preval.require('./app.config/');

  componentDidMount() {
    const { params } = this.$router;
    getSystemInfoSync();
    buriedPoint(params);
    checkAppUpdate();
    toggleTabBarRedDot();
    if (this.config && this.config.tabBar && this.config.tabBar.list) {
      const WelfareTabIndex = this.config.tabBar.list.findIndex(
        (item) => item.pagePath == 'pages/welfare/continuity/index',
      );
      if (WelfareTabIndex > -1) {
        toggleTabBarRedDot('show', WelfareTabIndex, 'FLNew');
      }
    }
    Taro.APP_TS = new Date().getTime();
    // 保存启动数据
    const { query, referrerInfo: { extraData = null } = {} } = params;
    Taro.launchParams = {
      ...query,
      ...extraData,
    };
  }

  componentDidShow() {
    fixSwanScanEntry(this, 'show');
  }

  componentDidHide() {
    fixSwanScanEntry(this, 'hide');
    // 主要是为地址填写时的粘贴识别设置标志
    Taro.canClipboard = true;
  }

  componentDidNotFound(opts) {
    resolve404(opts);
  }

  onError(error) {
    console.log('onError', error);
  }
  onUnhandledRejection(res) {
    console.log('onUnhandledRejection', res);
  }
  // 在 App 类中的 render() 函数没有实际作用
  // 请勿修改此函数
  render() {
    return (
      <Provider store={store}>
        <Index />
      </Provider>
    );
  }
}

Taro.render(<App />, document.getElementById('app'));
