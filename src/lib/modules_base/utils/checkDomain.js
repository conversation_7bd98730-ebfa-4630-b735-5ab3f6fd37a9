/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 域名检测机制：
 * 根据域名响应时长，判断使用哪个域名
 */
import Taro from '@tarojs/taro';

const now = () => new Date().getTime();
const invalid = 72 * 3600 * 1000;
// 本地存储的key
const localKey = 'minDomainData';
let checkedDomain;
let currentDomainData = null;

// 请求
const request = (url, then = () => {}) => {
  const ts = now();
  Taro.request({
    url,
    complete: (res) => {
      const { errMsg, data } = res;
      then(now() - ts, errMsg === 'request:ok' && data ? true : false);
    },
  }).catch(() => {});
};

// 解析域名
const parseDomain = (domain) => {
  const reg1 = /(https:\/\/)([a-zA-Z0-9]+)(.+)/;
  const reg2 = /([a-zA-Z]+)([0-9]?)/;
  const [url, protocol, prefix, suffix] = domain.match(reg1);
  const [, name, nameIndex = ''] = prefix.match(reg2);
  return {
    url,
    protocol,
    name,
    nameIndex,
    suffix,
  };
};

// 验证获取最快响应url；一次启动同一域名暂不支持多次验证请求
const checkDomain = (domain) => {
  const { protocol, name, suffix } = parseDomain(domain);
  const length = 5;
  let minUrl,
    count = 0,
    minTs;
  if (checkedDomain === domain) {
    return;
  } else {
    checkedDomain = domain;
  }
  let i = 0;
  while (i < length) {
    const currentUrl = `${protocol}${name}${i === 0 ? '' : 1 + i}${suffix}`;
    request(currentUrl, (ts, isSuccess) => {
      count++;
      if ((!minTs || ts < minTs) && isSuccess) {
        minTs = ts;
        minUrl = currentUrl;
      }
      if (count === length && minUrl) {
        // 所有请求完成，耗时最小且请求成功的域名
        currentDomainData = {
          url: minUrl,
          ts: now(),
        };
        Taro.setStorage({
          key: localKey,
          data: currentDomainData,
        });
        // 如果当前超时验证的域名和超时时间最短的域名相同，则重置已验证域名
        // if (minUrl === checkedDomain) {
        //   checkedDomain = "";
        // }
      }
    });
    i++;
  }
};

// 延迟验证
const triggerCheckDomain = (domain) => {
  if (!/https:\/\/vapi[0-9]?\.kuaidihelp\.com/.test(domain) || !domain) {
    return;
  }
  checkDomain(domain);
};

// 验证本地存储的url是否超时，超时则清除
const getCheckedDomain = (domain) => {
  // 需要修改
  currentDomainData = currentDomainData || Taro.getStorageSync(localKey) || {};
  const { url, ts } = currentDomainData;
  if (url) {
    if (now() - ts > invalid) {
      // 缓存已超时
      Taro.removeStorageSync(localKey);
      triggerCheckDomain(url);
    }
    return url;
  }
  return domain;
};

export { getCheckedDomain, triggerCheckDomain as checkDomain, localKey };
