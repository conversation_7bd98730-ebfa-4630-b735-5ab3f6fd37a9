/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { now } from '@base/utils/utils';
import Taro from '@tarojs/taro';

const map_key_web = '913826dfa70cf0bf04e334cf4ca2fa0e';
// 创建当前toast的text值
const createToastText = (show, text) => {
  if (show) {
    return typeof show === 'string' ? show : text;
  } else {
    return null;
  }
};
// 请求成功后toast回显操作
const toastBack = (params) => {
  const { toastLoading, status, text } = params;
  if (text) {
    Taro.kbToast({
      status,
      text,
    });
  } else {
    toastLoading && Taro.kbToast({ isOpened: false });
  }
};
export const resolveAddressByLocation = (gres) => {
  const { latitude, longitude } = gres;
  const onlyCity = ['北京市', '天津市', '上海市', '重庆市'];
  return new Promise((resolve, reject) => {
    request({
      sendSessionId: false,
      wrapData: false,
      url: 'https://restapi.amap.com/v3/geocode/regeo',
      mastLogin: false,
      data: {
        radius: 100,
        homeorcorp: 1,
        location: `${longitude},${latitude}`,
        key: map_key_web,
      },
      toastLoading: false,
    }).then((res) => {
      if (res.regeocode && res.regeocode.addressComponent) {
        const { addressComponent } = res.regeocode;
        let { province, city, district, township, neighborhood, building, streetNumber } =
          addressComponent;
        if (!city || city.length === 0) {
          city = onlyCity.indexOf(province) >= 0 ? province : district;
        }

        // 兼容直辖市
        if (!district || district.length === 0) {
          district = city;
        }
        // 详细地址
        let address =
          res.regeocode.formatted_address ||
          township +
            (neighborhood && neighborhood.name) +
            (building && building.name) +
            (streetNumber && streetNumber.street + streetNumber.number);
        const areaReg = new RegExp(`${province}|${city}|${district}`, 'g');
        address = address.replace(areaReg, '');
        const gpsInfo = {
          province,
          city,
          district,
          address,
          latitude,
          longitude,
          ts: now(),
        };
        resolve(gpsInfo);
      } else {
        reject({
          code: 91,
          msg: `定位失败：地址解析失败（${res.info}）`,
        });
      }
    });
  });
};
// GPS初始化 isAuto 初始加载完成自动获取数据
const gps = (params = {}) => {
  const { refresh, toastLoading = true, toastSuccess = true, toastError = true } = params;
  if (toastLoading) {
    Taro.kbToast({
      status: 'loading',
      text: '定位中',
    });
  }
  const gpsIns = new Promise((resolve, reject) => {
    if (Taro.gpsInfo && !refresh) {
      const currentTs = now();
      //  应用不重启的情况下，一分钟内不会重新请求接口 ，若refresh = true则跳过此逻辑
      if (currentTs - Taro.gpsInfo.ts <= 1 * 60000) {
        resolve({
          ...Taro.gpsInfo,
          source: 'cache',
        });
        return;
      }
    }

    Taro.getLocation({ type: 'gcj02' })
      .then((gres) => {
        resolveAddressByLocation(gres)
          .then((gpsInfo) => {
            Taro.gpsInfo = gpsInfo;
            resolve({
              ...Taro.gpsInfo,
              source: 'remote',
            });
          })
          .catch(reject);
      })
      .catch((err) => {
        reject({
          code: 92,
          msg: `定位失败：无法获取当前位置（${
            process.env.PLATFORM_ENV === 'alipay' ? err.errorMessage : err.errMsg
          }）`,
        });
        if (process.env.PLATFORM_ENV === 'alipay') {
          my.showAuthGuide({
            authType: 'LBS',
          });
        }
      });
  });
  gpsIns
    .then(() => {
      toastBack({
        toastLoading,
        status: 'success',
        text: createToastText(toastSuccess, '定位成功'),
      });
    })
    .catch((res) => {
      toastBack({
        toastLoading,
        status: 'error',
        text: createToastText(toastError, res.msg),
      });
    });
  return gpsIns;
};

export default gps;
