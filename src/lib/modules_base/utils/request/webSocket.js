/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isFunction from 'lodash/isFunction';
import { encryptionNew } from './encryption';
import { sleep } from '../utils';
// import logger from '../logger';

class WebSocket {
  constructor(props) {
    this.options = {
      url: '',
      heart: {},
      heartTime: 30,
      ...props,
    };
    this.connect();
  }

  connect() {
    return new Promise(async (resolve) => {
      console.log('connect====>');
      const { url, pathname, heart, onOpen, onError, onClose, onMessage } = this.options;
      const pathQuery = await encryptionNew(pathname, {
        // sessionID=>openid 更换为和发送数据中id字段一致
        sessionID: heart.data.id,
        isWebsocket: true,
        appId: 10100,
      });
      // this.destroy();
      // console.info('31======>');

      Taro.connectSocket({
        url: url + pathQuery,
        success: (res) => {
          console.log('链接成功==>res33', res);
        },
        fail: (res) => {
          console.log('链接失败==>res36', res);
        },
        complete: (res) => {
          console.log('链接完成==>res39', res);
        },
      }).then((SocketTask) => {
        console.log('SocketTask==>42', SocketTask);
        this.SocketTask = SocketTask;
        SocketTask.onOpen((res) => {
          console.log('onOpen==>res 46', res, SocketTask);
          this.startHeart();
          this.onStatusChange();
          if (isFunction(onOpen)) {
            onOpen(SocketTask, res);
          }
          resolve(SocketTask);
        });

        SocketTask.onError((res) => {
          console.log('onError==>res', res);
          this.SocketTask = null;
          this.stopHeart();
          this.onStatusChange('error');
          if (res && res.reason === 'destroy') {
            console.log('主动销毁链接');
          } else {
            // this.reconnect();
          }
          if (isFunction(onError)) {
            onError(res);
          }
        });

        SocketTask.onClose((res) => {
          // console.log('onClose==>res', res);
          this.SocketTask = null;
          this.stopHeart();
          this.onStatusChange('close');
          if (res && res.reason === 'destroy') {
            console.log('主动销毁链接');
            if (isFunction(onClose)) {
              onClose(res);
            }
          } else {
            // this.reconnect();
          }
        });

        SocketTask.onMessage((res) => {
          this.onStatusChange();
          if (isFunction(onMessage)) {
            onMessage(JSON.parse(res.data));
          }
        });
      });
    });
  }

  reconnect() {
    console.log('重新连接');
    this.onStatusChange('reconnect');
    this.reconnectTimer = clearTimeout(this.reconnectTimer);
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, 3000);
  }

  destroy() {
    return new Promise(async (resolve) => {
      this.reconnectTimer = clearTimeout(this.reconnectTimer);
      if (this.SocketTask && this.SocketTask.readyState == 1) {
        // 清除时若链接开启状态，应关闭链接
        this.SocketTask.close({
          code: 1000,
          reason: 'destroy',
        });
      }
      await sleep(100);
      resolve();
    });
  }

  startHeart() {
    const { heart, heartTime } = this.options;
    const heartFn = () => {
      this.sendMessage(heart);
    };
    if (heart && heartTime > 0) {
      this.stopHeart();
      heartFn();
      this.heartTimer = setInterval(heartFn, heartTime * 1000);
    }
  }

  stopHeart() {
    this.heartTimer && clearInterval(this.heartTimer);
  }

  async sendMessage(data = {}) {
    let SocketTask = this.SocketTask;
    // console.log('this.SocketTask===133', this.SocketTask, data);
    // if (data.action == 'forward') {
    //   logger.info('2025-03-30', 'sendMessage', {
    //     pickup_code: data.pickup_code,
    //     state: SocketTask ? SocketTask.readyState : '--',
    //   });
    // }
    if (!SocketTask || SocketTask.readyState != 1) {
      SocketTask = await this.connect();
    }
    if (SocketTask) {
      SocketTask.send({
        data: JSON.stringify(data),
      });
    }
  }

  onStatusChange(status) {
    const { onStatusChange } = this.options;
    const { readyState } = this.SocketTask || {};
    this.status = readyState == 1 ? 'success' : 'error';
    if (isFunction(onStatusChange)) {
      onStatusChange(status || this.status);
    }
  }
}

export default WebSocket;
