/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { apiOptions } from '@base/config';
import check from '@base/utils/check';
import {
  filterAvailableValue,
  getMadeUserInfo,
  getPage,
  getSessionIDFromLoginData,
  getUserStateByComplete,
  jsErrorCatch,
  jsonParse,
  jsonParse2,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isPlainObject from 'lodash/isPlainObject';
import logger from '../logger';
import { encryptionNew } from './encryption';
import {
  batchUrl,
  clearLoginStatus,
  createToastText,
  fixSetCookie2Header,
  triggerToast,
  updateLoadingStatus,
  updateSetCookie,
} from './request-util';

// request请求封装
const request = (params = {}, this_ = getPage(-1)) => {
  const {
    url,
    data,
    header,
    method = 'POST',
    autoTriggerLoginModal = false, // 未登录时触发登录弹窗
    toastLoading = true, //开启加载中toast，支持boolean|string
    toastSuccess = false, //开启成功toast，支持boolean|string
    toastError = false, //开启错误toast，支持boolean|string
    mastLogin = true, //是否必须登录
    mastHasMobile = process.env.MODE_ENV === 'wkd' ? false : true, // 必须有手机号
    formatResponse = (res) => res, //格式化响应，组装格式{code:0,msg:"",data:{}}
    formatRequest = (req) => req, //格式化请求数据
    form = null, //有此配置项，则发起表单检测
    loadingStatusKey = '', //有次配置项切传入this_，动态新请求状态
    onCustomCheck = () => {}, //此方法为表单以外的自定义检测结果返回{code:11（错误代码），msg:"错误信息"}
    onIntercept = () => false, //拦截请求，
    onReady = () => {}, //准备就绪，请求开始前
    onStop = () => {}, //请求终止
    onThen = () => {}, //请求结束
    requestDataType = 'form', //发送数据类型form为表单数据，file为文件数据；调用客户端提供的不同接口
    sendSessionId = true, //发送session_id
    wrapData = process.env.MODE_ENV === 'wkd' ? true : false, //包裹请求数据，即data:JSON.stringfy(requestData)
    quickTriggerThen = false, //快速触发then回调，不会等待弹出错误回调
    directTriggerThen = false, // 不触发toast
    version, //请求版本号
    nonceKey,
    ...other
  } = params;

  // 请求数据
  let requestData = {
    ...(formatRequest(data) || data),
  };

  filterAvailableValue(requestData);

  // 请求中断处理
  const noopThen = (by, ...arg) => {
    onStop(by, ...arg);
    return new Promise(() => {});
  };

  // 请求头
  const requestHeader = {
    'Content-Type': 'application/x-www-form-urlencoded',
    // "Timeout-Domain": "mpt.kuaidihelp.com", // 测试环境超时测试
    ...header,
  };

  // 登录且有手机号才允许请求接口
  // 需要登录且必须有手机号，即为需要完整信息才能请求接口，
  // mastHasMobile === false 则只要登录就能调用接口
  const needCompleteUserInfo = mastLogin && mastHasMobile;
  const loginStorageDataInfo = getUserStateByComplete(needCompleteUserInfo);
  if (mastLogin && !loginStorageDataInfo) {
    // 无登录信息，如果是必须登录的接口，直接跳过，触发登录逻辑
    // 如果已登录未绑定提示绑定
    const isLoginButHasNoMobile = !!getUserStateByComplete(false);
    triggerToast(this_, {
      text: toastError && `您还没有${isLoginButHasNoMobile ? '绑定手机号' : '登录'}`,
      quickTriggerThen: true,
      toastLoading,
      then: () => {
        if (autoTriggerLoginModal) {
          Taro.kbUpdateLoginData({ status: 'modal' });
        }
      },
    });
    // 触发登录逻辑
    return noopThen('login_no');
  }
  // 数据检测
  let checkResult = {
    code: 0,
    ...onCustomCheck(requestData),
  };
  if (checkResult.code === 0 && form) {
    checkResult = check({ form, data: requestData });
  }
  if (checkResult.code > 0) {
    const { msg } = checkResult;
    if (msg) {
      Taro.kbToast(
        {
          text: msg,
        },
        this_,
      );
    }
    return noopThen('check_fail', checkResult);
  }

  // 追加登录信息到接口中，兼容支付宝，需要将session_id拼到url中
  let sessionID;
  if (loginStorageDataInfo) {
    sessionID = getSessionIDFromLoginData(
      {
        userInfo: loginStorageDataInfo,
      },
      false,
    );
    // 有登录信息
    let sessionKey = 'sessionid';

    // 兼容微快递，请求时携带userId
    if (process.env.MODE_ENV === 'wkd') {
      const { userkey = 'user_id', sendKbId = false } = params;
      const { user_id, kb_id } = loginStorageDataInfo;
      userkey && (requestData[userkey] = user_id);
      if (sendKbId) {
        requestData.kb_id = kb_id;
      }
      sessionKey = 'session_id';
    }

    if (sendSessionId && sessionID) {
      requestData[sessionKey] = sessionID;
      // 新签名需要统一使用key=session_id
      let cookieStr = `${sessionKey}=${sessionID}`;
      if (process.env.MODE_ENV !== 'wkd') {
        cookieStr = `${cookieStr};session_id=${sessionID}`;
      }
      requestHeader.Cookie = cookieStr;
    }
  }

  // 拦截请求，可兼容其他方式提交
  const interceptResult = onIntercept(requestData, onThen);
  if (interceptResult) {
    // 返回对象，正常响应；否则仅做拦截
    if (isPlainObject(interceptResult)) {
      jsErrorCatch(async () => {
        const { triggerFormatResponse = false, ...responseData } = interceptResult;
        const data = triggerFormatResponse
          ? {
              ...responseData,
              ...(await formatResponse(responseData, requestData)),
            }
          : responseData;
        onThen(data, requestData);
      });
      return new Promise((resolve, reject) => {
        try {
          resolve(interceptResult);
        } catch (err) {
          reject(err);
        }
      });
    }
    return noopThen('intercept');
  }

  if (!url) {
    return noopThen('url_no');
  }

  // 触发loading状态
  triggerToast(this_, { toastLoading }, 'loading');

  // 准备就绪，即将开始发起请求；此时仍可修改数据结构
  const readyedRequestData = onReady(requestData) || requestData;
  requestData = {
    ...readyedRequestData,
    ...getMadeUserInfo(),
  };
  if (!wrapData || requestDataType === 'file') {
    Object.keys(requestData).map((key) => {
      let data = requestData[key];
      if (typeof data === 'object') {
        requestData[key] = JSON.stringify(data);
      }
    });
  }
  // 更新loading状态
  updateLoadingStatus(this_, loadingStatusKey, true);

  const createUrl = () => batchUrl(url, requestData);
  let requestTask;
  let requestBy;
  let triggerUpload;
  // 补充set-cookie到header;
  fixSetCookie2Header(requestHeader);
  const cookie = process.env.PLATFORM_ENV === 'alipay' ? requestHeader.Cookie : '';
  switch (requestDataType) {
    case 'form':
      // 普通网络请求
      requestBy = createUrl();
      triggerUpload = async (rUrl) => {
        const rUrl_ = await encryptionNew(rUrl, { cookie, sessionID, nonceKey }, requestData);
        const r = await Taro.request({
          url: rUrl_,
          data: wrapData
            ? {
                data: JSON.stringify(requestData),
              }
            : requestData,
          method,
          header: requestHeader,
          success: updateSetCookie,
          ...other,
        });
        r.requestUrl = rUrl_;
        // 重放请求，log日志
        logger.info('2023-04-30', '请求重放-form', rUrl_);
        return r;
      };
      break;
    case 'file':
      if (process.env.MODE_ENV === 'wkd') {
        requestData.data = '""';
      }
      // 文件上传
      const { filePath = '', fileType = 'image', name = 'file', ...formData } = requestData;
      // 兼容支付宝
      requestBy = jsonParse2(filePath);
      triggerUpload = async (filePath) => {
        const rUrl = createUrl();
        const rUrl_ = await encryptionNew(rUrl, { cookie, sessionID, nonceKey }, requestData);
        const r = await Taro.uploadFile({
          url: rUrl_,
          fileType,
          filePath,
          name,
          formData: {
            name,
            ...formData,
          },
          header: requestHeader,
          success: updateSetCookie,
        });
        r.requestUrl = rUrl_;
        // 重放请求，log日志
        logger.info('2023-04-30', '请求重放-file', rUrl_);
        return r;
      };
      break;
  }

  // 兼容批量请求
  if (isArray(requestBy)) {
    requestTask = new Promise((resolve, reject) => {
      const { length: requestByLength } = requestBy;
      const responses = [];
      const check2Resolve = (data, index) => {
        responses.push({
          ...jsonParse(data),
          index,
        });
        if (responses.length === requestByLength) {
          resolve({
            data: {
              code: 0,
              data: responses.sort((a, b) => (a.index > b.index ? 1 : -1)),
            },
          });
        }
      };
      requestBy.map((item, index) => {
        if (!item) {
          check2Resolve({ code: 91, msg: '无请求', data: {} }, index);
          return;
        }
        triggerUpload(item)
          .then((res) => check2Resolve(res.data, index))
          .catch(reject);
      });
    });
  } else {
    requestTask = triggerUpload(requestBy);
  }

  // 任务响应
  const response = requestTask
    .then(async (res) => {
      // formatResponse可保证统一的响应处理逻辑，主要为兼容部分接口响应不统一的问题
      const responseData =
        res.statusCode === 404
          ? {
              code: 91,
              msg: `${res.requestUrl.split('?')[0].replace(apiOptions.domain, '')}接口${
                res.statusCode
              }`,
              data: {},
            }
          : jsonParse(res.data);
      res.data = responseData;
      const data = {
        ...responseData,
        ...(await formatResponse(responseData, requestData)),
      };
      const { code = 0, msg = '' } = data;
      // 根据状态值判断是否展示结果
      const text = createToastText(code == 0 ? toastSuccess : toastError, msg);
      const status = code == 0 ? 'success' : 'error';
      updateLoadingStatus(this_, loadingStatusKey, false);

      triggerToast(this_, {
        status,
        text: !directTriggerThen && text,
        quickTriggerThen,
        toastLoading,
        then: () => {
          // 登录失效触发登录
          if (code == 1011 || code == 1010) {
            clearLoginStatus();
          }
          jsErrorCatch(() => onThen({ ...data }, requestData));
        },
      });
      if (`${code}` === '412') {
        // 重放请求，log日志
        logger.info('2023-04-30', '请求重放-412', res.requestUrl);
      }
      return { ...data };
    })
    .catch((err) => {
      const { errMsg, errorMessage, error } = err;
      const msg = errMsg || errorMessage || (error ? `平台错误码：${error}` : err.toString());
      const data = {
        code: 90,
        data: {},
        msg,
      };
      // 中断请求
      if (error != 9) {
        Taro.vibrateLong();
      }
      updateLoadingStatus(this_, loadingStatusKey, false);
      triggerToast(this_, {
        status: 'error',
        text: !directTriggerThen && msg,
        quickTriggerThen: true,
        toastLoading,
        then: () => {
          jsErrorCatch(() => onThen({ ...data }, requestData));
        },
      });
      return { ...data };
    });
  // 任务响应中兼容中断请求
  response.abort = requestTask.abort;
  return response;
};
export default request;
