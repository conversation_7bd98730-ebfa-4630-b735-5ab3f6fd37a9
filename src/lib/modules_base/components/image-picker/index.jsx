/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { check } from '@base/utils/rules';
import { chooseImageByActionSheet, debounce, getPage } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtAvatar, AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { path, custom, icon, text, files, count, sizeType, api, onChange } = props;
  const { length: filesLength } = files;
  // 剩余可选张数
  const restCount = count - filesLength;

  // 移除
  const onDelete = (index, e) => {
    e.stopPropagation();
    const list = [...files];
    list.splice(index, 1);
    onChange(list);
  };
  // 预览图片
  const onPreview = (index) => {
    let urls = files;
    if (path) {
      urls = urls.map((item) => path + item);
    }
    Taro.previewImage({ urls, current: urls[index] });
  };
  // 去抖动
  const triggerDebounce = debounce((page, text) =>
    Taro.kbToast(
      {
        text,
      },
      page,
    ),
  );
  // 选择图片
  const onSelect = () => {
    const page = getPage(-1);
    if (restCount <= 0) {
      Taro.kbToast(
        {
          text: `最多只允许上传${count}张`,
        },
        page,
      );
      return;
    }
    chooseImageByActionSheet(
      {
        count: restCount,
        sizeType,
      },
      page,
    ).then((res) => {
      const { tempFilePaths } = res;
      onChange([...files, ...tempFilePaths], 'select', res);
      if (api) {
        const { url, data, ...rest } = api;
        if (!url) return;
        request(
          {
            url,
            data: {
              filePath: tempFilePaths,
              ...data,
            },
            ...rest,
            toastError: false,
            toastSuccess: false,
            requestDataType: 'file',
            onThen: (res) => {
              const { data } = res;
              const list = [];
              const errorIndexs = []; // 索引 + 1，表示第i张
              const errorMsgs = []; // 保存信息
              data.map((item) => {
                const { code, data, index = 0, msg } = item;
                if (code > 0) {
                  errorIndexs.push(1 + index);
                  msg && errorMsgs.push(msg);
                } else {
                  const { img } = data || {};
                  img && list.push(img.replace('http:', 'https:'));
                }
              });
              // 部分图片上传失败
              if (!!errorIndexs.length) {
                // 某些状况下，无法正常显示错误信息，该部分相关问题需要优化（未解决）
                triggerDebounce(
                  page,
                  errorMsgs.length === 1 ? errorMsgs[0] : `第${errorIndexs.join('、')}张图上传失败`,
                );
              }
              onChange([...files, ...list], 'upload', count === 1 ? data[0] : res);
            },
          },
          page,
        );
      }
    });
  };
  return (
    <View className='kb-image-picker'>
      {!!filesLength && (
        <View className='picker-preview'>
          {files.map((item, index) => (
            <View key={`img_${index}`} className='picker-preview__item'>
              <View
                onClick={onDelete.bind(null, index)}
                className='picker-preview__item--delete'
                hoverClass='kb-hover-opacity'
              >
                <AtIcon value='close-circle' className='kb-icon-size__base kb-color__red' />
              </View>
              <View
                className='picker-preview__item--image'
                onClick={onPreview.bind(null, index)}
                hoverClass='kb-hover-opacity'
              >
                <AtAvatar
                  image={check('tmpFile', item).code === 0 ? item : path + item}
                  size='large'
                />
              </View>
            </View>
          ))}
        </View>
      )}
      {restCount > 0 && (
        <View className='picker-bar'>
          {!custom ? (
            <View className='picker-bar__default' hoverClass='kb-hover' onClick={onSelect}>
              {icon && <View className='picker-bar__default--icon' />}
              {text && <View className='picker-bar__default--text'>{text}</View>}
            </View>
          ) : (
            <View className='picker-bar__custom' hoverClass='kb-hover-opacity' onClick={onSelect}>
              {props.children}
            </View>
          )}
        </View>
      )}
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  sizeType: ['original', 'compressed'],
  count: 1,
  files: [],
  custom: false,
  icon: 'add',
  text: '添加图片',
  api: {},
  onChange: () => {},
  path: '',
};

export default Index;
