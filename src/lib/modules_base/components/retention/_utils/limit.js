/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getStorage, setStorage } from '@base/utils/utils';
import dayjs from 'dayjs';

function createStorageKey(key = 'common') {
  return `hold-day-click-${key}`;
}

// 只当天显示hold广告
const oneDayKeys = ['query.detail'];
export function getNowDay() {
  return dayjs().format('YYYY-MM-DD');
}
export function setHoldDay(key) {
  if (!oneDayKeys.includes(key)) return;
  setStorage({
    key: createStorageKey(key),
    data: getNowDay(),
  });
}
export function getHoldDay(key) {
  return new Promise((resolve) => {
    if (!oneDayKeys.includes(key)) {
      resolve();
      return;
    }
    getStorage({
      key: createStorageKey(key),
    })
      .then((res) => {
        const { data = '' } = (res && res.data) || {};
        resolve(data);
      })
      .catch(() => resolve());
  });
}
