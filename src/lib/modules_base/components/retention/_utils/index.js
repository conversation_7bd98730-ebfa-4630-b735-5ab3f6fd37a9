/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const defaultConfig = {
  'query.detail': {
    redirectUrl: '/pages-0/pages/query/detail/target/index?',
    source: '抽奖详情页',
    position: '19',
  },
  'lottery.detail': {
    redirectUrl: '/pages-0/pages/welfare/lotterys/target/index?',
    source: '抽奖详情页',
    position: '20',
  },
};

function getAdPositionReq(key = '') {
  const { position = '' } = defaultConfig[key] || {};
  return {
    type: 'miniapp',
    platform: process.env.PLATFORM_ENV === 'weapp' ? 'wkdmini' : 'wkdaliapp',
    position: position,
  };
}

function getRedirectConfig(key) {
  return defaultConfig[key];
}

export { getAdPositionReq, getRedirectConfig };
