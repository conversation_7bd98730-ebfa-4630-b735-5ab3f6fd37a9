/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDidHideCom, useDidShowCom, useUpdate } from '@base/hooks/page';
import { View, Image } from '@tarojs/components';
import request from '@base/utils/request';
import { reportAnalytics } from '@base/utils/utils';
import { adNavigator } from '@/components/_pages/ad-extension/sdk/';
import Taro, { useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import qs from 'qs';
import { getAdPositionReq, getRedirectConfig } from './_utils';
import { setHoldDay, getHoldDay, getNowDay } from './_utils/limit';
import './index.scss';

/**
 *
 * @param {{keys: string; params: any}} props
 * @returns
 */

const reportKey = 'retention';
const KbRetention = (props) => {
  const { keys, params } = props;
  const actionRef = useRef([]);
  const [image, updateImage] = useState('');
  const queryData = getAdPositionReq(keys);
  const { redirectUrl = '', source = '' } = getRedirectConfig(keys) || {};

  useUpdate((loginData) => {
    const { logined } = loginData || {};
    if (logined) {
      getAdList();
    }
  }, []);
  useDidShowCom(() => {
    if (!actionRef.current.isHide) return;
    const { imgUrl = '' } = actionRef.current.adInfo;
    if (!imgUrl) {
      if (!actionRef.current.autoJump) {
        actionRef.current.autoJump = true;
        handleClickImage(null, 'self');
      }
    } else {
      const { title = '' } = actionRef.current.adInfo;
      reportAnalytics({
        key: reportKey,
        title,
        source,
        options: '展示',
      });
      updateImage(imgUrl);
    }
  });
  useDidHideCom(() => {
    actionRef.current.isHide = true;
  });

  const handleClickImage = (e, target) => {
    const { adUrl, title } = actionRef.current.adInfo || {};
    const options = target === 'self' ? '自动跳转' : '点击';
    setHoldDay(keys);
    adNavigator({
      adUrl,
      target,
      report: {
        key: reportKey,
        title,
        source,
        options,
      },
      onArrived: () => {
        reportAnalytics({
          key: reportKey,
          title,
          source,
          options: `${options} - 跳转成功`,
        });
      },
    });
  };
  const handleJump = (opts, defaultTarget) => {
    const { adUrl = '' } = opts || {};
    const target = defaultTarget || (adUrl ? 'blank' : 'self');
    // 跳转配置
    const cfg = {
      url: `${redirectUrl}${qs.stringify({
        ...params,
      })}`,
    };
    if (target === 'blank') {
      Taro.navigateTo(cfg);
    } else {
      Taro.redirectTo(cfg);
    }
  };
  const setActivity = (adInfo) => {
    const {
      adUrl = '',
      imgUrl = '',
      title = '',
    } = {
      ...actionRef.current.adInfo,
      ...adInfo,
    };
    if (adUrl) {
      actionRef.current.adInfo = { adUrl, title, imgUrl };
    }
  };
  const getAdList = async () => {
    if (process.env.PLATFORM_ENV === 'weapp' && process.env.MODE_ENV === 'wkd') {
      const holdDay = await getHoldDay(keys);
      console.log('holdDay', holdDay);
      if (holdDay === getNowDay()) {
        handleJump();
        return;
      }
      request({
        url: '/g_tbk/v2/AdConfig/getAdConfig',
        toastLoading: false,
        data: queryData,
        onThen: ({ data } = {}) => {
          if (isArray(data)) {
            actionRef.current.adList = data;
            setActivity(data[0]);
          }
          handleJump(actionRef.current.adInfo);
        },
      });
    } else {
      handleJump();
    }
  };

  return (
    <View
      className={`kb-retention kb-retention__${image ? 'show' : 'hidden'}`}
      onClick={handleClickImage}
    >
      <Image
        src={image}
        className='kb-retention__image'
        mode='widthFix'
        lazyLoad
        showMenuByLongpress
      />
    </View>
  );
};

export default KbRetention;
