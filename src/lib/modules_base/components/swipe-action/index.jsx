/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import isFunction from 'lodash/isFunction';
import { AtSwipeAction } from 'taro-ui';
import './index.scss';

const KbSwipeAction = (props) => {
  const {
    title,
    hoverClass: hoverClassProps,
    autoClose,
    isOpened,
    options,
    longTapOptions,
    disabled,
    onOpened,
    onClosed,
    onClick,
    className,
  } = props;
  const hoverClass = disabled ? 'none' : hoverClassProps;

  /**
   *
   * @description 获取自定义的选项
   * @returns
   */
  const getActionItems = () => {
    if (isFunction(longTapOptions)) {
      return longTapOptions();
    }
    return longTapOptions;
  };

  const onTriggerEvent = (e) => {
    if (disabled) return;
    onClosed();
    let type = `${e.type}`.toLocaleLowerCase();
    if (type === 'longtap' || type === 'longpress') {
      const actionItems = getActionItems();
      Taro.kbActionSheet({
        title,
        items: actionItems || options,
        onClick: (_, item) => {
          onClick(item);
        },
      });
      Taro.vibrateShort();
    }
  };

  return (
    <AtSwipeAction
      autoClose={autoClose}
      isOpened={isOpened}
      disabled={disabled}
      options={options}
      onOpened={onOpened}
      onClosed={onClosed}
      onClick={onClick}
      className={className}
    >
      {process.env.PLATFORM_ENV === 'weapp' ? (
        <View hoverClass={hoverClass} onLongPress={onTriggerEvent} onClick={onTriggerEvent}>
          {props.children}
        </View>
      ) : (
        <View hoverClass={hoverClass} onLongTap={onTriggerEvent} onClick={onTriggerEvent}>
          {props.children}
        </View>
      )}
    </AtSwipeAction>
  );
};

KbSwipeAction.options = {
  addGlobalClass: true,
};

KbSwipeAction.defaultProps = {
  className: '',
  title: '',
  disabled: false,
  autoClose: true,
  isOpened: false,
  hoverClass: 'kb-hover',
  options: [],
  longTapOptions: null,
  onOpened: () => {},
  onClosed: () => {},
  onClick: () => {},
};

export default KbSwipeAction;
