/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 引导绑定手机号
 */

import KbLoginAuth from '@base/components/login/auth';
import { useUpdate } from '@base/hooks/page';
import request from '@base/utils/request';
import { getPage, reportAnalytics, scanParse } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro, { useEffect, useState } from '@tarojs/taro';
import { AtAvatar, AtFloatLayout } from 'taro-ui';
import { useSelector } from '@tarojs/redux';
import { initGzhStatus, useGetFollowGZHStatus } from '@/components/_pages/official-account/_utils';
import './authPhone.scss';

const Index = () => {
  const [isOpened, setIsOpened] = useState(false);
  const { followGZHStatus = initGzhStatus } = useSelector((state) => state.global);
  const { unlockGetGZH } = useGetFollowGZHStatus();

  const isSourceBD = () => {
    const query = Taro.launchParams || {};
    const { query: query2 } = scanParse(Taro.launchParams) || {};
    const params = { ...query, ...query2 };
    const { source = '' } = params;
    return source && /^bd-/.test(source) ? source : false;
  };

  const verifyLoginAndBindMobile = (openid) => {
    return new Promise((resolve) => {
      request({
        url:
          process.env.MODE_ENV === 'wkd'
            ? '/v1/WeApp/verifyLoginAndBindMobile'
            : '/api/weixin/mini/login/verifyLoginAndBindMobile',
        data:
          process.env.MODE_ENV === 'wkd'
            ? {}
            : {
                open_id: openid,
                platform: 'xyt',
              },
        mastHasMobile: false,
        onThen: (res) => {
          if (res.code == 0 && res.data.is_show) {
            resolve(true);
          } else {
            resolve(false);
          }
        },
      });
    });
  };

  useUpdate((loginData) => {
    const { logined, userInfo } = loginData || {};
    const { mobile, openid } = userInfo || {};
    if (Taro._authPhoneLock) {
      // 已弹出，锁定
      return;
    }
    if (logined && !mobile) {
      const { path = '' } = getPage().$router || {};
      const enablePaths = [];
      // if (process.env.MODE_ENV === 'wkd') {
      //   enablePaths.push('pages-0/pages/query/detail/target/index');
      // }

      // 仅在查件结果页、订单详情页、寄件结果页保留
      const isBd = isSourceBD();
      const isEnable = enablePaths.includes(`${path}`.replace(/^\//, ''));

      if (isBd || isEnable) {
        verifyLoginAndBindMobile(openid).then((isShow) => {
          if (isShow || isBd) {
            reportAnalytics({
              key: 'bind_mobile_phone',
              source: `展示推荐绑定手机号组件`,
            });
            setIsOpened(true);
          }
        });
      }
    }
  });

  const guideFollowGZH = () => {
    // 引导关注公众号：优先关注微快递公众号，其次快宝驿站公众号
    const guide = (url, name) => {
      Taro.previewImage({
        urls: [url],
      });
      unlockGetGZH();
      reportAnalytics({
        key: 'bind_mobile_phone',
        source: `展示推荐公众号-${name}`,
      });
    };
    if (!followGZHStatus.wkd) {
      guide('https://cdn-img.kuaidihelp.com/wkd/miniApp/gzh/wkd_huiyuan.png?t=20230519', '微快递');
    } else if (!followGZHStatus.yz) {
      guide('https://cdn-img.kuaidihelp.com/wkd/miniApp/gzh/yz.png', '快宝驿站');
    }
  };

  const handleAuthComplete = () => {
    const sourceBD = isSourceBD();
    if (sourceBD) {
      reportAnalytics({
        key: 'bind_mobile_phone',
        source: sourceBD,
      });
    }
    Taro.kbToast({
      text: '授权绑定成功!',
    });
    setIsOpened(false);
    if (process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV == 'weapp') {
      guideFollowGZH();
    }
  };

  const handleCancel = () => {
    setIsOpened(false);
  };

  useEffect(() => {
    if (isOpened) {
      // 锁定弹窗，防止其他页面持续弹出
      Taro._authPhoneLock = true;
    }
  }, [isOpened]);

  return (
    <AtFloatLayout isOpened={isOpened} onClose={handleCancel}>
      <View className='kb-auth-phone'>
        <View className='kb-auth-phone-title'>
          <AtAvatar
            image={
              process.env.MODE_ENV === 'wkd'
                ? 'https://cdn-img.kuaidihelp.com/wkd/miniApp/login_wkd.png'
                : 'https://cdn-img.kuaidihelp.com/yz/logo.png'
            }
            size='small'
            circle
          />
          <View className='txt'>
            尊敬的{process.env.MODE_ENV === 'wkd' ? '微快递' : '快宝驿站'}用户：
          </View>
        </View>
        <View className='kb-auth-phone-content'>
          为了给您提供更好的寄件服务体验，授权手机号+关注公众号可为您提供全链路的物流提醒
        </View>
        <View className='kb-auth-phone-opt'>
          <View className='kb-auth-phone-opt--auth'>
            <KbLoginAuth
              useOpenType
              text='立即授权'
              scope='phoneNumber'
              showIcon={false}
              onAuthComplete={handleAuthComplete}
            />
          </View>
          <View className='kb-auth-phone-opt--cancel' onClick={handleCancel}>
            暂不授权
          </View>
        </View>
      </View>
    </AtFloatLayout>
  );
};

export default Index;
