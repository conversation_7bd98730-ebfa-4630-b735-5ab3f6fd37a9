/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbEmpty from '@base/components/empty';
import KbLoader from '@base/components/loader';
import { extendMemo } from '@base/components/_utils';
import { useUpdate } from '@base/hooks/page';
import { debounce, noop } from '@base/utils/utils';
import { ScrollView, Text, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { Fragment, useCallback, useEffect, useRef, useScope } from '@tarojs/taro';
import classNames from 'classnames';
import isBoolean from 'lodash/isBoolean';
import isEqual from 'lodash/isEqual';
import Create from './create';
import './index.scss';

const Index = (props) => {
  const $scope = useScope();
  const actionRef = useRef({
    scope: null,
    ins: null,
  });

  const { data, name, active } = props;
  const { loginData = Taro.kbLoginData || {} } = useSelector((state) => state.global);
  let mastHasMobile = void 0;
  const { logined } = loginData;
  let enabled = logined;
  let mastLogin = true;
  let { enableRefresh, enableMore } = props;
  const loadingDefault = !!(enabled && active);
  const { api } = data || {};
  if (!api) {
    enableRefresh = false;
    enableMore = false;
  } else {
    const { mastLogin: $mastLogin = true } = api;
    mastLogin = $mastLogin;
    if (!mastLogin) {
      enabled = true;
    }
    if (process.env.MODE_ENV === 'yz') {
      mastHasMobile = isBoolean(api.mastHasMobile) ? api.mastHasMobile : true;
    }
  }

  // 缓存是否允许刷新与加载更多的状态
  actionRef.current.loaderStatus = { enableMore, enableRefresh };

  // 加载
  const triggerLoader = (data) => {
    if (!actionRef.current.ins) return;
    actionRef.current.ins.loader(data).catch((err) => console.log(err));
  };

  // 滚动触发
  const handleScroll = (e) => {
    props.onScroll(e);
    if (e.type.toLocaleLowerCase() === 'scrolltolower') {
      props.onReachBottom();
      if (enableMore === false) return;
    } else {
      if (enableRefresh === false) return;
    }
    actionRef.current.scope && actionRef.current.scope.onScroll(e);
  };

  const handleTouch = (e) => {
    props.onTouch(e);
    if (enableRefresh === false) return;
    actionRef.current.scope && actionRef.current.scope.onTouch(e);
  };

  // 实例化Create
  const triggerCreateIns = (opts = { active, data, enabled }) => {
    if ($scope && $scope.$component) {
      const { active, data, enabled } = opts;
      if (!actionRef.current.ins) {
        // 初始化
        const currentActive = (enabled && active) || false;
        actionRef.current.active = currentActive;
        actionRef.current.scope = $scope.$component;
        actionRef.current.ins = new Create(
          {
            ...data,
            name,
            active: currentActive,
            checkRefreshAndMore: () => actionRef.current.loaderStatus,
          },
          actionRef.current.scope,
        );
        props.onReady(actionRef.current.ins);
      }
    }
  };

  if (process.env.PLATFORM_ENV === 'swan') {
    // 百度小程序初始会按照 defaultProps 执行导致处理逻辑异常
    /* eslint-disable */
    const triggerCreateInsDebounce = useCallback(
      debounce(triggerCreateIns, 150, { trailing: true }),
      [],
    );
    useEffect(() => {
      triggerCreateInsDebounce({ data, active, enabled });
    }, [data, active, enabled]);
    /* eslint-enable */
  } else {
    triggerCreateIns();
  }

  useEffect(() => {
    return () => {
      const { requestTask } = actionRef.current.ins || {};
      if (requestTask && requestTask.abort) {
        requestTask.abort();
      }
    };
  }, []);

  useUpdate(
    ({ logined }, reLogin) => {
      if (logined) {
        // active变更，或者重新登录；
        if (active && (!isEqual(active, actionRef.current.active) || reLogin)) {
          triggerLoader(active);
        }
        actionRef.current.active = active;
      }
    },
    [active],
  );

  // 获取state数据
  const getState = (name) => {
    const { state = {} } = actionRef.current.scope || {};
    return state[name] || {};
  };

  const {
    topSpaceFix,
    scrollY,
    emptyImage,
    noScrollview,
    height,
    className,
    noDataText,
    useRenderEmpty,
    scrollIntoView,
    scrollTop,
    scrollWithAnimation,
    noMoreText,
    size,
    theme,
    useRenderBottom,
  } = props;

  const {
    dataReady = false,
    status,
    loading = loadingDefault,
    eventType,
    errMessage = '',
  } = getState(name);

  const refreshStatus =
    eventType === 'refreshReady' ? 'ready' : eventType === 'refreshByUpper' ? status : 'hidden';

  const moreStatus =
    eventType === 'more' && status === 'loading'
      ? 'loading'
      : status === 'noMore'
      ? status
      : 'more';

  const refresherShow = ['loading', 'ready'].includes(refreshStatus);

  const refreshIng = refresherShow && (!loading || dataReady);

  const rootCls = classNames('kb-longlist', `kb-longlist__height--${height}`, className);
  const isShow = enableRefresh && refreshIng;
  const isHidden = enableRefresh && !refreshIng;
  const scrollCls = classNames(
    'kb-longlist-scroll',
    'kb-longlist-refresher__wrapper', // 避免切换是否可刷新出现的抖动，调整刷新loading的展示规则
    {
      'kb-longlist-refresher__top-space-fix': topSpaceFix && isShow,
      'kb-longlist-refresher__show': isShow,
      'kb-longlist-refresher__hidden': isHidden,
    },
  );
  const refreshRootCls = classNames('kb-longlist-refresher', {
    [`kb-longlist-refresher__theme-${theme}`]: !!theme,
  });
  const refreshTextCls = classNames('kb-longlist-refresher__text', {
    'kb-longlist-refresher__text--up': refreshStatus === 'ready',
  });
  const ListContent = (
    <Fragment>
      <View className={refreshRootCls}>
        <View className='kb-longlist-refresher__content'>
          {refreshStatus === 'loading' ? (
            <KbLoader
              centered
              status='loading'
              loadingText='正在刷新'
              size='small'
              color={theme === 'brand' ? 'white' : 'brand'}
            />
          ) : (
            <View className={refreshTextCls}>
              <Text className='kb-longlist-refresher__text--tips'>
                {refreshStatus === 'ready' ? '松开' : '下拉'}
                刷新
              </Text>
            </View>
          )}
        </View>
      </View>
      <View className='kb-longlist-content' hidden={!dataReady}>
        {props.children}
        {enableMore && (
          <KbLoader
            noMoreText={noMoreText}
            loadingText='正在加载'
            status={moreStatus}
            size='small'
          />
        )}
      </View>
      <View hidden={dataReady}>
        {loading ? (
          <KbLoader centered className='kb-longlist-loader' />
        ) : useRenderEmpty && enabled ? (
          <Fragment>{props.renderEmpty}</Fragment>
        ) : (
          <KbEmpty
            mastLogin={mastLogin}
            image={emptyImage}
            mastHasMobile={mastHasMobile}
            centered
            description={errMessage || noDataText}
            renderAd={<View>{props.renderEmptyAd}</View>}
            className='kb-longlist-empty'
            size={size}
          >
            <View>{props.renderEmptyFooter}</View>
          </KbEmpty>
        )}
        {useRenderBottom && props.renderBottom}
      </View>
    </Fragment>
  );

  return (
    <View className={rootCls}>
      <View
        className={scrollCls}
        onTouchStart={handleTouch}
        onTouchMove={handleTouch}
        onTouchEnd={handleTouch}
        onTouchCancel={handleTouch}
      >
        <ScrollView
          trapScroll
          upperThreshold={0}
          className='kb-longlist-scroll__view'
          scrollAnimationDuration={300}
          id={`scrollView_${name}`}
          scrollTop={scrollTop}
          scrollWithAnimation={scrollWithAnimation !== false}
          scrollIntoView={scrollIntoView}
          scrollY={scrollY && !noScrollview}
          onScrollToUpper={handleScroll}
          onScrollToLower={handleScroll}
          onScroll={handleScroll}
        >
          {ListContent}
        </ScrollView>
      </View>
    </View>
  );
};

Index.defaultProps = {
  topSpaceFix: false, // 头部修正，头部有20rpx间隔，向上修正-20rpx
  scrollY: true, // 是否允许滚动
  noScrollview: false, // 是否包装在scorllview中
  active: true, // 可由父组件控制激活数据更新
  name: 'data', // 配置数据名称
  className: '',
  data: {},
  enableRefresh: true, // 控制是否允许刷新
  enableMore: false, // 控制是否允许加载更多
  scrollWithAnimation: true, // 滚动条动效
  useRenderEmpty: false, // 自定义的空数据展示内容
  noDataText: '暂无数据', // 空数据描述文案
  noMoreText: '没有更多了', // 无更多数据页面底部展示文案
  scrollIntoView: '', // 滚动到指定位置
  scrollTop: process.env.PLATFORM_ENV === 'swan' ? '' : 0, // 滚动到指定位置
  height: 'fixed', // 固定模式 100%，auto模式根据内容自动撑开
  size: 'normal', // empty组件尺寸
  theme: 'light', // 下拉展示风格 light|brand
  onReady: noop,
  onScroll: noop,
  onTouch: noop,
  onReachBottom: noop,
  useRenderBottom: false, // 使用自定义底部
};

Index.options = {
  addGlobalClass: true,
};

export default extendMemo(Index, ['active', 'data', 'scrollIntoView', 'scrollTop']);
