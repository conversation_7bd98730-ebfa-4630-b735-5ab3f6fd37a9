/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState, useCallback } from '@tarojs/taro';
import request from '@base/utils/request';
import { useUpdate } from '@base/hooks/page';
import isEmpty from 'lodash/isEmpty';

export function useAgreementFn(props) {
  const { agreeType, updateData, agreeModal } = props;

  const [modalVisible, setModalVisible] = useState(false);
  const [agreeInfo, setAgreeInfo] = useState({});
  const { mark, title } = agreeInfo;

  const getAgreement = (loading = false) => {
    return new Promise((resolve) => {
      request({
        toastSuccess: false,
        toastLoading: loading,
        toastError: true,
        data: {
          agreementType: agreeType,
          rendering: 1,
        },
        url: '/api/weixin/mini/user/Agreement/getAgreement',
      }).then((res) => {
        const { code, data = {} } = res;
        // data.isSigned = '0';
        if (code == 0) {
          setAgreeInfo(data);
          updateData({
            agree: data.isSigned == '1',
          });
          resolve(data);
        } else {
          resolve({});
        }
      });
    });
  };

  const signAgreement = useCallback(() => {
    request({
      toastSuccess: false,
      toastLoading: false,
      toastError: false,
      directTriggerThen: true,
      url: '/api/weixin/mini/user/Agreement/signAgreement',
      data: {
        mark,
        agreementType: agreeType,
      },
    }).then(() => {
      setModalVisible(false);
      setAgreeInfo((val) => ({
        ...val,
        isSigned: '1',
      }));
      updateData({
        agree: true,
      });
    });
  }, [mark, agreeType]);

  // 检查获取状态，如未获取手动获取协议
  const checkedAgreeInfo = async () => {
    return new Promise(async (resolve, reject) => {
      if (title) {
        resolve(agreeInfo);
      } else {
        const info = await getAgreement(true);
        if (!isEmpty(info)) {
          resolve(info);
        } else {
          reject();
        }
      }
    });
  };

  // 触发弹窗
  useEffect(() => {
    if (agreeModal) {
      checkedAgreeInfo().then(({ isSigned }) => {
        if (isSigned != '1') {
          setModalVisible(true);
        }
      });
    }
  }, [agreeModal]);

  // 监听登陆获取协议
  useUpdate(({ logined }) => {
    if (logined) {
      getAgreement();
    }
  });

  const changeChecked = async (event) => {
    const { isSigned } = await checkedAgreeInfo();
    if (event && isSigned != '1') {
      setModalVisible(true);
    } else {
      updateData({
        agree: event,
      });
    }
  };

  const handleShowAgreement = async () => {
    const { viewUrl } = await checkedAgreeInfo();
    Taro.navigator({
      url: viewUrl,
      target: 'webview',
      force: true,
    });
  };

  const handleMzAgreement = () => Taro.navigateToDocument(10);

  // 阻止冒泡
  const onCatch = (e) => {
    e.stopPropagation();
  };
  const closeModal = () => setModalVisible(false);

  return {
    agreeInfo,
    modalVisible,
    changeChecked,
    handleShowAgreement,
    handleMzAgreement,
    onCatch,
    closeModal,
    signAgreement,
  };
}
