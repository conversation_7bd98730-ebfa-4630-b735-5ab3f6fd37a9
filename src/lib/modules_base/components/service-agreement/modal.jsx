/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import { View, ScrollView } from '@tarojs/components';
import { AtButton, AtFloatLayout } from 'taro-ui';
import KbRichText from '@base/components/rich-text';

const AgreementModal = (props) => {
  const { modalVisible, title, content, signAgreement, closeModal } = props;

  const formatContent = useMemo(() => {
    if (content) {
      const str = content
        .replace(/\s*/g, '')
        .match(/<body><divclass="kb-article">(\S*)<\/div><\/body>/)[1]
        .replace(/pclass*/g, 'p class');
      return `<div class="kb-article">${str}</div>`;
    }
    return '';
  }, [content]);

  const handleHtmlReady = () => {};

  return (
    <AtFloatLayout isOpened={modalVisible} title={title} onClose={closeModal} scrollY={false}>
      <ScrollView scrollY style={{ height: '300px' }}>
        <KbRichText content={formatContent} onReady={handleHtmlReady} />
      </ScrollView>
      <View className='kb-spacing-md'>
        <AtButton type='primary' onClick={signAgreement} size='small'>
          同意协议内容，下次不再提醒
        </AtButton>
      </View>
    </AtFloatLayout>
  );
};

AgreementModal.options = {
  addGlobalClass: true,
};

export default AgreementModal;
