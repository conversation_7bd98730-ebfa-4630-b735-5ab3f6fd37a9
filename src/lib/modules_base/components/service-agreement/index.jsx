/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import KbCheckbox from '@base/components/checkbox';
import { View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import { useAgreementFn } from './utils';
import AgreementModal from './modal';
import './index.scss';

const ServiceAgreement = (props) => {
  const { agree, style } = props;
  const {
    agreeInfo,
    modalVisible,
    changeChecked,
    handleShowAgreement,
    handleMzAgreement,
    onCatch,
    closeModal,
    signAgreement,
  } = useAgreementFn(props);
  const { title, content } = agreeInfo || {};

  return (
    <View>
      <View className='at-row at-row__align--center'>
        <KbCheckbox
          label='我已阅读并同意'
          checked={agree}
          onChange={changeChecked}
          className='kb-color__black'
        />
        <View onClick={onCatch} hoverStopPropagation className='kb-agreement'>
          <AtButton className='kb-button__link' size='small' onClick={handleShowAgreement}>
            <View style={style}>{title ? `《${title}》` : '《 服务协议 》'}</View>
          </AtButton>
          <AtButton className='kb-button__link' size='small' onClick={handleMzAgreement}>
            <View style={style}>《免责声明》</View>
          </AtButton>
        </View>
      </View>
      {content && (
        <AgreementModal
          modalVisible={modalVisible}
          title={title}
          content={content}
          closeModal={closeModal}
          signAgreement={signAgreement}
        />
      )}
    </View>
  );
};

ServiceAgreement.options = {
  addGlobalClass: true,
};

export default ServiceAgreement;
