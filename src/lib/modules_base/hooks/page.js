/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getPage, getSessionIDFromLoginData } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import { useDidHide, useDidShow, useEffect, useRef, useScope } from '@tarojs/taro';
import isFunction from 'lodash/isFunction';

/**
 *
 * @description 函数式页面利用该方法绑定onPostMessage方法，主要用于页面组件
 * @param {*} callback onPostMessage
 * @param {*} deps 依赖项
 */
export const usePostMessage = (callback) => {
  const $scope = useScope();
  if (!$scope || !$scope.$component || $scope.$component.onPostMessage) return;
  $scope.$component.onPostMessage = callback;
};

/**
 *
 * @description 登录状态更新，函数组件监听登录状态更新
 */
export const useUpdate = (callback, deps = []) => {
  const { loginData } = useSelector((state) => state.global);
  const actionRef = useRef({});
  const sessionID = loginData ? getSessionIDFromLoginData(loginData) : void 0;
  useEffect(() => {
    if (typeof sessionID === 'undefined') return;
    const { sessionID: preSessionId } = actionRef.current;
    callback(loginData, sessionID !== preSessionId);
    actionRef.current = { sessionID };
  }, [sessionID, ...deps]);
};

/**
 *
 * @description 修正支付宝与百度，自定义组件不支持useDidShow的问题；
 * @param {*} status
 * @param {*} callback_
 */
function useFixPageLife(status, callback_) {
  const $scope = useScope();
  let callback = callback_;

  const triggerCallback = () => {
    if (callback) {
      callback();
    }
  };

  useEffect(() => {
    let { $page } = $scope;
    if (process.env.PLATFORM_ENV === 'swan') {
      $page = $scope.pageinstance.$component.$scope;
    }
    if (status === 'show') {
      triggerCallback();
      const { onShow: onShowPage } = $page || {};
      if (onShowPage) {
        $page.onShow = (...arg) => {
          onShowPage.apply($page, arg);
          triggerCallback();
        };
      }
    } else {
      const { onHide: onHidePage } = $page || {};
      if (onHidePage) {
        $page.onHide = (...arg) => {
          onHidePage.apply($page, arg);
          triggerCallback();
        };
      }
    }
    return () => {
      callback = null;
    };
  }, []);
}

/**
 * @description 组件展示，兼容支付宝，主要用于自定义，页面组件请使用 useDidShow
 */
export const useDidShowCom = (callback) => {
  /* eslint-disable */
  if (process.env.PLATFORM_ENV === 'weapp') {
    useDidShow(callback);
  } else {
    useFixPageLife('show', callback);
  }
  /* eslint-enable */
};

/**
 * @description 组件隐藏，兼容支付宝，主要用于自定义，页面组件请使用 useDidHide
 */
export const useDidHideCom = (callback) => {
  /* eslint-disable */
  if (process.env.PLATFORM_ENV === 'weapp') {
    useDidHide(callback);
  } else {
    useFixPageLife('hide', callback);
  }
  /* eslint-enable */
};

export const useOnUnload = (callback) => {
  const originalOnUnload = useRef(null);

  useEffect(() => {
    const page = getPage();
    const $scope = page.$scope || {};

    if (originalOnUnload.current) {
      return;
    }
    originalOnUnload.current = $scope.onUnload;

    $scope.onUnload = () => {
      if (isFunction(originalOnUnload.current)) {
        originalOnUnload.current();
      }
      callback();
    };

    return () => {
      if ($scope && originalOnUnload.current) {
        $scope.onUnload = originalOnUnload.current;
        originalOnUnload.current = null;
      }
    };
  }, []);
};
