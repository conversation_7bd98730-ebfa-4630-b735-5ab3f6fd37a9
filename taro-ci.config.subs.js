/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 疫情分包
const yiqing = {
  chunks: 'components/_pages/yiqing',
  chunksPages: ['pages/user/yiqing/index', 'components/_pages/yiqing/code/index'],
};

// 停发区分包
const sub3 = {
  'pages-3': {
    chunks: ['components/_pages/closedArea'],
    chunksPages: (pagesNames) => {
      const ls = [
        ...pagesNames.filter((pathname) => pathname.startsWith('pages/closedArea/')),
        'components/_pages/closedArea/area/index',
      ];
      if (process.env.MODE_ENV === 'wkd') {
        ls.push(...yiqing.chunksPages);
      }
      return ls;
    },
  },
};
if (process.env.MODE_ENV === 'wkd') {
  sub3['pages-3'].chunks.push(yiqing.chunks);
}

const sub1 = {
  'pages-1': {
    chunks: 'components/_pages/delivery',
    chunksPages: [
      'pages/order/delivery/index',
      'pages/order/delivery/append/index',
      'pages/order/delivery/goods/index',
      'pages/order/delivery/address/index',
      'pages/order/delivery/address/edit/index',
      'pages/order/delivery/address/choose/index',
      // 组件
      'components/_pages/delivery/address-info/index',
      'components/_pages/delivery/estimated-fee/index',
    ],
  },
};

const sub2 = {
  'pages-2': {
    chunks: ['components/_pages/order/qj-detail'],
    chunksPages: [
      'pages/order/qj-detail/index',
      // 组件
      'components/_pages/order/qj-detail/address-info',
      'components/_pages/order/qj-detail/goods-info',
      'components/_pages/order/qj-detail/order-status',
      'components/_pages/order/qj-detail/pay-info',
    ],
  },
};

// eslint-disable-next-line
module.exports = {
  sub1,
  sub2,
  sub3,
};
