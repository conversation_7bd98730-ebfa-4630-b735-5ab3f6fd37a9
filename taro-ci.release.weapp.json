["2.4.4-存柜优化，新增自定义手机号码", "2.4.3-全部亮灯", "2.4.2-自助出库", "2.4.1-优化柜机交互提示", "2.4.0-快宝驿站7.5版本", "2.3.9-快宝驿站 跑腿代取业务改版", "2.3.8-快宝驿站7.4版本", "2.3.7-wss签名", "2.3.6-快递柜取消订单优化", "2.3.5-取件灯条展示", "2.3.4-小程序快递柜取件", "2.3.3-配合APP7.0需求", "2.3.2-展示完整取件码", "2.3.1-支持好印莱引导跳转", "2.3.0-新增支持下单时输入及导入公司名称并打印、导入发件人及多备注，优化登录页及绑定手机号交互；修复支付页报价单参数问题", "2.2.31-下单页实名制弹窗引导", "2.2.30-处理无效广告接口调用问题;修复订单列表报错问题", "2.2.29-增加快递柜扫码预约寄件，疑似包裹显示取件码", "2.2.28-增加扫码优惠券支付逻辑", "2.2.27-物流信息页面显示疑似包裹取件码", "2.2.26-小程序手机号码获取控件的场景优化", "2.2.25-疑似包裹显示", "2.2.24-支付补充order_random；协议勾选默认为false", "2.2.23-意见反馈；处理微信混淆行为", "2.2.22-包裹图片查看权限调整;身份码展示手机号;散客下单拦截;开门取件提示弹窗;驿站营业状态;", "2.2.21-修复下单品牌为一个时，无法选择品牌类型问题", "2.2.20-增加部分接口签名", "2.2.15-订单详情开放快递柜订单支付入口;", "2.2.14-新增实名管理入口;", "2.2.13-监听登陆状态获取服务协议", "2.2.12-优惠券增加适用品牌;", "2.2.11-下单页服务协议需手动签署", "2.2.10-优化敏感信息接口请求签名机制;", "2.2.0-增加快递柜寄件;", "2.1.5-增加拉黑驿站功能", "2.1.4-更换签名库", "2.1.3-订单详情脱敏；分享的订单列表脱敏", "2.1.2-调整授权手机号弹窗规则，保留链接带有bd参数时的弹窗逻辑", "2.1.1-调整授权手机号弹窗规则，仅在订单详情、物流信息、优寄下单成功页生效", "2.1.0-请求接入新签名规则", "2.0.36-因违规屏蔽引导绑定手机号弹窗", "2.0.35-扫码开启无人驿站闸机", "2.0.34-增加c端绑定手机号同步逻辑", "2.0.33-增加绑定手机号授权弹窗", "2.0.32-绑定手机号上报", "2.0.31-修改半屏打开微快递参数", "2.0.30-新增微快递半屏访问入口", "2.0.29-停发区查询库木块", "2.0.28-停发区查询;实名支持座机号", "2.0.27-地址自动识别并粘贴", "2.0.26-预约取件广告位调整", "2.0.25-更改用户信息授权交互", "2.0.24-身份条形码优化", "2.0.23-下单页实名展示优化;品牌类型点击优化;文案优化", "2.0.22-增加区间报价处理;", "2.0.21-支持电商虚拟号下单", "2.0.20-智能识别兼容固话", "2.0.19-修复实名问题", "2.0.18-请求更新ts+set<PERSON><PERSON>ie", "2.0.17-修复操作记录页面接口参数问题", "2.0.16-优化预约取件跑腿页面", "2.0.15-修复跑腿文案错误", "2.0.14-修复驿站关闭跑腿时页面显示问题;", "2.0.13-修复预约取件备注输入框问题", "2.0.12-增加WE分析", "2.0.12-修改防疫宣传页面标题;", "2.0.11-增加We分析;去掉防疫宣传页面顶部显示", "2.0.10-兼容微信关注公众号组件每个页面只能配置一个限制", "2.0.8-修复弹窗问题", "2.0.7-关注公众号去掉分区域逻辑", "2.0.6-预约取件备注功能,送货上门在线支付功能,新增防疫宣传页面", "2.0.5-驿站兼容扫码寄新规则;", "2.0.4-公众号功能改为多地区", "2.0.3-粘贴板地址功能改为手动触发", "2.0.0-同步微快递2.0内容", "1.9.2-自建订单功能", "1.8.6-修复扫码获取内容的问题", "1.8.5-api域名切换", "1.8.4-解决申请加盟和帮助文档不能跳转的问题", "1.8.1-1月份优化内容:批量填写地址提供增值服务填写,获取运单号支持批量获取,批量导入功能支持物品类型，备注，增值服务-代收到付的导入...", "1.8.1-12月份优化内容", "1.8.0-积分使用功能修改、扫码取件、解除绑定", "1.5.1-寄件地址优化，样式以及输入优化", "1.5.2-替换投诉模版id", "1.5.3-扫下单码逻辑", "1.7.1-支付时增加选择权益次卡切换权益次卡功能，寄件逻辑优化，订单详情优,操作记录页面，底单分享功能", "1.7.1-扫码下单问题的修复", "1.7.1-操作记录页面优化，历史订单问题，修改订单时的品牌问题，微信扫查件码问题", "1.7.2-驿站主页打赏功能，运单号复制功能按钮", "1.7.3-解决广告问题，大屏取件功能", "1.7.4-更新手机号时更新包裹"]